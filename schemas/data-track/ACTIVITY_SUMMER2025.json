{"$defs": {"CLICK_EVENT__OP_ACTIVITY_SUM2025_MAIN__OP_ACTIVITY_MAKE_TEAM_CARD": {"type": "object", "properties": {"action": {"const": "OP_ACTIVITY_MAKE_TEAM_CARD", "description": "社交组队卡片"}, "params": {"type": "object", "required": [], "properties": {"friend_id": {"type": "string", "description": "好友id；"}, "click_area": {"type": "string", "description": "区分点击的区域", "enum": ["change", "invite", "close"]}}}}, "required": ["action", "params"]}, "PAGE_SHOW_EVENT__OP_ACTIVITY_SUM2025_MAIN": {"type": "object", "properties": {"page": {"const": "OP_ACTIVITY_SUM2025_MAIN", "description": "2025暑期活动主会场页面"}, "params": {"type": "object", "required": [], "properties": {"activity_name": {"type": "string", "description": "活动名称，本次为SUMMER2025", "enum": ["SUMMER2025"]}, "url": {"type": "string", "description": "页面链接"}, "entry_src": {"type": "string", "description": "区分活动页来源", "enum": ["ks_2025sum_001", "ks_2025sum_002", "ks_2025sum_003", "ks_2025sum_004", "ks_2025sum_005", "ks_2025sum_006", "ks_2025sum_007", "ks_2025sum_008", "ks_2025sum_009", "ks_2025sum_010", "ks_2025sum_011", "ks_2025sum_012", "ks_2025sum_013", "ks_2025sum_014", "ks_2025sum_015", "ks_2025sum_016", "ks_2025sum_017", "ks_2025sum_018", "ks_2025sum_019", "ks_2025sum_020", "ks_2025sum_021", "ks_2025sum_022", "ks_2025sum_023", "ks_2025sum_024", "ks_2025sum_025", "ks_2025sum_026", "ks_2025sum_027", "ks_2025sum_028", "ks_2025sum_029", "ks_2025sum_030", "ks_2025sum_031", "ks_2025sum_032", "ks_2025sum_033", "ks_2025sum_034", "ks_2025sum_035", "ks_2025sum_036", "ks_2025sum_037", "ks_2025sum_038", "ks_2025sum_039", "ks_2025sum_040", "ks_2025sum_041", "ks_2025sum_042", "ks_2025sum_043", "ks_2025sum_044", "ks_2025sum_045", "ks_2025sum_046", "ks_2025sum_047", "ks_2025sum_048", "ks_2025sum_049", "ks_2025sum_050", "ks_2025sum_051", "ks_2025sum_052", "ks_2025sum_053", "ks_2025sum_054"]}, "status": {"type": "string", "description": "状态", "enum": ["1", "0", "-1"]}}}}, "required": ["page", "params"]}, "ELEMENT_SHOW_EVENT__OP_ACTIVITY_SUM2025_MAIN__OP_ACTIVITY_MAKE_TEAM_CARD": {"type": "object", "properties": {"action": {"const": "OP_ACTIVITY_MAKE_TEAM_CARD", "description": "社交组队卡片"}, "params": {"type": "object", "required": [], "properties": {"friend_id": {"type": "string", "description": "好友id"}}}}, "required": ["action", "params"]}, "ELEMENT_SHOW_EVENT__OP_ACTIVITY_SUM2025_MAIN__OP_ACTIVITY_MAKE_TEAM_ICON": {"type": "object", "properties": {"action": {"const": "OP_ACTIVITY_MAKE_TEAM_ICON", "description": "组队格子icon"}, "params": {}}, "required": ["action", "params"]}, "CLICK_EVENT__OP_ACTIVITY_SUM2025_MAIN__OP_ACTIVITY_MAKE_TEAM_GRID_POP": {"type": "object", "properties": {"action": {"const": "OP_ACTIVITY_MAKE_TEAM_GRID_POP", "description": "格子上的组队任务弹窗"}, "params": {"type": "object", "required": [], "properties": {"click_area": {"type": "string", "description": "点击的区域", "enum": ["invite", "close"]}}}}, "required": ["action", "params"]}, "ELEMENT_SHOW_EVENT__OP_ACTIVITY_SUM2025_MAIN__OP_ACTIVITY_MAKE_TEAM_GRID_POP": {"type": "object", "properties": {"action": {"const": "OP_ACTIVITY_MAKE_TEAM_GRID_POP", "description": "格子上的组队任务弹窗"}, "params": {}}, "required": ["action", "params"]}, "CLICK_EVENT__OP_ACTIVITY_SUM2025_MAIN__OP_ACTIVITY_MAKE_TEAM_POP": {"type": "object", "properties": {"action": {"const": "OP_ACTIVITY_MAKE_TEAM_POP", "description": "邀请组队弹窗"}, "params": {"type": "object", "required": [], "properties": {"team_status": {"type": "string", "description": "目前组队状态", "enum": ["1", "2", "3"]}, "click_area": {"type": "string", "description": "区分图中点击的位置，主button上报文案，其他位置上报如下枚举值，如立即邀请", "enum": ["invite+", "head", "change", "exit"]}}}}, "required": ["action", "params"]}, "ELEMENT_SHOW_EVENT__OP_ACTIVITY_SUM2025_MAIN__OP_ACTIVITY_MAKE_TEAM_POP": {"type": "object", "properties": {"action": {"const": "OP_ACTIVITY_MAKE_TEAM_POP", "description": "邀请组队弹窗"}, "params": {"type": "object", "required": [], "properties": {"team_status": {"type": "string", "description": "目前组队状态", "enum": ["1", "2", "3"]}}}}, "required": ["action", "params"]}, "CLICK_EVENT__OP_ACTIVITY_SUM2025_MAIN__OP_ACTIVITY_MAKE_TEAM_GUID": {"type": "object", "properties": {"action": {"const": "OP_ACTIVITY_MAKE_TEAM_GUID", "description": "组队引导蒙层"}, "params": {}}, "required": ["action", "params"]}, "ELEMENT_SHOW_EVENT__OP_ACTIVITY_SUM2025_MAIN__OP_ACTIVITY_MAKE_TEAM_GUID": {"type": "object", "properties": {"action": {"const": "OP_ACTIVITY_MAKE_TEAM_GUID", "description": "组队引导蒙层"}, "params": {}}, "required": ["action", "params"]}, "CLICK_EVENT__OP_ACTIVITY_SUM2025_MAIN__OP_ACTIVITY_REWARD_POP": {"type": "object", "properties": {"action": {"const": "OP_ACTIVITY_REWARD_POP", "description": "主会场奖励弹窗"}, "params": {"type": "object", "required": [], "properties": {"encourage_type": {"type": "string", "description": "奖励类型", "enum": ["UNKNOWN", "DEFAULT_BLESS", "DEFAULT_LLCN", "LUCK_RUSH_CHANCE", "LLCN", "LLCH", "COUPON", "LLWDW_CARD", "LLCH_TRANSFER_CARD", "PROFILE_PENDANT", "COMMON_ZT_TASK_LLCN", "COMMON_ZT_TASK_LLCH", "FOLLOW_LLREWD_LLCN_TASK", "FOLLOW_LLREWD_LLCH_TASK", "FOLLOW_LLREWD_BLESS_TASK", "FOLLOW_LLREWD_RUSH_TASK", "WATCH_LIVE_LLREWD_LLCH_TASK", "WATCH_LIVE_LLREWD_LLCN_TASK", "WATCH_LIVE_LLREWD_RUSH_TASK", "TIME_LIMITED_ASSIST_DOUBLE_LLCN_TASK", "TIME_LIMITED_ASSIST_DOUBLE_LLCH_TASK", "TIME_LIMITED_COUNT_LLCN_TASK", "TIME_LIMITED_COUNT_LLCH_TASK", "TIME_LIMITED_INVITE_LLCN_TASK", "TIME_LIMITED_INVITE_LLCH_TASK", "TIME_LIMITED_INVITE_REFLUX_LLCN_TASK", "TIME_LIMITED_INVITE_REFLUX_LLCH_TASK", "TIME_LIMITED_PK_TASK", "TEAM_TASK", "AD_VIDEO", "AD_PHOTO", "FISSION_GOAT_DIVERSATION", "PHYSICAL_PRODUCT", "AD_STATIC_DIVERSION_POPUP", "ALWAYS_PULL_TASK", "INVOKE_APP_LLREWD_LLCN", "INVOKE_APP_LLREWD_LLCH", "INVOKE_APP_LLREWD_SHAKE"]}, "popup_type": {"type": "string", "description": "弹窗类型", "enum": ["TIME_ASSIST_TASK_SUCCESS", "TIME_ASSIST_RETURN_TASK_SUCCESS", "TIME_LIMIT_TASK_SUCCESS", "ASSIST_DOUBLE_LLCH_TASK_SUCCESS", "ASSIST_DOUBLE_LLCN_TASK_SUCCESS", " LS_LLCH_LLREWD", "LS_LLCN_LLREWD", "LS_COUPON_LLREWD", " LS_WITH_DRAW_CARD_LLREWD", "LS_LLCH_TRANSFER_CARD_LLREWD", "LS_PROFILE_PENDANT_LLREWD", " LS_DEFAULT_BLESS", " LS_DEFAULT_LLCN", "LS_AD_PHOTO", " LS_AD_VIDEO"]}, "brand_name": {"type": "string", "description": "品牌名称"}, "title": {"type": "string", "description": "弹窗标题"}, "button_name": {"type": "string", "description": "按钮文案"}, "coupon_id": {"type": "string", "description": "包含所有券的实例id"}, "photo_id": {"type": "string", "description": "商业化id，包含图片、视频等，实际上报为url区分"}, "task_id": {"type": "string", "description": "task_id:区分inpush和日历"}}}}, "required": ["action", "params"]}, "ELEMENT_SHOW_EVENT__OP_ACTIVITY_SUM2025_MAIN__OP_ACTIVITY_REWARD_POP": {"type": "object", "properties": {"action": {"const": "OP_ACTIVITY_REWARD_POP", "description": "主会场奖励弹窗"}, "params": {"type": "object", "required": [], "properties": {"encourage_type": {"type": "string", "description": "奖励类型", "enum": ["UNKNOWN", "DEFAULT_BLESS", "DEFAULT_LLCN", "LUCK_RUSH_CHANCE", "LLCN", "LLCH", "COUPON", "LLWDW_CARD", "LLCH_TRANSFER_CARD", "PROFILE_PENDANT", "COMMON_ZT_TASK_LLCN", "COMMON_ZT_TASK_LLCH", "FOLLOW_LLREWD_LLCN_TASK", "FOLLOW_LLREWD_LLCH_TASK", "FOLLOW_LLREWD_BLESS_TASK", "FOLLOW_LLREWD_RUSH_TASK", "WATCH_LIVE_LLREWD_LLCH_TASK", "WATCH_LIVE_LLREWD_LLCN_TASK", "WATCH_LIVE_LLREWD_RUSH_TASK", "TIME_LIMITED_ASSIST_DOUBLE_LLCN_TASK", "TIME_LIMITED_ASSIST_DOUBLE_LLCH_TASK", "TIME_LIMITED_COUNT_LLCN_TASK", "TIME_LIMITED_COUNT_LLCH_TASK", "TIME_LIMITED_INVITE_LLCN_TASK", "TIME_LIMITED_INVITE_LLCH_TASK", "TIME_LIMITED_INVITE_REFLUX_LLCN_TASK", "TIME_LIMITED_INVITE_REFLUX_LLCH_TASK", "TIME_LIMITED_PK_TASK", "TEAM_TASK", "AD_VIDEO", "AD_PHOTO", "FISSION_GOAT_DIVERSATION", "PHYSICAL_PRODUCT", "AD_STATIC_DIVERSION_POPUP", "ALWAYS_PULL_TASK", "INVOKE_APP_LLREWD_LLCN", "INVOKE_APP_LLREWD_LLCH", "INVOKE_APP_LLREWD_SHAKE"]}, "popup_type": {"type": "string", "description": "弹窗类型", "enum": ["TIME_ASSIST_TASK_SUCCESS", "TIME_ASSIST_RETURN_TASK_SUCCESS", "TIME_LIMIT_TASK_SUCCESS", "ASSIST_DOUBLE_LLCH_TASK_SUCCESS", "ASSIST_DOUBLE_LLCN_TASK_SUCCESS", " LS_LLCH_LLREWD", "LS_LLCN_LLREWD", "LS_COUPON_LLREWD", " LS_WITH_DRAW_CARD_LLREWD", "LS_LLCH_TRANSFER_CARD_LLREWD", "LS_PROFILE_PENDANT_LLREWD", " LS_DEFAULT_BLESS", " LS_DEFAULT_LLCN", "LS_AD_PHOTO", " LS_AD_VIDEO"]}, "brand_name": {"type": "string", "description": "品牌名称"}, "title": {"type": "string", "description": "弹窗标题"}, "button_name": {"type": "string", "description": "主按钮文案（默认高亮的button）"}, "coupon_id": {"type": "string", "description": "包含所有券的实例id"}, "photo_id": {"type": "string", "description": "商业化id，包含图片、视频等，实际上报为url区分"}}}}, "required": ["action", "params"]}, "CLICK_EVENT__OP_ACTIVITY_SUM2025_MAIN__OP_ACTIVITY_JION_BUTTON": {"type": "object", "properties": {"action": {"const": "OP_ACTIVITY_JION_BUTTON", "description": "主页面参与button"}, "params": {"type": "object", "required": [], "properties": {"title": {"type": "string", "description": "按钮文案，上报按钮的文案，如\"向前冲\"等"}, "steps": {"type": "string", "description": "剩余步数，上报进入页面按钮曝光时的剩余步数，0、1、2·····"}}}}, "required": ["action", "params"]}, "ELEMENT_SHOW_EVENT__OP_ACTIVITY_SUM2025_MAIN__OP_ACTIVITY_JION_BUTTON": {"type": "object", "properties": {"action": {"const": "OP_ACTIVITY_JION_BUTTON", "description": "主页面参与button"}, "params": {"type": "object", "required": [], "properties": {"title": {"type": "string", "description": "按钮文案，上报按钮的文案，如\"向前冲\"等"}, "steps": {"type": "string", "description": "剩余步数，上报进入页面按钮曝光时的剩余步数，0、1、2·····"}}}}, "required": ["action", "params"]}, "CLICK_EVENT__OP_ACTIVITY_SUM2025_MAIN__OP_ACTIVITY_REWARD_CHOOSE_POP": {"type": "object", "properties": {"action": {"const": "OP_ACTIVITY_REWARD_CHOOSE_POP", "description": "主会场礼物选择弹窗"}, "params": {"type": "object", "required": [], "properties": {"source": {"type": "string", "description": "弹窗来源，区分是打卡完成掉落还是用户主动点击", "enum": ["choose", "sign", "newer_guid"]}, "button_name": {"type": "string", "description": "上报点击的按钮名称，如\"换这个“”就选这个“"}, "good_id": {"type": "string", "description": "点击的按钮对应的商品id"}}}}, "required": ["action", "params"]}, "ELEMENT_SHOW_EVENT__OP_ACTIVITY_SUM2025_MAIN__OP_ACTIVITY_REWARD_CHOOSE_POP": {"type": "object", "properties": {"action": {"const": "OP_ACTIVITY_REWARD_CHOOSE_POP", "description": "主会场礼物选择弹窗"}, "params": {"type": "object", "required": [], "properties": {"source": {"type": "string", "description": "弹窗来源，区分是打卡完成掉落还是用户主动点击", "enum": ["choose", "sign", "calender_pop", "newer_guid"]}}}}, "required": ["action", "params"]}, "ELEMENT_SHOW_EVENT__OP_ACTIVITY_SUM2025_MAIN__OP_ACTIVITY_TASK_RESULT_POP__FORWARD": {"type": "object", "properties": {"action": {"const": "OP_ACTIVITY_TASK_RESULT_POP__FORWARD", "description": "主会场返回结果弹窗封皮"}, "params": {"type": "object", "required": [], "properties": {"popup_type": {"type": "string", "description": "弹窗类型", "enum": ["TIME_ASSIST_TASK_SUCCESS", "TIME_ASSIST_RETURN_TASK_SUCCESS", "TIME_LIMIT_TASK_SUCCESS", "ASSIST_DOUBLE_LLCH_TASK_SUCCESS", "ASSIST_DOUBLE_LLCN_TASK_SUCCESS", " LS_LLCH_LLREWD", "LS_LLCN_LLREWD", "LS_COUPON_LLREWD", " LS_WITH_DRAW_CARD_LLREWD", "LS_LLCH_TRANSFER_CARD_LLREWD", "LS_PROFILE_PENDANT_LLREWD", " LS_DEFAULT_BLESS", " LS_DEFAULT_LLCN", "LS_AD_PHOTO", " LS_AD_VIDEO"]}, "encourage_type": {"type": "string", "description": "奖励类型", "enum": ["UNKNOWN", "DEFAULT_BLESS", "DEFAULT_LLCN", "LUCK_RUSH_CHANCE", "LLCN", "LLCH", "COUPON", "LLWDW_CARD", "LLCH_TRANSFER_CARD", "PROFILE_PENDANT", "COMMON_ZT_TASK_LLCN", "COMMON_ZT_TASK_LLCH", "FOLLOW_LLREWD_LLCN_TASK", "FOLLOW_LLREWD_LLCH_TASK", "FOLLOW_LLREWD_BLESS_TASK", "FOLLOW_LLREWD_RUSH_TASK", "WATCH_LIVE_LLREWD_LLCH_TASK", "WATCH_LIVE_LLREWD_LLCN_TASK", "WATCH_LIVE_LLREWD_RUSH_TASK", "TIME_LIMITED_ASSIST_DOUBLE_LLCN_TASK", "TIME_LIMITED_ASSIST_DOUBLE_LLCH_TASK", "TIME_LIMITED_COUNT_LLCN_TASK", "TIME_LIMITED_COUNT_LLCH_TASK", "TIME_LIMITED_INVITE_LLCN_TASK", "TIME_LIMITED_INVITE_LLCH_TASK", "TIME_LIMITED_INVITE_REFLUX_LLCN_TASK", "TIME_LIMITED_INVITE_REFLUX_LLCH_TASK", "TIME_LIMITED_PK_TASK", "TEAM_TASK", "AD_VIDEO", "AD_PHOTO", "FISSION_GOAT_DIVERSATION", "PHYSICAL_PRODUCT", "AD_STATIC_DIVERSION_POPUP", "ALWAYS_PULL_TASK", "INVOKE_APP_LLREWD_LLCN", "INVOKE_APP_LLREWD_LLCH", "INVOKE_APP_LLREWD_SHAKE"]}, "task_type": {"type": "string", "description": "任务类型"}, "task_id": {"type": "string", "description": "任务id"}, "task_name": {"type": "string", "description": "任务名称"}, "brand_name": {"type": "string", "description": "冠名商名称"}, "coupon_id": {"type": "string", "description": "包含所有券的实例id"}, "photo_id": {"type": "string", "description": "商业化id，包含图片、视频等，实际上报为url区分"}, "button_name": {"type": "string", "description": "点击的按钮名称，上报按钮文案，如“去开心手下”"}}}}, "required": ["action", "params"]}, "ELEMENT_SHOW_EVENT__OP_ACTIVITY_SUM2025_MAIN__OP_ACTIVITY_TASK_ASSIGN_POP_FORWARD": {"type": "object", "properties": {"action": {"const": "OP_ACTIVITY_TASK_ASSIGN_POP_FORWARD", "description": "主会场任务下发弹窗封皮"}, "params": {"type": "object", "required": [], "properties": {"popup_type": {"type": "string", "description": "弹窗类型", "enum": ["TIME_ASSIST_TASK_SUCCESS", "TIME_ASSIST_RETURN_TASK_SUCCESS", "TIME_LIMIT_TASK_SUCCESS", "ASSIST_DOUBLE_LLCH_TASK_SUCCESS", "ASSIST_DOUBLE_LLCN_TASK_SUCCESS", " LS_LLCH_LLREWD", "LS_LLCN_LLREWD", "LS_COUPON_LLREWD", " LS_WITH_DRAW_CARD_LLREWD", "LS_LLCH_TRANSFER_CARD_LLREWD", "LS_PROFILE_PENDANT_LLREWD", " LS_DEFAULT_BLESS", " LS_DEFAULT_LLCN", "LS_AD_PHOTO", " LS_AD_VIDEO"]}, "task_type": {"type": "string", "description": "任务类型"}, "task_id": {"type": "string", "description": "任务id"}, "task_name": {"type": "string", "description": "任务名称"}, "brand_name": {"type": "string", "description": "冠名商名称"}, "title": {"type": "string", "description": "弹窗主标题"}}}}, "required": ["action", "params"]}, "ELEMENT_SHOW_EVENT__OP_ACTIVITY_SUM2025_MAIN__OP_ACTIVITY_REWARD_POP_FORWARD": {"type": "object", "properties": {"action": {"const": "OP_ACTIVITY_REWARD_POP_FORWARD", "description": "主会场奖励弹窗封皮"}, "params": {"type": "object", "required": [], "properties": {"encourage_type": {"type": "string", "description": "奖励类型", "enum": ["UNKNOWN", "DEFAULT_BLESS", "DEFAULT_LLCN", "LUCK_RUSH_CHANCE", "LLCN", "LLCH", "COUPON", "LLWDW_CARD", "LLCH_TRANSFER_CARD", "PROFILE_PENDANT", "COMMON_ZT_TASK_LLCN", "COMMON_ZT_TASK_LLCH", "FOLLOW_LLREWD_LLCN_TASK", "FOLLOW_LLREWD_LLCH_TASK", "FOLLOW_LLREWD_BLESS_TASK", "FOLLOW_LLREWD_RUSH_TASK", "WATCH_LIVE_LLREWD_LLCH_TASK", "WATCH_LIVE_LLREWD_LLCN_TASK", "WATCH_LIVE_LLREWD_RUSH_TASK", "TIME_LIMITED_ASSIST_DOUBLE_LLCN_TASK", "TIME_LIMITED_ASSIST_DOUBLE_LLCH_TASK", "TIME_LIMITED_COUNT_LLCN_TASK", "TIME_LIMITED_COUNT_LLCH_TASK", "TIME_LIMITED_INVITE_LLCN_TASK", "TIME_LIMITED_INVITE_LLCH_TASK", "TIME_LIMITED_INVITE_REFLUX_LLCN_TASK", "TIME_LIMITED_INVITE_REFLUX_LLCH_TASK", "TIME_LIMITED_PK_TASK", "TEAM_TASK", "AD_VIDEO", "AD_PHOTO", "FISSION_GOAT_DIVERSATION", "PHYSICAL_PRODUCT", "AD_STATIC_DIVERSION_POPUP", "ALWAYS_PULL_TASK", "INVOKE_APP_LLREWD_LLCN", "INVOKE_APP_LLREWD_LLCH", "INVOKE_APP_LLREWD_SHAKE"]}, "popup_type": {"type": "string", "description": "弹窗类型", "enum": ["TIME_ASSIST_TASK_SUCCESS", "TIME_ASSIST_RETURN_TASK_SUCCESS", "TIME_LIMIT_TASK_SUCCESS", "ASSIST_DOUBLE_LLCH_TASK_SUCCESS", "ASSIST_DOUBLE_LLCN_TASK_SUCCESS", " LS_LLCH_LLREWD", "LS_LLCN_LLREWD", "LS_COUPON_LLREWD", " LS_WITH_DRAW_CARD_LLREWD", "LS_LLCH_TRANSFER_CARD_LLREWD", "LS_PROFILE_PENDANT_LLREWD", " LS_DEFAULT_BLESS", " LS_DEFAULT_LLCN", "LS_AD_PHOTO", " LS_AD_VIDEO"]}, "brand_name": {"type": "string", "description": "品牌名称"}, "title": {"type": "string", "description": "弹窗标题"}, "button_name": {"type": "string", "description": "主按钮文案（默认高亮的button）"}, "coupon_id": {"type": "string", "description": "包含所有券的实例id"}, "photo_id": {"type": "string", "description": "商业化id，包含图片、视频等，实际上报为url区分"}}}}, "required": ["action", "params"]}, "ELEMENT_SHOW_EVENT__OP_ACTIVITY_SUM2025_MAIN__OP_ACTIVITY_BUSINESS_GRID_LOGO": {"type": "object", "properties": {"action": {"const": "OP_ACTIVITY_BUSINESS_GRID_LOGO", "description": "格子上的商业化logo"}, "params": {"type": "object", "required": [], "properties": {"icon_url": {"type": "string", "description": "商业化图片url"}, "brand_name": {"type": "string", "description": "品牌名"}}}}, "required": ["action", "params"]}, "ELEMENT_SHOW_EVENT__OP_ACTIVITY_SUM2025_MAIN__OP_ACTIVITY_BUSINESS_BUILD_LOGO": {"type": "object", "properties": {"action": {"const": "OP_ACTIVITY_BUSINESS_BUILD_LOGO", "description": "建筑上的商业化logo"}, "params": {"type": "object", "required": [], "properties": {"type": {"type": "string", "description": "建筑上的logo类型，区分商业化和建筑装饰", "enum": ["guide", "decoration"]}, "icon_url": {"type": "string", "description": "商业化图片url"}, "link_url": {"type": "string", "description": "跳转url，包含导流"}, "brand_name": {"type": "string", "description": "品牌名"}, "name": {"type": "string", "description": "上报建筑名"}}}}, "required": ["action", "params"]}, "ELEMENT_SHOW_EVENT__OP_ACTIVITY_SUM2025_MAIN__OP_ACTIVITY_BUSINESS_LOGO": {"type": "object", "properties": {"action": {"const": "OP_ACTIVITY_BUSINESS_LOGO", "description": "主会场首页商业化logo"}, "params": {"type": "object", "required": [], "properties": {"url": {"type": "string", "description": "商业化点位的url"}}}}, "required": ["action", "params"]}, "CLICK_EVENT__OP_ACTIVITY_SUM2025_MAIN__OP_ACTIVITY_MAKE_TEAM_SUCCESS": {"type": "object", "properties": {"action": {"const": "OP_ACTIVITY_MAKE_TEAM_SUCCESS", "description": "组队成功事件"}, "params": {"type": "object", "required": [], "properties": {"team_id": {"type": "string", "description": "队伍id"}, "button_name": {"type": "string", "description": "按钮名称，如“开启组队打卡\"和\"开心手下“"}, "title": {"type": "string", "description": "标题"}, "brand_name": {"type": "string", "description": "品牌名称"}}}}, "required": ["action", "params"]}, "CLICK_EVENT__OP_ACTIVITY_SUM2025_MAIN__OP_ACTIVITY_MAKE_TEAM_SIGN_SUCCESS": {"type": "object", "properties": {"action": {"const": "OP_ACTIVITY_MAKE_TEAM_SIGN_SUCCESS", "description": "组队且打卡7天成功事件"}, "params": {"type": "object", "required": [], "properties": {"team_id": {"type": "string", "description": "队伍id"}, "button_name": {"type": "string", "description": "按钮名称，如“查看我的直通卡\""}, "brand_name": {"type": "string", "description": "品牌名称"}, "title": {"type": "string", "description": "标题"}}}}, "required": ["action", "params"]}, "CLICK_EVENT__OP_ACTIVITY_SUM2025_MAIN__OP_ACTIVITY_TEAM_BREAK": {"type": "object", "properties": {"action": {"const": "OP_ACTIVITY_TEAM_BREAK", "description": "队伍解散完成"}, "params": {"type": "object", "required": [], "properties": {"break_reason": {"type": "string", "description": "解散原因", "enum": ["EXIT_TEAM", "BREAK_SIGN", "MATCH_EXPIRE"]}, "type": {"type": "string", "description": "是否打卡超过7天", "enum": ["1", "2"]}, "button_name": {"type": "string", "description": "点击的按钮名称"}, "brand_name": {"type": "string", "description": "品牌名称"}, "title": {"type": "string", "description": "标题"}}}}, "required": ["action", "params"]}, "ELEMENT_SHOW_EVENT__OP_ACTIVITY_SUM2025_MAIN__OP_ACTIVITY_TEAM_BREAK": {"type": "object", "properties": {"action": {"const": "OP_ACTIVITY_TEAM_BREAK", "description": "队伍解散完成"}, "params": {"type": "object", "required": [], "properties": {"break_reason": {"type": "string", "description": "解散原因", "enum": ["EXIT_TEAM", "BREAK_SIGN", "MATCH_EXPIRE"]}, "type": {"type": "string", "description": "是否打卡超过7天", "enum": ["1", "2"]}, "brand_name": {"type": "string", "description": "品牌名称"}, "title": {"type": "string", "description": "标题"}}}}, "required": ["action", "params"]}, "ELEMENT_SHOW_EVENT__OP_ACTIVITY_SUM2025_MAIN__OP_ACTIVITY_MAKE_TEAM_SIGN_SUCCESS": {"type": "object", "properties": {"action": {"const": "OP_ACTIVITY_MAKE_TEAM_SIGN_SUCCESS", "description": "组队且打卡7天成功事件"}, "params": {"type": "object", "required": [], "properties": {"team_id": {"type": "string", "description": "队伍id"}, "brand_name": {"type": "string", "description": "品牌名称"}, "title": {"type": "string", "description": "标题"}}}}, "required": ["action", "params"]}, "ELEMENT_SHOW_EVENT__OP_ACTIVITY_SUM2025_MAIN__OP_ACTIVITY_MAKE_TEAM_SUCCESS": {"type": "object", "properties": {"action": {"const": "OP_ACTIVITY_MAKE_TEAM_SUCCESS", "description": "组队成功事件"}, "params": {"type": "object", "required": [], "properties": {"team_id": {"type": "string", "description": "队伍id"}, "title": {"type": "string", "description": "弹窗标题"}, "brand_name": {"type": "string", "description": "品牌名称"}}}}, "required": ["action", "params"]}, "CLICK_EVENT__OP_ACTIVITY_SUM2025_MAIN__OP_ACTIVITY_REWARD_CHOOSE_RESULT": {"type": "object", "properties": {"action": {"const": "OP_ACTIVITY_REWARD_CHOOSE_RESULT", "description": "礼物选择结果"}, "params": {"type": "object", "required": [], "properties": {"source": {"type": "string", "description": "弹窗来源，区分是打卡完成掉落还是用户主动点击"}, "good_id": {"type": "string", "description": "选择成功的商品id"}, "result_type": {"type": "string", "description": "区分时成功还是失败", "enum": ["success", "fail", "sucess"]}, "button_name": {"type": "string", "description": "点击的按钮名称，上报按钮文档"}}}}, "required": ["action", "params"]}, "CLICK_EVENT__OP_ACTIVITY_SUM2025_MAIN__OP_ACTIVITY_PUSH_OPEN_GUIDE_IS_OPEN_RESULT": {"type": "object", "properties": {"action": {"const": "OP_ACTIVITY_PUSH_OPEN_GUIDE_IS_OPEN_RESULT", "description": "开关开启结果"}, "params": {"type": "object", "required": [], "properties": {"is_open": {"type": "string", "description": "", "enum": ["true", "false"]}}}}, "required": ["action", "params"]}, "CLICK_EVENT__OP_ACTIVITY_SUM2025_MAIN__OP_ACTIVITY_PUSH_OPEN_GUIDE_CNY_POPUP": {"type": "object", "properties": {"action": {"const": "OP_ACTIVITY_PUSH_OPEN_GUIDE_CNY_POPUP", "description": "打开推送消息通知弹窗"}, "params": {"type": "object", "required": [], "properties": {"click_type": {"type": "string", "description": "点击类型", "enum": ["open", "ignore", "close"]}}}}, "required": ["action", "params"]}, "ELEMENT_SHOW_EVENT__OP_ACTIVITY_SUM2025_MAIN__OP_ACTIVITY_PUSH_OPEN_GUIDE_CNY_POPUP": {"type": "object", "properties": {"action": {"const": "OP_ACTIVITY_PUSH_OPEN_GUIDE_CNY_POPUP", "description": "打开推送消息通知弹窗"}, "params": {}}, "required": ["action", "params"]}, "CLICK_EVENT__OP_ACTIVITY_SUM2025_MAIN__OP_ACTIVITY_CORE_POP": {"type": "object", "properties": {"action": {"const": "OP_ACTIVITY_CORE_POP", "description": "主会场其他核心弹窗"}, "params": {"type": "object", "required": [], "properties": {"popup_type": {"type": "string", "description": "弹窗类型，需要给枚举值"}, "title": {"type": "string", "description": "弹窗标题"}, "brand_name": {"type": "string", "description": "品牌商名称"}, "coupon_id": {"type": "string", "description": "包含所有券的实例id，没有上报“”"}, "good_id": {"type": "string", "description": "包含商品的上报商品id，没有上报\"\""}, "button_name": {"type": "string", "description": "上报点击的按钮名称，关闭不上报"}}}}, "required": ["action", "params"]}, "ELEMENT_SHOW_EVENT__OP_ACTIVITY_SUM2025_MAIN__OP_ACTIVITY_CORE_POP": {"type": "object", "properties": {"action": {"const": "OP_ACTIVITY_CORE_POP", "description": "主会场其他核心弹窗"}, "params": {"type": "object", "required": [], "properties": {"popup_type": {"type": "string", "description": "弹窗类型，需要给枚举值"}, "title": {"type": "string", "description": "弹窗标题"}, "brand_name": {"type": "string", "description": "品牌商名称"}, "coupon_id": {"type": "string", "description": "包含所有券的实例id"}, "good_id": {"type": "string", "description": "包含商品的上报商品id"}, "button_name": {"type": "string", "description": "主button按钮"}}}}, "required": ["action", "params"]}, "CLICK_EVENT__OP_ACTIVITY_SUM2025_MAIN__OP_ACTIVITY_TASK_RESULT_POP": {"type": "object", "properties": {"action": {"const": "OP_ACTIVITY_TASK_RESULT_POP", "description": "主会场返回结果弹窗"}, "params": {"type": "object", "required": [], "properties": {"task_finish_status": {"type": "string", "description": "任务完成状态", "enum": ["success", "fail"]}, "task_type": {"type": "string", "description": "任务类型"}, "task_id": {"type": "string", "description": "任务id"}, "task_name": {"type": "string", "description": "任务名称"}, "button_name": {"type": "string", "description": "点击的按钮名称，上报按钮文案，如“去开心手下”"}, "brand_name": {"type": "string", "description": "冠名商名称"}, "encourage_type": {"type": "string", "description": "奖励类型", "enum": ["UNKNOWN", "DEFAULT_BLESS", "DEFAULT_LLCN", "LUCK_RUSH_CHANCE", "LLCN", "LLCH", "COUPON", "LLWDW_CARD", "LLCH_TRANSFER_CARD", "PROFILE_PENDANT", "COMMON_ZT_TASK_LLCN", "COMMON_ZT_TASK_LLCH", "FOLLOW_LLREWD_LLCN_TASK", "FOLLOW_LLREWD_LLCH_TASK", "FOLLOW_LLREWD_BLESS_TASK", "FOLLOW_LLREWD_RUSH_TASK", "WATCH_LIVE_LLREWD_LLCH_TASK", "WATCH_LIVE_LLREWD_LLCN_TASK", "WATCH_LIVE_LLREWD_RUSH_TASK", "TIME_LIMITED_ASSIST_DOUBLE_LLCN_TASK", "TIME_LIMITED_ASSIST_DOUBLE_LLCH_TASK", "TIME_LIMITED_COUNT_LLCN_TASK", "TIME_LIMITED_COUNT_LLCH_TASK", "TIME_LIMITED_INVITE_LLCN_TASK", "TIME_LIMITED_INVITE_LLCH_TASK", "TIME_LIMITED_INVITE_REFLUX_LLCN_TASK", "TIME_LIMITED_INVITE_REFLUX_LLCH_TASK", "TIME_LIMITED_PK_TASK", "TEAM_TASK", "AD_VIDEO", "AD_PHOTO", "FISSION_GOAT_DIVERSATION", "PHYSICAL_PRODUCT", "AD_STATIC_DIVERSION_POPUP", "ALWAYS_PULL_TASK", "INVOKE_APP_LLREWD_LLCN", "INVOKE_APP_LLREWD_LLCH", "INVOKE_APP_LLREWD_SHAKE"]}, "popup_type": {"type": "string", "description": "弹窗类型", "enum": ["TIME_ASSIST_TASK_SUCCESS", "TIME_ASSIST_RETURN_TASK_SUCCESS", "TIME_LIMIT_TASK_SUCCESS", "ASSIST_DOUBLE_LLCH_TASK_SUCCESS", "ASSIST_DOUBLE_LLCN_TASK_SUCCESS", " LS_LLCH_LLREWD", "LS_LLCN_LLREWD", "LS_COUPON_LLREWD", " LS_WITH_DRAW_CARD_LLREWD", "LS_LLCH_TRANSFER_CARD_LLREWD", "LS_PROFILE_PENDANT_LLREWD", " LS_DEFAULT_BLESS", " LS_DEFAULT_LLCN", "LS_AD_PHOTO", " LS_AD_VIDEO"]}, "photo_id": {"type": "string", "description": "商业化id，包含图片、视频等，实际上报为url区分"}, "coupon_id": {"type": "string", "description": "包含所有券的实例id"}}}}, "required": ["action", "params"]}, "ELEMENT_SHOW_EVENT__OP_ACTIVITY_SUM2025_MAIN__OP_ACTIVITY_TASK_RESULT_POP": {"type": "object", "properties": {"action": {"const": "OP_ACTIVITY_TASK_RESULT_POP", "description": "主会场返回结果弹窗"}, "params": {"type": "object", "required": [], "properties": {"task_finish_status": {"type": "string", "description": "任务完成状态", "enum": ["success", "fail"]}, "task_type": {"type": "string", "description": "任务类型"}, "task_id": {"type": "string", "description": "任务id"}, "task_name": {"type": "string", "description": "任务名称"}, "brand_name": {"type": "string", "description": "冠名商名称"}, "popup_type": {"type": "string", "description": "弹窗类型", "enum": ["TIME_ASSIST_TASK_SUCCESS", "TIME_ASSIST_RETURN_TASK_SUCCESS", "TIME_LIMIT_TASK_SUCCESS", "ASSIST_DOUBLE_LLCH_TASK_SUCCESS", "ASSIST_DOUBLE_LLCN_TASK_SUCCESS", " LS_LLCH_LLREWD", "LS_LLCN_LLREWD", "LS_COUPON_LLREWD", " LS_WITH_DRAW_CARD_LLREWD", "LS_LLCH_TRANSFER_CARD_LLREWD", "LS_PROFILE_PENDANT_LLREWD", " LS_DEFAULT_BLESS", " LS_DEFAULT_LLCN", "LS_AD_PHOTO", " LS_AD_VIDEO"]}, "coupon_id": {"type": "string", "description": "包含所有券的实例id"}, "encourage_type": {"type": "string", "description": "奖励类型", "enum": ["UNKNOWN", "DEFAULT_BLESS", "DEFAULT_LLCN", "LUCK_RUSH_CHANCE", "LLCN", "LLCH", "COUPON", "LLWDW_CARD", "LLCH_TRANSFER_CARD", "PROFILE_PENDANT", "COMMON_ZT_TASK_LLCN", "COMMON_ZT_TASK_LLCH", "FOLLOW_LLREWD_LLCN_TASK", "FOLLOW_LLREWD_LLCH_TASK", "FOLLOW_LLREWD_BLESS_TASK", "FOLLOW_LLREWD_RUSH_TASK", "WATCH_LIVE_LLREWD_LLCH_TASK", "WATCH_LIVE_LLREWD_LLCN_TASK", "WATCH_LIVE_LLREWD_RUSH_TASK", "TIME_LIMITED_ASSIST_DOUBLE_LLCN_TASK", "TIME_LIMITED_ASSIST_DOUBLE_LLCH_TASK", "TIME_LIMITED_COUNT_LLCN_TASK", "TIME_LIMITED_COUNT_LLCH_TASK", "TIME_LIMITED_INVITE_LLCN_TASK", "TIME_LIMITED_INVITE_LLCH_TASK", "TIME_LIMITED_INVITE_REFLUX_LLCN_TASK", "TIME_LIMITED_INVITE_REFLUX_LLCH_TASK", "TIME_LIMITED_PK_TASK", "TEAM_TASK", "AD_VIDEO", "AD_PHOTO", "FISSION_GOAT_DIVERSATION", "PHYSICAL_PRODUCT", "AD_STATIC_DIVERSION_POPUP", "ALWAYS_PULL_TASK", "INVOKE_APP_LLREWD_LLCN", "INVOKE_APP_LLREWD_LLCH", "INVOKE_APP_LLREWD_SHAKE"]}, "photo_id": {"type": "string", "description": "商业化id，包含图片、视频等，实际上报为url区分"}}}}, "required": ["action", "params"]}, "CLICK_EVENT__OP_ACTIVITY_SUM2025_MAIN__OP_ACTIVITY_TASK_ASSIGN_POP": {"type": "object", "properties": {"action": {"const": "OP_ACTIVITY_TASK_ASSIGN_POP", "description": "主会场任务下发弹窗"}, "params": {"type": "object", "required": [], "properties": {"task_type": {"type": "string", "description": "任务类型"}, "task_id": {"type": "string", "description": "任务id"}, "task_name": {"type": "string", "description": "任务名称"}, "brand_name": {"type": "string", "description": "冠名商名称"}, "title": {"type": "string", "description": "弹窗主标题"}, "button_name": {"type": "string", "description": "点击的按钮名称，上报按钮文案，如“去邀请好友”", "enum": ["+"]}, "popup_type": {"type": "string", "description": "弹窗类型", "enum": ["TIME_ASSIST_TASK_SUCCESS", "TIME_ASSIST_RETURN_TASK_SUCCESS", "TIME_LIMIT_TASK_SUCCESS", "ASSIST_DOUBLE_LLCH_TASK_SUCCESS", "ASSIST_DOUBLE_LLCN_TASK_SUCCESS", "LS_LLCH_LLREWD", "LS_LLCN_LLREWD", "LS_COUPON_LLREWD", "LS_WITH_DRAW_CARD_LLREWD", "LS_LLCH_TRANSFER_CARD_LLREWD", "LS_PROFILE_PENDANT_LLREWD", "LS_DEFAULT_BLESS", "LS_DEFAULT_LLCN", "LS_AD_PHOTO", "LS_AD_VIDEO", "GRID_TASK_SHEET", "HUGE_SIGN_IN_SUBSCRIBE_POPUP"]}}}}, "required": ["action", "params"]}, "ELEMENT_SHOW_EVENT__OP_ACTIVITY_SUM2025_MAIN__OP_ACTIVITY_TASK_ASSIGN_POP": {"type": "object", "properties": {"action": {"const": "OP_ACTIVITY_TASK_ASSIGN_POP", "description": "主会场任务下发弹窗"}, "params": {"type": "object", "required": [], "properties": {"task_type": {"type": "string", "description": "任务类型"}, "task_id": {"type": "string", "description": "任务id"}, "task_name": {"type": "string", "description": "任务名称"}, "brand_name": {"type": "string", "description": "冠名商名称"}, "title": {"type": "string", "description": "弹窗主标题"}, "popup_type": {"type": "string", "description": "弹窗类型", "enum": ["TIME_ASSIST_TASK_SUCCESS", "TIME_ASSIST_RETURN_TASK_SUCCESS", "TIME_LIMIT_TASK_SUCCESS", "ASSIST_DOUBLE_LLCH_TASK_SUCCESS", "ASSIST_DOUBLE_LLCN_TASK_SUCCESS", "LS_LLCH_LLREWD", "LS_LLCN_LLREWD", "LS_COUPON_LLREWD", "LS_WITH_DRAW_CARD_LLREWD", "LS_LLCH_TRANSFER_CARD_LLREWD", "LS_PROFILE_PENDANT_LLREWD", "LS_DEFAULT_BLESS", "LS_DEFAULT_LLCN", "LS_AD_PHOTO", "LS_AD_VIDEO", "GRID_TASK_SHEET", "HUGE_SIGN_IN_SUBSCRIBE_POPUP"]}}}}, "required": ["action", "params"]}, "CLICK_EVENT__OP_ACTIVITY_SUM2025_MAIN__OP_ACTIVITY_DRECT_CARD": {"type": "object", "properties": {"action": {"const": "OP_ACTIVITY_DRECT_CARD", "description": "直通卡"}, "params": {"type": "object", "required": [], "properties": {"index": {"type": "string", "description": "卡片顺序，从上到下从1开始"}, "button_type": {"type": "string", "description": "目前每个卡片针对的按钮文案，用来区分是否可用\"去使用\"", "enum": ["1", "2", "3", "4", "5", "去使用", "明日可用", "暂不使用（置灰的不可点击", "已使用（置灰的不可点击", "已过期（置灰的不可点击"]}}}}, "required": ["action", "params"]}, "ELEMENT_SHOW_EVENT__OP_ACTIVITY_SUM2025_MAIN__OP_ACTIVITY_DRECT_CARD": {"type": "object", "properties": {"action": {"const": "OP_ACTIVITY_DRECT_CARD", "description": "直通卡"}, "params": {"type": "object", "required": [], "properties": {"index": {"type": "string", "description": "卡片顺序，从上到下从1开始"}, "button_type": {"type": "string", "description": "目前每个卡片针对的按钮文案，用来区分是否可用\"去使用\"", "enum": ["1", "2", "3", "4", "5", "去使用", "明日可用", "暂不使用（置灰的不可点击", "已使用（置灰的不可点击", "已过期（置灰的不可点击"]}}}}, "required": ["action", "params"]}, "CLICK_EVENT__OP_ACTIVITY_SUM2025_MAIN__OP_ACTIVITY_MAKE_TEAM_BUTTON": {"type": "object", "properties": {"action": {"const": "OP_ACTIVITY_MAKE_TEAM_BUTTON", "description": "主会场页面去组队按钮"}, "params": {"type": "object", "required": [], "properties": {"entry_status": {"type": "string", "description": "目前组队状态", "enum": ["1", "2", "3", "4"]}, "number": {"type": "string", "description": "目前组队人数", "enum": ["1", "2", "3"]}}}}, "required": ["action", "params"]}, "ELEMENT_SHOW_EVENT__OP_ACTIVITY_SUM2025_MAIN__OP_ACTIVITY_MAKE_TEAM_BUTTON": {"type": "object", "properties": {"action": {"const": "OP_ACTIVITY_MAKE_TEAM_BUTTON", "description": "主会场页面去组队按钮"}, "params": {"type": "object", "required": [], "properties": {"entry_status": {"type": "string", "description": "目前组队状态", "enum": ["1", "2", "3", "4"]}, "number": {"type": "string", "description": "目前组队人数", "enum": ["1", "2", "3"]}}}}, "required": ["action", "params"]}, "CLICK_EVENT__OP_ACTIVITY_SUM2025_MAIN__OP_ACTIVITY_TASK_ITEM": {"type": "object", "properties": {"action": {"const": "OP_ACTIVITY_TASK_ITEM", "description": "任务卡片"}, "params": {"type": "object", "required": [], "properties": {"title": {"type": "string", "description": "上报任务弹窗顶部的title文案，如“邀好友，得现金“”做任务，赚步数“"}, "task_id": {"type": "string", "description": "任务id；"}, "task_name": {"type": "string", "description": "任务名称"}, "task_type": {"type": "string", "description": "任务类型"}, "task_status": {"type": "string", "description": "任务状态"}, "url": {"type": "string", "description": "任务页面链接"}, "index": {"type": "string", "description": "卡片顺序，从上到下从1开始"}, "button_name": {"type": "string", "description": "按钮名称"}, "is_popup": {"type": "string", "description": "是否为悬浮弹窗类型"}}}}, "required": ["action", "params"]}, "ELEMENT_SHOW_EVENT__OP_ACTIVITY_SUM2025_MAIN__OP_ACTIVITY_TASK_ITEM": {"type": "object", "properties": {"action": {"const": "OP_ACTIVITY_TASK_ITEM", "description": "任务卡片"}, "params": {"type": "object", "required": [], "properties": {"title": {"type": "string", "description": "上报任务弹窗顶部的title文案，如“邀好友，得现金“”做任务，赚步数“"}, "task_id": {"type": "string", "description": "任务id"}, "task_name": {"type": "string", "description": "任务名称"}, "task_type": {"type": "string", "description": "任务类型"}, "task_status": {"type": "string", "description": "任务状态"}, "url": {"type": "string", "description": "任务页面链接"}, "index": {"type": "string", "description": "卡片顺序，从上到下从1开始"}, "button_name": {"type": "string", "description": "按钮名称"}, "is_popup": {"type": "string", "description": "是否为悬浮弹窗类型"}}}}, "required": ["action", "params"]}, "ELEMENT_SHOW_EVENT__OP_ACTIVITY_SUM2025_MAIN__OP_ACTIVITY_TASK_LIST_POP": {"type": "object", "properties": {"action": {"const": "OP_ACTIVITY_TASK_LIST_POP", "description": "任务列表弹窗"}, "params": {"type": "object", "required": [], "properties": {"title": {"type": "string", "description": "上报任务弹窗顶部的title文案，如“邀好友，得现金“”做任务，赚步数“"}}}}, "required": ["action", "params"]}, "ELEMENT_SHOW_EVENT__OP_ACTIVITY_SUM2025_MAIN__OP_ACTIVITY_MAKE_TEAM_BUBBLE_REMIND_POP": {"type": "object", "properties": {"action": {"const": "OP_ACTIVITY_MAKE_TEAM_BUBBLE_REMIND_POP", "description": "组队气泡提醒弹窗"}, "params": {"type": "object", "required": [], "properties": {"title": {"type": "string", "description": "组队气泡提醒弹窗文案"}}}}, "required": ["action", "params"]}, "CLICK_EVENT__OP_ACTIVITY_SUM2025_MAIN__OP_ACTIVITY_BUBBLE_REMIND_POP": {"type": "object", "properties": {"action": {"const": "OP_ACTIVITY_BUBBLE_REMIND_POP", "description": "气泡提醒弹窗"}, "params": {"type": "object", "required": [], "properties": {"title": {"type": "string", "description": "气泡提醒弹窗文案"}, "remind_num": {"type": "string", "description": "人数，上报具体数值"}, "click_positon": {"type": "string", "description": "点击按钮名称，如“去提醒”"}, "click_position": {"type": "string", "description": "点击按钮名称，如“去提醒”"}}}}, "required": ["action", "params"]}, "ELEMENT_SHOW_EVENT__OP_ACTIVITY_SUM2025_MAIN__OP_ACTIVITY_BUBBLE_REMIND_POP": {"type": "object", "properties": {"action": {"const": "OP_ACTIVITY_BUBBLE_REMIND_POP", "description": "气泡提醒弹窗"}, "params": {"type": "object", "required": [], "properties": {"title": {"type": "string", "description": "气泡提醒弹窗文案"}, "remind_num": {"type": "string", "description": "人数，上报具体数值"}}}}, "required": ["action", "params"]}, "CLICK_EVENT__OP_ACTIVITY_SUM2025_MAIN__OP_ACTIVITY_TASK_BUTTON": {"type": "object", "properties": {"action": {"const": "OP_ACTIVITY_TASK_BUTTON", "description": "主会场任务入口button"}, "params": {"type": "object", "required": [], "properties": {"title": {"type": "string", "description": "按钮文案"}}}}, "required": ["action", "params"]}, "ELEMENT_SHOW_EVENT__OP_ACTIVITY_SUM2025_MAIN__OP_ACTIVITY_SIGN_INFO_POP": {"type": "object", "properties": {"action": {"const": "OP_ACTIVITY_SIGN_INFO_POP", "description": "打卡进度说明弹窗"}, "params": {"type": "object", "required": [], "properties": {"title": {"type": "string", "description": "上面弹窗文案"}, "button_name": {"type": "string", "description": "主按钮文案，如“向前冲”"}}}}, "required": ["action", "params"]}, "CLICK_EVENT__OP_ACTIVITY_SUM2025_MAIN__OP_ACTIVITY_RE_SIGN_POP": {"type": "object", "properties": {"action": {"const": "OP_ACTIVITY_RE_SIGN_POP", "description": "补签弹窗"}, "params": {"type": "object", "required": [], "properties": {"button_name": {"type": "string", "description": "点击的按钮名称，上报按钮文案，如“补签“、”立即邀请“"}, "title": {"type": "string", "description": "用户看到的任务类型title，多个通过arry上传"}, "type": {"type": "string", "description": "用户看到的任务类型title，多个通过arry上传，枚举用server已有"}, "popup_type": {"type": "string", "description": "区分不同的补签弹窗类型", "enum": ["1", "2"]}}}}, "required": ["action", "params"]}, "ELEMENT_SHOW_EVENT__OP_ACTIVITY_SUM2025_MAIN__OP_ACTIVITY_RE_SIGN_POP": {"type": "object", "properties": {"action": {"const": "OP_ACTIVITY_RE_SIGN_POP", "description": "补签弹窗"}, "params": {"type": "object", "required": [], "properties": {"title": {"type": "string", "description": "用户看到的任务类型title，多个通过arry上传"}, "type": {"type": "string", "description": "用户看到的任务类型title，多个通过arry上传，枚举用server已有"}, "popup_type": {"type": "string", "description": "区分不同的补签弹窗类型", "enum": ["1", "2"]}}}}, "required": ["action", "params"]}, "CLICK_EVENT__OP_ACTIVITY_SUM2025_MAIN__OP_ACTIVITY_CALENDER_POP": {"type": "object", "properties": {"action": {"const": "OP_ACTIVITY_CALENDER_POP", "description": "日历弹窗"}, "params": {"type": "object", "required": [], "properties": {"status": {"type": "string", "description": "日历弹窗签到状态", "enum": ["1", "0"]}, "title": {"type": "string", "description": "弹窗标题，上传弹窗标题文案，如“已连续签到14天“”已经断签2天“了"}, "button_name": {"type": "string", "description": "点击的按钮名称", "enum": ["chang_good", "re_sign", "reminder", "close"]}}}}, "required": ["action", "params"]}, "ELEMENT_SHOW_EVENT__OP_ACTIVITY_SUM2025_MAIN__OP_ACTIVITY_CALENDER_POP": {"type": "object", "properties": {"action": {"const": "OP_ACTIVITY_CALENDER_POP", "description": "日历弹窗"}, "params": {"type": "object", "required": [], "properties": {"status": {"type": "string", "description": "日历弹窗签到状态", "enum": ["1", "0"]}, "title": {"type": "string", "description": "弹窗标题，上传弹窗标题文案，如“已连续签到14天“”已经断签2天“了"}}}}, "required": ["action", "params"]}, "CLICK_EVENT__OP_ACTIVITY_SUM2025_MAIN__OP_ACTIVITY_TIME_BAR": {"type": "object", "properties": {"action": {"const": "OP_ACTIVITY_TIME_BAR", "description": "主会场打卡记录按钮（日历入口）"}, "params": {"type": "object", "required": [], "properties": {"title": {"type": "string", "description": "按钮处文案标题"}}}}, "required": ["action", "params"]}, "ELEMENT_SHOW_EVENT__OP_ACTIVITY_SUM2025_MAIN__OP_ACTIVITY_TIME_BAR": {"type": "object", "properties": {"action": {"const": "OP_ACTIVITY_TIME_BAR", "description": "主会场打卡记录按钮（日历入口）"}, "params": {"type": "object", "required": [], "properties": {"title": {"type": "string", "description": "按钮处文案标题"}}}}, "required": ["action", "params"]}, "CLICK_EVENT__OP_ACTIVITY_SUM2025_MAIN__OP_ACTIVITY_CHANGE_GOOD_ENTR": {"type": "object", "properties": {"action": {"const": "OP_ACTIVITY_CHANGE_GOOD_ENTR", "description": "主会场首页换商品入口"}, "params": {"type": "object", "required": [], "properties": {"good_id": {"type": "string", "description": "曝光时的商品id"}}}}, "required": ["action", "params"]}, "ELEMENT_SHOW_EVENT__OP_ACTIVITY_SUM2025_MAIN__OP_ACTIVITY_CHANGE_GOOD_ENTR": {"type": "object", "properties": {"action": {"const": "OP_ACTIVITY_CHANGE_GOOD_ENTR", "description": "主会场首页换商品入口"}, "params": {"type": "object", "required": [], "properties": {"good_id": {"type": "string", "description": "曝光时的商品id"}}}}, "required": ["action", "params"]}, "CLICK_EVENT__OP_ACTIVITY_SUM2025_MAIN__OP_ACTIVITY_GUID_BUTTON": {"type": "object", "properties": {"action": {"const": "OP_ACTIVITY_GUID_BUTTON", "description": "主页面导流button"}, "params": {"type": "object", "required": [], "properties": {"title": {"type": "string", "description": "按钮文案"}, "url": {"type": "string", "description": "无文案时上报url链接"}}}}, "required": ["action", "params"]}, "ELEMENT_SHOW_EVENT__OP_ACTIVITY_SUM2025_MAIN__OP_ACTIVITY_GUID_BUTTON": {"type": "object", "properties": {"action": {"const": "OP_ACTIVITY_GUID_BUTTON", "description": "主页面导流button"}, "params": {"type": "object", "required": [], "properties": {"title": {"type": "string", "description": "按钮文案"}, "url": {"type": "string", "description": "无文案时上报url链接"}}}}, "required": ["action", "params"]}, "CLICK_EVENT__OP_ACTIVITY_SUM2025_MAIN__OP_ACTIVITY_ICON_BUTTON_MORE": {"type": "object", "properties": {"action": {"const": "OP_ACTIVITY_ICON_BUTTON_MORE", "description": "更多里面的按钮点击"}, "params": {"type": "object", "required": [], "properties": {"title": {"type": "string", "description": "按钮文案"}}}}, "required": ["action", "params"]}, "CLICK_EVENT__OP_ACTIVITY_SUM2025_MAIN__OP_ACTIVITY_ICON_BUTTON": {"type": "object", "properties": {"action": {"const": "OP_ACTIVITY_ICON_BUTTON", "description": "顶部功能葫芦串"}, "params": {"type": "object", "required": [], "properties": {"title": {"type": "string", "description": "按钮文案"}}}}, "required": ["action", "params"]}}, "type": "object", "properties": {"application_info": {"type": "object", "properties": {"appName": {"const": "ACTIVITY_SUMMER2025"}, "groupIdList": {"const": [43370, 48276]}}}, "track_info": {"oneOf": [{"type": "object", "properties": {"event_type": {"const": "CLICK_EVENT"}, "page_info": {"$ref": "#/$defs/PAGE_SHOW_EVENT__OP_ACTIVITY_SUM2025_MAIN"}, "element_info": {"$ref": "#/$defs/CLICK_EVENT__OP_ACTIVITY_SUM2025_MAIN__OP_ACTIVITY_MAKE_TEAM_CARD"}}, "required": ["event_type", "page_info", "element_info"]}, {"type": "object", "properties": {"event_type": {"const": "ELEMENT_SHOW_EVENT"}, "page_info": {"$ref": "#/$defs/PAGE_SHOW_EVENT__OP_ACTIVITY_SUM2025_MAIN"}, "element_info": {"$ref": "#/$defs/ELEMENT_SHOW_EVENT__OP_ACTIVITY_SUM2025_MAIN__OP_ACTIVITY_MAKE_TEAM_CARD"}}, "required": ["event_type", "page_info", "element_info"]}, {"type": "object", "properties": {"event_type": {"const": "ELEMENT_SHOW_EVENT"}, "page_info": {"$ref": "#/$defs/PAGE_SHOW_EVENT__OP_ACTIVITY_SUM2025_MAIN"}, "element_info": {"$ref": "#/$defs/ELEMENT_SHOW_EVENT__OP_ACTIVITY_SUM2025_MAIN__OP_ACTIVITY_MAKE_TEAM_ICON"}}, "required": ["event_type", "page_info", "element_info"]}, {"type": "object", "properties": {"event_type": {"const": "CLICK_EVENT"}, "page_info": {"$ref": "#/$defs/PAGE_SHOW_EVENT__OP_ACTIVITY_SUM2025_MAIN"}, "element_info": {"$ref": "#/$defs/CLICK_EVENT__OP_ACTIVITY_SUM2025_MAIN__OP_ACTIVITY_MAKE_TEAM_GRID_POP"}}, "required": ["event_type", "page_info", "element_info"]}, {"type": "object", "properties": {"event_type": {"const": "ELEMENT_SHOW_EVENT"}, "page_info": {"$ref": "#/$defs/PAGE_SHOW_EVENT__OP_ACTIVITY_SUM2025_MAIN"}, "element_info": {"$ref": "#/$defs/ELEMENT_SHOW_EVENT__OP_ACTIVITY_SUM2025_MAIN__OP_ACTIVITY_MAKE_TEAM_GRID_POP"}}, "required": ["event_type", "page_info", "element_info"]}, {"type": "object", "properties": {"event_type": {"const": "CLICK_EVENT"}, "page_info": {"$ref": "#/$defs/PAGE_SHOW_EVENT__OP_ACTIVITY_SUM2025_MAIN"}, "element_info": {"$ref": "#/$defs/CLICK_EVENT__OP_ACTIVITY_SUM2025_MAIN__OP_ACTIVITY_MAKE_TEAM_POP"}}, "required": ["event_type", "page_info", "element_info"]}, {"type": "object", "properties": {"event_type": {"const": "ELEMENT_SHOW_EVENT"}, "page_info": {"$ref": "#/$defs/PAGE_SHOW_EVENT__OP_ACTIVITY_SUM2025_MAIN"}, "element_info": {"$ref": "#/$defs/ELEMENT_SHOW_EVENT__OP_ACTIVITY_SUM2025_MAIN__OP_ACTIVITY_MAKE_TEAM_POP"}}, "required": ["event_type", "page_info", "element_info"]}, {"type": "object", "properties": {"event_type": {"const": "CLICK_EVENT"}, "page_info": {"$ref": "#/$defs/PAGE_SHOW_EVENT__OP_ACTIVITY_SUM2025_MAIN"}, "element_info": {"$ref": "#/$defs/CLICK_EVENT__OP_ACTIVITY_SUM2025_MAIN__OP_ACTIVITY_MAKE_TEAM_GUID"}}, "required": ["event_type", "page_info", "element_info"]}, {"type": "object", "properties": {"event_type": {"const": "ELEMENT_SHOW_EVENT"}, "page_info": {"$ref": "#/$defs/PAGE_SHOW_EVENT__OP_ACTIVITY_SUM2025_MAIN"}, "element_info": {"$ref": "#/$defs/ELEMENT_SHOW_EVENT__OP_ACTIVITY_SUM2025_MAIN__OP_ACTIVITY_MAKE_TEAM_GUID"}}, "required": ["event_type", "page_info", "element_info"]}, {"type": "object", "properties": {"event_type": {"const": "CLICK_EVENT"}, "page_info": {"$ref": "#/$defs/PAGE_SHOW_EVENT__OP_ACTIVITY_SUM2025_MAIN"}, "element_info": {"$ref": "#/$defs/CLICK_EVENT__OP_ACTIVITY_SUM2025_MAIN__OP_ACTIVITY_REWARD_POP"}}, "required": ["event_type", "page_info", "element_info"]}, {"type": "object", "properties": {"event_type": {"const": "ELEMENT_SHOW_EVENT"}, "page_info": {"$ref": "#/$defs/PAGE_SHOW_EVENT__OP_ACTIVITY_SUM2025_MAIN"}, "element_info": {"$ref": "#/$defs/ELEMENT_SHOW_EVENT__OP_ACTIVITY_SUM2025_MAIN__OP_ACTIVITY_REWARD_POP"}}, "required": ["event_type", "page_info", "element_info"]}, {"type": "object", "properties": {"event_type": {"const": "CLICK_EVENT"}, "page_info": {"$ref": "#/$defs/PAGE_SHOW_EVENT__OP_ACTIVITY_SUM2025_MAIN"}, "element_info": {"$ref": "#/$defs/CLICK_EVENT__OP_ACTIVITY_SUM2025_MAIN__OP_ACTIVITY_JION_BUTTON"}}, "required": ["event_type", "page_info", "element_info"]}, {"type": "object", "properties": {"event_type": {"const": "ELEMENT_SHOW_EVENT"}, "page_info": {"$ref": "#/$defs/PAGE_SHOW_EVENT__OP_ACTIVITY_SUM2025_MAIN"}, "element_info": {"$ref": "#/$defs/ELEMENT_SHOW_EVENT__OP_ACTIVITY_SUM2025_MAIN__OP_ACTIVITY_JION_BUTTON"}}, "required": ["event_type", "page_info", "element_info"]}, {"type": "object", "properties": {"event_type": {"const": "CLICK_EVENT"}, "page_info": {"$ref": "#/$defs/PAGE_SHOW_EVENT__OP_ACTIVITY_SUM2025_MAIN"}, "element_info": {"$ref": "#/$defs/CLICK_EVENT__OP_ACTIVITY_SUM2025_MAIN__OP_ACTIVITY_REWARD_CHOOSE_POP"}}, "required": ["event_type", "page_info", "element_info"]}, {"type": "object", "properties": {"event_type": {"const": "ELEMENT_SHOW_EVENT"}, "page_info": {"$ref": "#/$defs/PAGE_SHOW_EVENT__OP_ACTIVITY_SUM2025_MAIN"}, "element_info": {"$ref": "#/$defs/ELEMENT_SHOW_EVENT__OP_ACTIVITY_SUM2025_MAIN__OP_ACTIVITY_REWARD_CHOOSE_POP"}}, "required": ["event_type", "page_info", "element_info"]}, {"type": "object", "properties": {"event_type": {"const": "PAGE_SHOW_EVENT"}, "page_info": {"$ref": "#/$defs/PAGE_SHOW_EVENT__OP_ACTIVITY_SUM2025_MAIN"}}, "required": ["event_type", "page_info", "element_info"]}, {"type": "object", "properties": {"event_type": {"const": "ELEMENT_SHOW_EVENT"}, "page_info": {"$ref": "#/$defs/PAGE_SHOW_EVENT__OP_ACTIVITY_SUM2025_MAIN"}, "element_info": {"$ref": "#/$defs/ELEMENT_SHOW_EVENT__OP_ACTIVITY_SUM2025_MAIN__OP_ACTIVITY_TASK_RESULT_POP__FORWARD"}}, "required": ["event_type", "page_info", "element_info"]}, {"type": "object", "properties": {"event_type": {"const": "ELEMENT_SHOW_EVENT"}, "page_info": {"$ref": "#/$defs/PAGE_SHOW_EVENT__OP_ACTIVITY_SUM2025_MAIN"}, "element_info": {"$ref": "#/$defs/ELEMENT_SHOW_EVENT__OP_ACTIVITY_SUM2025_MAIN__OP_ACTIVITY_TASK_ASSIGN_POP_FORWARD"}}, "required": ["event_type", "page_info", "element_info"]}, {"type": "object", "properties": {"event_type": {"const": "ELEMENT_SHOW_EVENT"}, "page_info": {"$ref": "#/$defs/PAGE_SHOW_EVENT__OP_ACTIVITY_SUM2025_MAIN"}, "element_info": {"$ref": "#/$defs/ELEMENT_SHOW_EVENT__OP_ACTIVITY_SUM2025_MAIN__OP_ACTIVITY_REWARD_POP_FORWARD"}}, "required": ["event_type", "page_info", "element_info"]}, {"type": "object", "properties": {"event_type": {"const": "ELEMENT_SHOW_EVENT"}, "page_info": {"$ref": "#/$defs/PAGE_SHOW_EVENT__OP_ACTIVITY_SUM2025_MAIN"}, "element_info": {"$ref": "#/$defs/ELEMENT_SHOW_EVENT__OP_ACTIVITY_SUM2025_MAIN__OP_ACTIVITY_BUSINESS_GRID_LOGO"}}, "required": ["event_type", "page_info", "element_info"]}, {"type": "object", "properties": {"event_type": {"const": "ELEMENT_SHOW_EVENT"}, "page_info": {"$ref": "#/$defs/PAGE_SHOW_EVENT__OP_ACTIVITY_SUM2025_MAIN"}, "element_info": {"$ref": "#/$defs/ELEMENT_SHOW_EVENT__OP_ACTIVITY_SUM2025_MAIN__OP_ACTIVITY_BUSINESS_BUILD_LOGO"}}, "required": ["event_type", "page_info", "element_info"]}, {"type": "object", "properties": {"event_type": {"const": "ELEMENT_SHOW_EVENT"}, "page_info": {"$ref": "#/$defs/PAGE_SHOW_EVENT__OP_ACTIVITY_SUM2025_MAIN"}, "element_info": {"$ref": "#/$defs/ELEMENT_SHOW_EVENT__OP_ACTIVITY_SUM2025_MAIN__OP_ACTIVITY_BUSINESS_LOGO"}}, "required": ["event_type", "page_info", "element_info"]}, {"type": "object", "properties": {"event_type": {"const": "CLICK_EVENT"}, "page_info": {"$ref": "#/$defs/PAGE_SHOW_EVENT__OP_ACTIVITY_SUM2025_MAIN"}, "element_info": {"$ref": "#/$defs/CLICK_EVENT__OP_ACTIVITY_SUM2025_MAIN__OP_ACTIVITY_MAKE_TEAM_SUCCESS"}}, "required": ["event_type", "page_info", "element_info"]}, {"type": "object", "properties": {"event_type": {"const": "CLICK_EVENT"}, "page_info": {"$ref": "#/$defs/PAGE_SHOW_EVENT__OP_ACTIVITY_SUM2025_MAIN"}, "element_info": {"$ref": "#/$defs/CLICK_EVENT__OP_ACTIVITY_SUM2025_MAIN__OP_ACTIVITY_MAKE_TEAM_SIGN_SUCCESS"}}, "required": ["event_type", "page_info", "element_info"]}, {"type": "object", "properties": {"event_type": {"const": "CLICK_EVENT"}, "page_info": {"$ref": "#/$defs/PAGE_SHOW_EVENT__OP_ACTIVITY_SUM2025_MAIN"}, "element_info": {"$ref": "#/$defs/CLICK_EVENT__OP_ACTIVITY_SUM2025_MAIN__OP_ACTIVITY_TEAM_BREAK"}}, "required": ["event_type", "page_info", "element_info"]}, {"type": "object", "properties": {"event_type": {"const": "ELEMENT_SHOW_EVENT"}, "page_info": {"$ref": "#/$defs/PAGE_SHOW_EVENT__OP_ACTIVITY_SUM2025_MAIN"}, "element_info": {"$ref": "#/$defs/ELEMENT_SHOW_EVENT__OP_ACTIVITY_SUM2025_MAIN__OP_ACTIVITY_TEAM_BREAK"}}, "required": ["event_type", "page_info", "element_info"]}, {"type": "object", "properties": {"event_type": {"const": "ELEMENT_SHOW_EVENT"}, "page_info": {"$ref": "#/$defs/PAGE_SHOW_EVENT__OP_ACTIVITY_SUM2025_MAIN"}, "element_info": {"$ref": "#/$defs/ELEMENT_SHOW_EVENT__OP_ACTIVITY_SUM2025_MAIN__OP_ACTIVITY_MAKE_TEAM_SIGN_SUCCESS"}}, "required": ["event_type", "page_info", "element_info"]}, {"type": "object", "properties": {"event_type": {"const": "ELEMENT_SHOW_EVENT"}, "page_info": {"$ref": "#/$defs/PAGE_SHOW_EVENT__OP_ACTIVITY_SUM2025_MAIN"}, "element_info": {"$ref": "#/$defs/ELEMENT_SHOW_EVENT__OP_ACTIVITY_SUM2025_MAIN__OP_ACTIVITY_MAKE_TEAM_SUCCESS"}}, "required": ["event_type", "page_info", "element_info"]}, {"type": "object", "properties": {"event_type": {"const": "CLICK_EVENT"}, "page_info": {"$ref": "#/$defs/PAGE_SHOW_EVENT__OP_ACTIVITY_SUM2025_MAIN"}, "element_info": {"$ref": "#/$defs/CLICK_EVENT__OP_ACTIVITY_SUM2025_MAIN__OP_ACTIVITY_REWARD_CHOOSE_RESULT"}}, "required": ["event_type", "page_info", "element_info"]}, {"type": "object", "properties": {"event_type": {"const": "CLICK_EVENT"}, "page_info": {"$ref": "#/$defs/PAGE_SHOW_EVENT__OP_ACTIVITY_SUM2025_MAIN"}, "element_info": {"$ref": "#/$defs/CLICK_EVENT__OP_ACTIVITY_SUM2025_MAIN__OP_ACTIVITY_PUSH_OPEN_GUIDE_IS_OPEN_RESULT"}}, "required": ["event_type", "page_info", "element_info"]}, {"type": "object", "properties": {"event_type": {"const": "CLICK_EVENT"}, "page_info": {"$ref": "#/$defs/PAGE_SHOW_EVENT__OP_ACTIVITY_SUM2025_MAIN"}, "element_info": {"$ref": "#/$defs/CLICK_EVENT__OP_ACTIVITY_SUM2025_MAIN__OP_ACTIVITY_PUSH_OPEN_GUIDE_CNY_POPUP"}}, "required": ["event_type", "page_info", "element_info"]}, {"type": "object", "properties": {"event_type": {"const": "ELEMENT_SHOW_EVENT"}, "page_info": {"$ref": "#/$defs/PAGE_SHOW_EVENT__OP_ACTIVITY_SUM2025_MAIN"}, "element_info": {"$ref": "#/$defs/ELEMENT_SHOW_EVENT__OP_ACTIVITY_SUM2025_MAIN__OP_ACTIVITY_PUSH_OPEN_GUIDE_CNY_POPUP"}}, "required": ["event_type", "page_info", "element_info"]}, {"type": "object", "properties": {"event_type": {"const": "CLICK_EVENT"}, "page_info": {"$ref": "#/$defs/PAGE_SHOW_EVENT__OP_ACTIVITY_SUM2025_MAIN"}, "element_info": {"$ref": "#/$defs/CLICK_EVENT__OP_ACTIVITY_SUM2025_MAIN__OP_ACTIVITY_CORE_POP"}}, "required": ["event_type", "page_info", "element_info"]}, {"type": "object", "properties": {"event_type": {"const": "ELEMENT_SHOW_EVENT"}, "page_info": {"$ref": "#/$defs/PAGE_SHOW_EVENT__OP_ACTIVITY_SUM2025_MAIN"}, "element_info": {"$ref": "#/$defs/ELEMENT_SHOW_EVENT__OP_ACTIVITY_SUM2025_MAIN__OP_ACTIVITY_CORE_POP"}}, "required": ["event_type", "page_info", "element_info"]}, {"type": "object", "properties": {"event_type": {"const": "CLICK_EVENT"}, "page_info": {"$ref": "#/$defs/PAGE_SHOW_EVENT__OP_ACTIVITY_SUM2025_MAIN"}, "element_info": {"$ref": "#/$defs/CLICK_EVENT__OP_ACTIVITY_SUM2025_MAIN__OP_ACTIVITY_TASK_RESULT_POP"}}, "required": ["event_type", "page_info", "element_info"]}, {"type": "object", "properties": {"event_type": {"const": "ELEMENT_SHOW_EVENT"}, "page_info": {"$ref": "#/$defs/PAGE_SHOW_EVENT__OP_ACTIVITY_SUM2025_MAIN"}, "element_info": {"$ref": "#/$defs/ELEMENT_SHOW_EVENT__OP_ACTIVITY_SUM2025_MAIN__OP_ACTIVITY_TASK_RESULT_POP"}}, "required": ["event_type", "page_info", "element_info"]}, {"type": "object", "properties": {"event_type": {"const": "CLICK_EVENT"}, "page_info": {"$ref": "#/$defs/PAGE_SHOW_EVENT__OP_ACTIVITY_SUM2025_MAIN"}, "element_info": {"$ref": "#/$defs/CLICK_EVENT__OP_ACTIVITY_SUM2025_MAIN__OP_ACTIVITY_TASK_ASSIGN_POP"}}, "required": ["event_type", "page_info", "element_info"]}, {"type": "object", "properties": {"event_type": {"const": "ELEMENT_SHOW_EVENT"}, "page_info": {"$ref": "#/$defs/PAGE_SHOW_EVENT__OP_ACTIVITY_SUM2025_MAIN"}, "element_info": {"$ref": "#/$defs/ELEMENT_SHOW_EVENT__OP_ACTIVITY_SUM2025_MAIN__OP_ACTIVITY_TASK_ASSIGN_POP"}}, "required": ["event_type", "page_info", "element_info"]}, {"type": "object", "properties": {"event_type": {"const": "CLICK_EVENT"}, "page_info": {"$ref": "#/$defs/PAGE_SHOW_EVENT__OP_ACTIVITY_SUM2025_MAIN"}, "element_info": {"$ref": "#/$defs/CLICK_EVENT__OP_ACTIVITY_SUM2025_MAIN__OP_ACTIVITY_DRECT_CARD"}}, "required": ["event_type", "page_info", "element_info"]}, {"type": "object", "properties": {"event_type": {"const": "ELEMENT_SHOW_EVENT"}, "page_info": {"$ref": "#/$defs/PAGE_SHOW_EVENT__OP_ACTIVITY_SUM2025_MAIN"}, "element_info": {"$ref": "#/$defs/ELEMENT_SHOW_EVENT__OP_ACTIVITY_SUM2025_MAIN__OP_ACTIVITY_DRECT_CARD"}}, "required": ["event_type", "page_info", "element_info"]}, {"type": "object", "properties": {"event_type": {"const": "CLICK_EVENT"}, "page_info": {"$ref": "#/$defs/PAGE_SHOW_EVENT__OP_ACTIVITY_SUM2025_MAIN"}, "element_info": {"$ref": "#/$defs/CLICK_EVENT__OP_ACTIVITY_SUM2025_MAIN__OP_ACTIVITY_MAKE_TEAM_BUTTON"}}, "required": ["event_type", "page_info", "element_info"]}, {"type": "object", "properties": {"event_type": {"const": "ELEMENT_SHOW_EVENT"}, "page_info": {"$ref": "#/$defs/PAGE_SHOW_EVENT__OP_ACTIVITY_SUM2025_MAIN"}, "element_info": {"$ref": "#/$defs/ELEMENT_SHOW_EVENT__OP_ACTIVITY_SUM2025_MAIN__OP_ACTIVITY_MAKE_TEAM_BUTTON"}}, "required": ["event_type", "page_info", "element_info"]}, {"type": "object", "properties": {"event_type": {"const": "CLICK_EVENT"}, "page_info": {"$ref": "#/$defs/PAGE_SHOW_EVENT__OP_ACTIVITY_SUM2025_MAIN"}, "element_info": {"$ref": "#/$defs/CLICK_EVENT__OP_ACTIVITY_SUM2025_MAIN__OP_ACTIVITY_TASK_ITEM"}}, "required": ["event_type", "page_info", "element_info"]}, {"type": "object", "properties": {"event_type": {"const": "ELEMENT_SHOW_EVENT"}, "page_info": {"$ref": "#/$defs/PAGE_SHOW_EVENT__OP_ACTIVITY_SUM2025_MAIN"}, "element_info": {"$ref": "#/$defs/ELEMENT_SHOW_EVENT__OP_ACTIVITY_SUM2025_MAIN__OP_ACTIVITY_TASK_ITEM"}}, "required": ["event_type", "page_info", "element_info"]}, {"type": "object", "properties": {"event_type": {"const": "ELEMENT_SHOW_EVENT"}, "page_info": {"$ref": "#/$defs/PAGE_SHOW_EVENT__OP_ACTIVITY_SUM2025_MAIN"}, "element_info": {"$ref": "#/$defs/ELEMENT_SHOW_EVENT__OP_ACTIVITY_SUM2025_MAIN__OP_ACTIVITY_TASK_LIST_POP"}}, "required": ["event_type", "page_info", "element_info"]}, {"type": "object", "properties": {"event_type": {"const": "ELEMENT_SHOW_EVENT"}, "page_info": {"$ref": "#/$defs/PAGE_SHOW_EVENT__OP_ACTIVITY_SUM2025_MAIN"}, "element_info": {"$ref": "#/$defs/ELEMENT_SHOW_EVENT__OP_ACTIVITY_SUM2025_MAIN__OP_ACTIVITY_MAKE_TEAM_BUBBLE_REMIND_POP"}}, "required": ["event_type", "page_info", "element_info"]}, {"type": "object", "properties": {"event_type": {"const": "CLICK_EVENT"}, "page_info": {"$ref": "#/$defs/PAGE_SHOW_EVENT__OP_ACTIVITY_SUM2025_MAIN"}, "element_info": {"$ref": "#/$defs/CLICK_EVENT__OP_ACTIVITY_SUM2025_MAIN__OP_ACTIVITY_BUBBLE_REMIND_POP"}}, "required": ["event_type", "page_info", "element_info"]}, {"type": "object", "properties": {"event_type": {"const": "ELEMENT_SHOW_EVENT"}, "page_info": {"$ref": "#/$defs/PAGE_SHOW_EVENT__OP_ACTIVITY_SUM2025_MAIN"}, "element_info": {"$ref": "#/$defs/ELEMENT_SHOW_EVENT__OP_ACTIVITY_SUM2025_MAIN__OP_ACTIVITY_BUBBLE_REMIND_POP"}}, "required": ["event_type", "page_info", "element_info"]}, {"type": "object", "properties": {"event_type": {"const": "CLICK_EVENT"}, "page_info": {"$ref": "#/$defs/PAGE_SHOW_EVENT__OP_ACTIVITY_SUM2025_MAIN"}, "element_info": {"$ref": "#/$defs/CLICK_EVENT__OP_ACTIVITY_SUM2025_MAIN__OP_ACTIVITY_TASK_BUTTON"}}, "required": ["event_type", "page_info", "element_info"]}, {"type": "object", "properties": {"event_type": {"const": "ELEMENT_SHOW_EVENT"}, "page_info": {"$ref": "#/$defs/PAGE_SHOW_EVENT__OP_ACTIVITY_SUM2025_MAIN"}, "element_info": {"$ref": "#/$defs/ELEMENT_SHOW_EVENT__OP_ACTIVITY_SUM2025_MAIN__OP_ACTIVITY_SIGN_INFO_POP"}}, "required": ["event_type", "page_info", "element_info"]}, {"type": "object", "properties": {"event_type": {"const": "CLICK_EVENT"}, "page_info": {"$ref": "#/$defs/PAGE_SHOW_EVENT__OP_ACTIVITY_SUM2025_MAIN"}, "element_info": {"$ref": "#/$defs/CLICK_EVENT__OP_ACTIVITY_SUM2025_MAIN__OP_ACTIVITY_RE_SIGN_POP"}}, "required": ["event_type", "page_info", "element_info"]}, {"type": "object", "properties": {"event_type": {"const": "ELEMENT_SHOW_EVENT"}, "page_info": {"$ref": "#/$defs/PAGE_SHOW_EVENT__OP_ACTIVITY_SUM2025_MAIN"}, "element_info": {"$ref": "#/$defs/ELEMENT_SHOW_EVENT__OP_ACTIVITY_SUM2025_MAIN__OP_ACTIVITY_RE_SIGN_POP"}}, "required": ["event_type", "page_info", "element_info"]}, {"type": "object", "properties": {"event_type": {"const": "CLICK_EVENT"}, "page_info": {"$ref": "#/$defs/PAGE_SHOW_EVENT__OP_ACTIVITY_SUM2025_MAIN"}, "element_info": {"$ref": "#/$defs/CLICK_EVENT__OP_ACTIVITY_SUM2025_MAIN__OP_ACTIVITY_CALENDER_POP"}}, "required": ["event_type", "page_info", "element_info"]}, {"type": "object", "properties": {"event_type": {"const": "ELEMENT_SHOW_EVENT"}, "page_info": {"$ref": "#/$defs/PAGE_SHOW_EVENT__OP_ACTIVITY_SUM2025_MAIN"}, "element_info": {"$ref": "#/$defs/ELEMENT_SHOW_EVENT__OP_ACTIVITY_SUM2025_MAIN__OP_ACTIVITY_CALENDER_POP"}}, "required": ["event_type", "page_info", "element_info"]}, {"type": "object", "properties": {"event_type": {"const": "CLICK_EVENT"}, "page_info": {"$ref": "#/$defs/PAGE_SHOW_EVENT__OP_ACTIVITY_SUM2025_MAIN"}, "element_info": {"$ref": "#/$defs/CLICK_EVENT__OP_ACTIVITY_SUM2025_MAIN__OP_ACTIVITY_TIME_BAR"}}, "required": ["event_type", "page_info", "element_info"]}, {"type": "object", "properties": {"event_type": {"const": "ELEMENT_SHOW_EVENT"}, "page_info": {"$ref": "#/$defs/PAGE_SHOW_EVENT__OP_ACTIVITY_SUM2025_MAIN"}, "element_info": {"$ref": "#/$defs/ELEMENT_SHOW_EVENT__OP_ACTIVITY_SUM2025_MAIN__OP_ACTIVITY_TIME_BAR"}}, "required": ["event_type", "page_info", "element_info"]}, {"type": "object", "properties": {"event_type": {"const": "CLICK_EVENT"}, "page_info": {"$ref": "#/$defs/PAGE_SHOW_EVENT__OP_ACTIVITY_SUM2025_MAIN"}, "element_info": {"$ref": "#/$defs/CLICK_EVENT__OP_ACTIVITY_SUM2025_MAIN__OP_ACTIVITY_CHANGE_GOOD_ENTR"}}, "required": ["event_type", "page_info", "element_info"]}, {"type": "object", "properties": {"event_type": {"const": "ELEMENT_SHOW_EVENT"}, "page_info": {"$ref": "#/$defs/PAGE_SHOW_EVENT__OP_ACTIVITY_SUM2025_MAIN"}, "element_info": {"$ref": "#/$defs/ELEMENT_SHOW_EVENT__OP_ACTIVITY_SUM2025_MAIN__OP_ACTIVITY_CHANGE_GOOD_ENTR"}}, "required": ["event_type", "page_info", "element_info"]}, {"type": "object", "properties": {"event_type": {"const": "CLICK_EVENT"}, "page_info": {"$ref": "#/$defs/PAGE_SHOW_EVENT__OP_ACTIVITY_SUM2025_MAIN"}, "element_info": {"$ref": "#/$defs/CLICK_EVENT__OP_ACTIVITY_SUM2025_MAIN__OP_ACTIVITY_GUID_BUTTON"}}, "required": ["event_type", "page_info", "element_info"]}, {"type": "object", "properties": {"event_type": {"const": "ELEMENT_SHOW_EVENT"}, "page_info": {"$ref": "#/$defs/PAGE_SHOW_EVENT__OP_ACTIVITY_SUM2025_MAIN"}, "element_info": {"$ref": "#/$defs/ELEMENT_SHOW_EVENT__OP_ACTIVITY_SUM2025_MAIN__OP_ACTIVITY_GUID_BUTTON"}}, "required": ["event_type", "page_info", "element_info"]}, {"type": "object", "properties": {"event_type": {"const": "CLICK_EVENT"}, "page_info": {"$ref": "#/$defs/PAGE_SHOW_EVENT__OP_ACTIVITY_SUM2025_MAIN"}, "element_info": {"$ref": "#/$defs/CLICK_EVENT__OP_ACTIVITY_SUM2025_MAIN__OP_ACTIVITY_ICON_BUTTON_MORE"}}, "required": ["event_type", "page_info", "element_info"]}, {"type": "object", "properties": {"event_type": {"const": "CLICK_EVENT"}, "page_info": {"$ref": "#/$defs/PAGE_SHOW_EVENT__OP_ACTIVITY_SUM2025_MAIN"}, "element_info": {"$ref": "#/$defs/CLICK_EVENT__OP_ACTIVITY_SUM2025_MAIN__OP_ACTIVITY_ICON_BUTTON"}}, "required": ["event_type", "page_info", "element_info"]}]}}, "required": ["application_info", "track_info"]}