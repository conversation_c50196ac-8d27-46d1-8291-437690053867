import { createModel, useModel } from '@gundam/model';
import { useGuideState } from '@pet/25cny.guide-directive';
import type { Task } from '@pet/adapt.queue-api/main/TaskQueue';
import { toast } from '@pet/adapt.toast';
import { isNotNil, sleep } from '@pet/yau.core';
import useCaptureDebugLog from '@pet/yau.logger';
import { whenever } from '@vueuse/core';
import { invoke } from '@yoda/bridge';

import FullTransModal from '@/components/full-modals/FullTransModal.vue';
import FriendsTeamPopup from '@/components/popups/friends-team-popup/FriendsTeamPopup.vue';
import { useABTest } from '@/hooks/useABTest';
import { useLlwardAnimation, type LlwardAniName } from '@/models/llwardAnimation';
import {
    HugeSignInStatus,
    type ChessMoveResultView,
    type HugeSignInLLawdFollowPopupView,
    type SignInSelectProductListView,
    type LuckRushSudokuView,
    WishTravelPrizeType,
    PopupType,
    LinkTypeEnum,
    type TeamRecoFriendsCardPopupView,
} from '@/services/open-api-docs/home/<USER>/schemas';
import { BeginnerGuideStrategy } from '@/types/abTest';
import { reportKeyActionEnd, reportKeyActionStart } from '@/utils/log/keyActionLog';
import { getFinalAwdPreModalData } from '@/utils/popupTransform/fullModalTransform';
import { setLocalMapConfig } from '@/utils/ssg';

import { useABTestModel } from './abTestModel';
import { forwardRushApiModel } from './api/forwardRushApiModel';
import { taskApiModel } from './api/taskApiModel';
import { useBannerModel } from './bannerModel';
import { ForwardRushBtnStatus, useConfigModel, type IMainButtonConfig } from './configModel';
import { useForwardRushGridModel } from './forwardRushGridModel';
import { useForwardStationModel } from './forwardStationModel';
import { useGridTaskSheetModel } from './gridTaskSheetModel';
import { useHomeModel } from './homeModel';
import { useLocalCalendarModel } from './localCalendarModel';
import { usePopoverModel } from './popoverModel';
import { QUEUE_TAGS_TYPE, TaskType, usePopupModel } from './popup.model';
import { useSelectProductModel } from './selectProductModel';
import { useSignInModel } from './signInModel';
import { useSnapShotModel } from './snapShotModel';
import { useTaskModel } from './taskModel';
import { useNavigate } from './utils/useNavigate';

export const forwardRushBtnModel = createModel(({ getModelInstance }) => {
    // 是否允许点击向前冲
    const enableForwardRush = ref(true);
    // 第二天的花字提示与小手引导提示是否结束
    const showSecondDayTipFinished = ref(false);
    // 是否展示按钮上方的花字提示
    const showForwardRushTip = ref<boolean>(false);
    const forwardRushTip = ref<string>('');
    // 站点奖励动画
    const llwardAniController = useLlwardAnimation();

    const { openGridTaskSheet } = useGridTaskSheetModel();

    const { goForward, hasSetSignedTime } = useModel(forwardRushApiModel);
    const {
        rushCount,
        todaySigned,
        currentStep,
        targetStep,
        currentStepForProgress,
        targetStepForProgress,
        isArrived,
        isProgressSignedAnim,
    } = useSnapShotModel();
    const { signInStatus, mainButton, refreshHome, blockingRefreshHandler, homeData, localPrefix, isNoStep } =
        useHomeModel();
    const { kconfConfig, bubbleOptimize } = useConfigModel();
    const { openSheet, hasUnfinishedGetCountsTask, hasPushPermission, inpushTaskInfo, doTask, calendarTaskInfo } =
        useTaskModel();
    const { manager, isLastStation, showStationBubble, afterSelect, showBubbleAndAniAfterSelect, firstSigned } =
        useForwardRushGridModel();
    const {
        openPopup,
        createSummerPopup,
        addGroupTask,
        createPopupTask,
        openSummerPopup,
        alreadySignedToOpenPopup,
        addEventTask,
        validShowTaskPopup,
    } = usePopupModel();
    const { openResumeSignInPopup } = useSignInModel();
    const { openSelectProductSheet, choiceProductAnim } = useSelectProductModel();
    const { currentGuide } = useGuideState();
    const { log } = useCaptureDebugLog('forwardRushBtnModel');
    const { closeGridTaskSheet } = useGridTaskSheetModel();
    const { gCurrentStation } = useForwardStationModel();
    const { gotoCityPage } = useNavigate();
    const {
        beginnerGuideStrategy,
        enableEmphasizeLlwd,
        enableEmphasizeSelected,
        doubledDetailView,
        stepBubbleOptimize,
    } = useABTest();
    const {
        showFirstDayExperimentPop,
        hasBeginnerGuide,
        hasFirstSelectedProduct,
        isLoin,
        exp0714MainButtonSubTitle,
        exp0714MainButtonTitle,
        isCalendarTask,
        isInpushTask,
    } = useABTestModel();
    const {
        openBannerTextPopup,
        openChallengeNullBanner,
        openChallengeSuccessBanner,
        openBannerPopup,
        openTeamTextPopup,
    } = useBannerModel();
    const { clickedFlag, checkAndAddCalendar } = useLocalCalendarModel();

    /** 主按钮的kconf配置 */
    const mainButtonConfig = computed<IMainButtonConfig>(() => kconfConfig.value?.mainButtonConfig ?? {});
    /** 花字的kconf配置 */
    const flowerTip = computed(() => kconfConfig.value?.flowerTip ?? {});
    /** 是否是第一天 */
    const isFirstDay = computed(() => mainButton.value?.stationDayIndex === 1);
    // 站点的step
    const signedStep = computed(() => {
        const steps = [targetStep.value];
        !isFirstDay.value && steps.unshift(0);
        return steps;
    });
    /** 是否是第二天 */
    const isSecondDay = computed<boolean>(() => mainButton.value?.stationDayIndex === 2);
    /** 今天是哪天 */
    const todayIndex = computed(() => mainButton.value?.stationDayIndex);

    /** 系统赠送次数 */
    const systemGivingRushChance = computed<number>(() => mainButton.value?.systemGivingRushChance ?? 0);

    /** 进度百分比 */
    const progress = computed(() => {
        return targetStepForProgress.value <= 0 ? '0%' : (currentStepForProgress.value / targetStep.value) * 100 + '%';
    });
    /** 差的步数 */
    const stepCount = computed(() => {
        return Math.max(0, targetStepForProgress.value - currentStepForProgress.value);
    });
    /** 是否命中可以展示城市二级页开关放量  */
    const showCityPage = computed(() => homeData.value?.cityButtonView?.show ?? false);
    /** 是否完成城市二级页 */
    const cityCompleted = computed(() => homeData.value?.cityButtonView?.cityCompleted ?? false);
    /** 是否需要强化大奖引导 */
    const isBiggerGuide = computed(
        () =>
            isFirstDay.value &&
            currentStepForProgress.value === 0 &&
            beginnerGuideStrategy.value === BeginnerGuideStrategy.ThirdTest,
    );

    /**
     * 第一天的剧本逻辑
     * 1. 点击向前冲按钮，新手引导蒙层渐变退场
     * 2. 游戏任务向前跳跃1步，抵达站点后出现打卡成功花字
     * 3. 自动打卡选品弹窗
     */
    const showBeginPopup = ref(false);

    /** 主按钮状态 */
    // eslint-disable-next-line sonarjs/cognitive-complexity
    const mainButtonStatus = computed(() => {
        if (signInStatus.value === HugeSignInStatus.INTERCEPTED) {
            return ForwardRushBtnStatus.SIGN_INTERCEPTED;
        }
        if (signInStatus.value === HugeSignInStatus.COMPLETED || todaySigned.value) {
            if (todaySigned.value && !hasSetSignedTime.value) {
                setLocalMapConfig(`${localPrefix}latest-signed-time`, new Date().toString());
            }
            if (
                todaySigned.value &&
                enableEmphasizeLlwd.value &&
                (isCalendarTask.value || isInpushTask.value || exp0714MainButtonTitle.value)
            ) {
                return ForwardRushBtnStatus.FIRST_DAY_SIGNED_EXPERIMENT;
            }
            return ForwardRushBtnStatus.SIGNED;
        }
        if (signInStatus.value === HugeSignInStatus.PROCESSING) {
            if (mainButton.value?.hasValidSignFreeCard) {
                return ForwardRushBtnStatus.SIGN_FREE;
            }
            if (hasBeginnerGuide.value && !hasFirstSelectedProduct.value && enableEmphasizeSelected.value) {
                return ForwardRushBtnStatus.FIRST_DAY_UN_SIGNED_EXPERIMENT;
            }
            return ForwardRushBtnStatus.UN_SIGN;
        }
        return ForwardRushBtnStatus.OTHER;
    });

    const exp0714Toast = computed(() => {
        const data = mainButtonConfig.value?.[ForwardRushBtnStatus.FIRST_DAY_SIGNED_EXPERIMENT];
        return isLoin.value ? (data.firstDayToast ?? '') : (data.firstDayPacketToast ?? '');
    });
    const showButtonOptimizeText = computed(() => isFirstDay.value && currentStep.value === 0);

    /** 主按钮和气泡信息 */
    // eslint-disable-next-line sonarjs/cognitive-complexity
    const buttonAndBubbleInfo = computed(() => {
        const data = isBiggerGuide.value
            ? mainButtonConfig.value[ForwardRushBtnStatus.FIRST_DAY]
            : (mainButtonConfig.value[mainButtonStatus.value] ?? {});
        let buttonText = '';
        let buttonSubText = data.buttonSubText ?? '';
        let bubbleText = '';
        let bubbleSubText = '';
        if (showCityPage.value && !enableEmphasizeLlwd.value) {
            buttonText =
                mainButtonStatus.value === ForwardRushBtnStatus.SIGNED
                    ? (data.cityButtonText ?? '')
                    : (data.buttonText ?? '');
            bubbleText =
                mainButtonStatus.value === ForwardRushBtnStatus.SIGNED
                    ? (homeData.value?.cityButtonView?.bubbleText ?? '')
                    : (data.bubbleText ?? '');
            bubbleSubText =
                mainButtonStatus.value === ForwardRushBtnStatus.SIGNED
                    ? (homeData.value?.cityButtonView?.bubbleSubText ?? '')
                    : (data.bubbleText ?? '');
        } else {
            buttonText =
                mainButtonStatus.value === ForwardRushBtnStatus.SIGNED
                    ? (data.newButtonText ?? '')
                    : (data.buttonText ?? '');
            if (mainButtonStatus.value === ForwardRushBtnStatus.FIRST_DAY_SIGNED_EXPERIMENT) {
                buttonText =
                    isCalendarTask.value || isInpushTask.value
                        ? (data.firstDayRemindText ?? '')
                        : exp0714MainButtonTitle.value;
                if (isCalendarTask.value) {
                    const loin: number = calendarTaskInfo.value?.taskInfo?.profit?.count ?? 0;
                    buttonSubText = loin > 0 && !calendarTaskInfo.value?.finished ? `再得${loin}金币` : '';
                } else if (isInpushTask.value) {
                    const loin: number = inpushTaskInfo.value?.taskInfo?.profit?.count ?? 0;
                    buttonSubText = loin > 0 && !inpushTaskInfo.value?.finished ? `再得${loin}金币` : '';
                } else {
                    buttonSubText = exp0714MainButtonSubTitle.value ?? '';
                }
            }
            bubbleText = data.bubbleText ?? '';
            bubbleSubText = data.bubbleSubText ?? '';
            if (stepBubbleOptimize.value) {
                if ([ForwardRushBtnStatus.UN_SIGN].includes(mainButtonStatus.value)) {
                    bubbleText = bubbleOptimize.value?.bubbleText ?? '';
                }
                if (
                    showButtonOptimizeText.value &&
                    mainButtonStatus.value !== ForwardRushBtnStatus.FIRST_DAY_UN_SIGNED_EXPERIMENT
                ) {
                    buttonText = bubbleOptimize.value?.buttonTextOptimize ?? '';
                }
            }
        }
        return {
            /** 主按钮文案 */
            buttonText,
            /** 主按钮副文案 */
            buttonSubText,
            // todo 后端下发逛城市的气泡文案
            /** 气泡第一行文案 */
            bubbleText,
            /** 气泡第二行文案 */
            bubbleSubText,
            /** 气泡展示时长 */
            duration: !todaySigned.value ? 0 : (data.duration ?? 3000),
        };
    });

    const whenStatusChanged = ref(false);

    watch(
        () => mainButtonStatus.value,
        (val, oldVal) => {
            if (showCityPage.value && !enableEmphasizeLlwd.value) {
                // 城市二级页修改
                whenStatusChanged.value = true;
                setTimeout(() => {
                    whenStatusChanged.value = false;
                }, 200);
            } else {
                // 原逻辑
                if (val !== ForwardRushBtnStatus.SIGNED && val !== oldVal) {
                    whenStatusChanged.value = true;
                    setTimeout(() => {
                        whenStatusChanged.value = false;
                    }, 200);
                }
            }
        },
    );

    const { showCardPopover, showTeamPopover } = usePopoverModel();
    /** 持续展示向前冲气泡的条件 */
    const keepForwardBubbleShow = computed(() => {
        /** 组队气泡是否展示 */
        const baseCondition1 = !showCardPopover.value && !showTeamPopover.value;
        const baseCondition2 = mainButtonStatus.value !== ForwardRushBtnStatus.OTHER && !showForwardRushTip.value;
        /** 是否展示城市二级页，不展示为原逻辑 */
        const baseCondition3 =
            showCityPage.value && !enableEmphasizeLlwd.value
                ? !cityCompleted.value
                : (!todaySigned.value &&
                      mainButtonStatus.value !== ForwardRushBtnStatus.FIRST_DAY_UN_SIGNED_EXPERIMENT) ||
                  (mainButtonStatus.value === ForwardRushBtnStatus.FIRST_DAY_SIGNED_EXPERIMENT &&
                      !!doubledDetailView.value);
        const baseCondition = baseCondition1 && baseCondition2 && baseCondition3;
        /** 断签或者有免任务卡时始终展示 */
        const condition1 = [
            ForwardRushBtnStatus.FIRST_DAY_SIGNED_EXPERIMENT,
            ForwardRushBtnStatus.SIGN_INTERCEPTED,
            ForwardRushBtnStatus.SIGN_FREE,
        ].includes(mainButtonStatus.value);
        /** 不是第一天 */
        const condition2 =
            isNotNil(mainButton.value) &&
            !(isSecondDay.value && !showSecondDayTipFinished.value && systemGivingRushChance.value > 0) &&
            !(isLastStation.value && stepCount.value <= 1);
        return baseCondition && (condition1 || condition2);
    });

    const showForwardRushBubble = computed(() => {
        return keepForwardBubbleShow.value && !whenStatusChanged.value;
    });

    /** 第100天，最后一步了 */
    watch(
        (): [boolean, number] => [isLastStation.value, stepCount.value],
        async ([isLastStation, stepCount]) => {
            if (isLastStation && stepCount === 1) {
                await sleep(100);
                forwardRushTip.value = flowerTip.value.day100 ?? '';
                showForwardRushTip.value = !!forwardRushTip.value;
            }
        },
        { immediate: true },
    );

    whenever(
        homeData,
        () => {
            afterSelect.value = false;
            firstSigned.value = !!homeData.value?.todaySigned;
        },
        {
            immediate: true,
        },
    );

    /**
     * 打卡站点，站点弹窗弹完后，执行了一些打卡成功的动画
     */
    async function playAniWhenPopupClosed() {
        if (!isFirstDay.value) {
            isProgressSignedAnim.value = true;
            // 站点动画
            showStationBubble.value = true;
        }
    }

    const llwardGridName = computed(() => {
        return manager.value?.renderMap.ip.llwardGridName ?? null;
    });

    const showGridPopup = ref(false);

    const inFirstDayProcessing = ref(false);

    // 3. inpush 任务弹窗
    const showInpushPopup = async (data: ChessMoveResultView) => {
        const inpushTaskPopup = data?.subscribeInpushPopupView?.[0];
        if (isNotNil(inpushTaskPopup)) {
            if (choiceProductAnim.play) {
                log('[首日打卡] 等待选品动效 800ms');
                // 等待选品动效执行完成后，展示 inpush 任务弹窗
                await sleep(800);
            }
            log('[首日打卡] 订阅任务弹窗');
            // 如果有选品动效，等待选品动效执行完成
            const inpushTask = openSummerPopup(inpushTaskPopup, {
                taskType: TaskType.ACTIVE_POPUP,
            });
            await inpushTask?.end;
            log('[首日打卡] 订阅任务弹窗 end');
        }
    };
    // eslint-disable-next-line sonarjs/cognitive-complexity
    async function handleForwardRushProgress(data: ChessMoveResultView, isSignedFree = false) {
        /** 更新进度 */
        if (isNotNil(data?.progress?.currentStep) && isNotNil(data?.progress?.expectTotalStep)) {
            currentStep.value = data?.progress.currentStep;
            targetStep.value = data?.progress.expectTotalStep;
        }
        // 是否已经签到了
        const hasSigned = isSignedFree ? true : !!data?.progress?.signed;
        if (isFirstDay.value && hasSigned) {
            if (!enableEmphasizeLlwd.value) {
                todaySigned.value = true;
                enableForwardRush.value = true;
                isProgressSignedAnim.value = true;
            }
            await openBannerTextPopup();
        }
        // 非站点
        const showNormalGridModel = !hasSigned;
        // 第一站点
        const showFirstSignedModal = hasSigned && isFirstDay.value;
        // 非第一站 & 非最后一站 & 打卡了，展示奖励弹窗
        const showNormalSignedModal = hasSigned && !isFirstDay.value && !isLastStation.value;
        // 最后一站打卡了
        const showFinalSignedModal = hasSigned && isLastStation.value;
        const luckRushSudokuView = data?.luckRushSudokuView;
        const teamGridDegrade = data?.progress?.teamGridDegrade;
        if (teamGridDegrade) {
            openTeamTextPopup();
            enableForwardRush.value = true;
            return;
        }
        if (!(isNotNil(luckRushSudokuView?.[0]) && luckRushSudokuView?.length > 0)) {
            log(`[forward rush]当前格子没有奖励数据${JSON.stringify(luckRushSudokuView ?? [])}`);
            enableForwardRush.value = true;
            return;
        }
        // 普通格子奖励
        if (showNormalGridModel) {
            /**
             * 新增组队格子任务
             * 任务已经完成 降级为花字提示
             */
            const popupData = luckRushSudokuView[0];
            if (popupData.popupType === PopupType.TEAM_GRID_RECO_FRIENDS_CARD) {
                const { sponsorLogo, mainButton, friendCardViews } = popupData as any;
                openPopup({
                    component: FriendsTeamPopup,
                    data: {
                        popupType: PopupType.TEAM_GRID_RECO_FRIENDS_CARD,
                        sponsorLogo,
                        mainButtonText: mainButton?.linkText ?? '喊Ta组队',
                        friendCards: friendCardViews ?? [],
                    },
                    options: {
                        // 必须填，用来关闭弹窗
                        name: TaskType.TEAM_GRID_POPUP,
                    },
                });
                enableForwardRush.value = true;
                return;
            }
            showGridPopup.value = true;
            const validPopup = await validShowTaskPopup(popupData.popupType);
            // 下发权限通知弹窗时，如果开启了通知就不执行这个推送弹窗展示的逻辑
            if (
                (popupData.popupType === PopupType.LS_PUSH_SWITCH_LLCN_TASK && hasPushPermission.value) ||
                !validPopup
            ) {
                enableForwardRush.value = true;
                return;
            }
            // 获取奖励类型，用来处理飞入钱包的动画
            const luckRushSudokuTask = openSummerPopup(popupData, {
                taskType: TaskType.ACTIVE_POPUP,
            });
            luckRushSudokuTask?.start?.finally(() => {
                sleep(200).finally(() => {
                    isArrived.value = hasSigned;
                });
            });
            let isClickContinueRush = false;
            await luckRushSudokuTask?.end?.then((res: any) => {
                isClickContinueRush =
                    res?.event === 'mainClick' && res?.data?.mainButton?.linkType === LinkTypeEnum.LUCK_SHAKE_SUDOKU;
                log('[forward rush]普通格子弹窗关闭, 是否点击了继续冲', isClickContinueRush);
            });
            if (!isClickContinueRush && llwardGridName.value) {
                llwardAniController.startAni(llwardGridName.value as LlwardAniName);
                await sleep(1000);
            }
            // @ts-expect-error
            if (!!popupData.showInTaskList) {
                getModelInstance(taskApiModel)?.tasksRefetch();
            }
            !isClickContinueRush && (enableForwardRush.value = true);
            showGridPopup.value = false;
            return;
        }

        if (showFirstSignedModal) {
            if (luckRushSudokuView.length === 0) {
                inFirstDayProcessing.value = false;
                return;
            }
            /**
             * 暑期迭代0714：首日打卡成功后
             * 1. 不展示打卡进度弹窗和选品弹窗
             * 2. 不触发次日金币气泡和开启提醒任务
             */
            if (enableEmphasizeLlwd.value) {
                await showFirstDayExperimentPop(luckRushSudokuView);
                inFirstDayProcessing.value = false;
                return;
            }
            // 首日打卡展示成就弹窗->选品弹窗->inpush任务弹窗
            // 为兼容历史离线包老逻辑（仅有选品弹窗），数组顺序始终保持选品弹窗在首位；新前端代码需自行控制弹窗展示顺序
            log(`[首日打卡] 弹窗数量：${luckRushSudokuView.length}, ${data.subscribeInpushPopupView?.length || 0}`);
            // 1. 成就弹窗
            const signedPopup = luckRushSudokuView.filter((item) => item.popupType === PopupType.HUGE_SIGN_IN_LLAWD)[0];
            if (Boolean(signedPopup)) {
                log('[首日打卡] 成就弹窗');
                // 如果是先选品再打卡，会出现打卡结算弹窗
                const luckRushSudokuTask = openSummerPopup(signedPopup, {
                    taskType: TaskType.ACTIVE_POPUP,
                });
                luckRushSudokuTask?.start?.finally(async () => {
                    await sleep(200);
                    isArrived.value = hasSigned;
                    showStationBubble.value = false;
                });
                await luckRushSudokuTask?.end;
                log('[首日打卡] 成就弹窗 end');
            }
            // 2. 选品弹窗
            const selectProductPopup = luckRushSudokuView.filter(
                (item) => item.popupType === PopupType.HUGE_SIGN_IN_PRODUCT,
            )[0];

            const group = ref<Task | null>();
            if (
                selectProductPopup?.popupType === PopupType.HUGE_SIGN_IN_PRODUCT &&
                isNotNil(selectProductPopup?.selectProductListView)
            ) {
                log('[首日打卡] 选品弹窗');
                sleep(400).finally(() => {
                    isArrived.value = hasSigned;
                    showStationBubble.value = true;
                });
                group.value = await openSelectProductSheet({
                    popup: selectProductPopup.selectProductListView as SignInSelectProductListView,
                    topTitle: selectProductPopup.title ?? '',
                    logSource: 'sign',
                });
                // 3. 选品弹窗彻底关闭后，打开 inpush 任务弹窗
                await group.value?.end?.finally(async () => {
                    log('[首日打卡] 选品弹窗 end');
                    await showInpushPopup(data);
                });
            } else {
                // 3. 换品后无选品弹窗，打开 inpush 任务弹窗
                await showInpushPopup(data);
                afterSelect.value = true;
            }
            inFirstDayProcessing.value = false;
            log('[首日打卡] 按钮已解锁');
            return;
        }
        // 普通站点奖励
        if (showNormalSignedModal) {
            // 出现过弹窗数据下发不对的情况，这里记录一个日志
            log('[forward rush]打卡普通站点', JSON.stringify(luckRushSudokuView));
            const luckRushSudokuType = (luckRushSudokuView[0] as LuckRushSudokuView)?.llpeDetail?.[0]
                ?.llpeType as WishTravelPrizeType;
            const index = luckRushSudokuView.findIndex((item) => item?.popupType === PopupType.HUGE_SIGN_IN_LLAWD);
            // 在金币动画执行前需要先弹的弹窗
            const taskPopListBeforeCoinAni = luckRushSudokuView.slice(0, index + 1).map((item) => {
                return createSummerPopup(item as any, {
                    taskType: TaskType.ACTIVE_POPUP,
                });
            });
            // 在金币动画执行后需要弹的弹窗
            const taskPopListAfterCoinAni = luckRushSudokuView.slice(index + 1).map((item) => {
                return createSummerPopup(item as any, {
                    taskType: TaskType.ACTIVE_POPUP,
                });
            });
            const groupTask = addGroupTask({
                options: {
                    name: TaskType.GROUP_TASK,
                },
                tasks: taskPopListBeforeCoinAni as Array<any>,
            });
            sleep(200).finally(() => {
                isArrived.value = hasSigned;
            });
            await groupTask.end;
            if (isNotNil(luckRushSudokuType)) {
                await llwardAniController.startAni(luckRushSudokuType as LlwardAniName);
            }
            if (taskPopListAfterCoinAni.length > 0) {
                const groupTaskSecond = addGroupTask({
                    options: {
                        name: TaskType.GROUP_TASK,
                    },
                    tasks: taskPopListAfterCoinAni as Array<any>,
                });
                await groupTaskSecond.end;
            }
            return;
        }
        // 最终站点奖励
        if (showFinalSignedModal) {
            const popupData = luckRushSudokuView[0] as HugeSignInLLawdFollowPopupView;
            const data = getFinalAwdPreModalData(popupData);
            const finalTransPopup = createPopupTask({
                component: FullTransModal,
                data,
                options: {
                    name: TaskType.ACTIVE_POPUP,
                },
            });
            // 成就弹窗
            const signedPopup = createSummerPopup(popupData, {
                taskType: TaskType.ACTIVE_POPUP,
            });
            sleep(200).finally(() => {
                isArrived.value = hasSigned;
            });
            // 添加组任务到队列
            const group = addGroupTask({
                options: {
                    name: TaskType.GROUP_TASK,
                },
                tasks: [finalTransPopup, signedPopup as any],
            });
            await group.end;
            return;
        }
        enableForwardRush.value = true;
        return;
    }

    /**
     * 处理使用免签卡的逻辑
     * @param data 向前冲接口返回的数据
     */
    async function handleForwardRushWithSignFreeCard(data: ChessMoveResultView) {
        // eslint-disable-next-line no-underscore-dangle
        currentGuide.value?.el?.__destroyGuide?.();
        const task = openBannerPopup();
        task.start.finally(async () => {
            sleep(400).finally(async () => {
                await manager.value?.moveToImmediately(data?.progress?.currentStep ?? 0);
            });
        });
        await task.end;
        await handleForwardRushProgress(data, true);
    }
    async function handleForwardRushWithoutSignFreeCard(data: ChessMoveResultView) {
        /** 扣除消耗的步数 */
        rushCount.value = isNoStep.value ? Math.max(1, rushCount.value) : Math.max(0, rushCount.value - 1);
        /** 触发向前走格子 */
        await manager.value?.moveTo(data?.progress?.currentStep ?? 0);
        await handleForwardRushProgress(data);
    }

    // 气泡展示进度条
    const showBubbleProgress = computed(() => {
        return !isFirstDay.value && mainButtonStatus.value === ForwardRushBtnStatus.UN_SIGN && !todaySigned.value;
    });

    /**
     * 处理点击按钮触发向前冲的逻辑
     * @param useSignFreeCard 是否使用免签卡
     * @param forceRush 点击弹窗上的「做任务得步数」按钮时，enableForwardRush为false，设置forceRush来强制执行向前冲的逻辑
     * @returns
     */
    // eslint-disable-next-line sonarjs/cognitive-complexity
    async function handleForwardRush(useSignFreeCard = false, forceRush = false) {
        if (!enableForwardRush.value && !forceRush) {
            return;
        }
        // 关键动作名称
        const keyActionName = forceRush
            ? 'forward_rush_by_continue'
            : useSignFreeCard
              ? 'forward_rush_by_free_card'
              : 'forward_rush_normal';
        // 关键动作上报--开始
        reportKeyActionStart({
            name: keyActionName,
            extra_info: {
                useSignFreeCard,
                forceRush,
                enableForwardRush,
            },
        });
        log(
            `[forward rush]开始向前冲,是否使用免签卡:${useSignFreeCard};是否点击了继续冲:${forceRush};当前是否可点击按钮: ${enableForwardRush.value}`,
        );
        blockingRefreshHandler(true);
        // 向前冲加锁
        enableForwardRush.value = false;
        const { success, data, needRefresh } = await goForward(useSignFreeCard);
        if (!success) {
            enableForwardRush.value = true;
            blockingRefreshHandler(false);
            // 后端返回异常，但是打卡可能是成功的，这里补充一下主接口刷新
            needRefresh && refreshHome();
            log(`[forward rush]move接口请求异常, ${JSON.stringify(data ?? {})}`);
            return;
        }
        /** 等新手引导弹窗关闭后再执行向前冲逻辑 */
        if (isFirstDay.value) {
            inFirstDayProcessing.value = true;
            if (showBeginPopup.value) {
                await sleep(333);
            }
        }
        // 使用免签卡
        if (useSignFreeCard) {
            await handleForwardRushWithSignFreeCard(data!);
        } else {
            if (data?.progress?.currentGridTaskDetail?.commonTaskDetail) {
                // 按钮呼吸态恢复
                enableForwardRush.value = true;
                blockingRefreshHandler(false);
                // 刷新主接口
                refreshHome();
                // 挑战 唤起挑战任务面板
                openGridTaskSheet(0);
                return;
            } else {
                if (data?.progress?.gridTaskDegrade) {
                    // 关闭挑战任务面板
                    closeGridTaskSheet();
                    // 展示助力成功花字
                    addEventTask({
                        event: openChallengeNullBanner,
                        options: {
                            priority: -1,
                            name: 'taskNullBanner',
                            log: '挑战任务下线后，展示助力成功花字',
                            queueTags: [QUEUE_TAGS_TYPE.POPUP, QUEUE_TAGS_TYPE.SHEET, QUEUE_TAGS_TYPE.TASK_SHEET_POPUP],
                        },
                    });
                    // await task.end;
                }
                await handleForwardRushWithoutSignFreeCard(data!);
            }
        }
        /** 更新进度 */
        if (isNotNil(data?.progress?.currentStep) && isNotNil(data?.progress?.expectTotalStep)) {
            currentStepForProgress.value = data?.progress.currentStep;
            targetStepForProgress.value = data?.progress.expectTotalStep;
        }
        if (showBubbleProgress.value) {
            // 进度条更新动画的时长是360ms
            await sleep(360);
        }

        const signedFlag = useSignFreeCard ? true : !!data?.progress?.signed;
        if (signedFlag) {
            await playAniWhenPopupClosed();
            await sleep(400);
            // 释放按钮的点击态
            enableForwardRush.value = true;
        }
        todaySigned.value = signedFlag;
        enableForwardRush.value && blockingRefreshHandler(false);
        todaySigned.value && refreshHome();
        // 关键动作上报--结束
        reportKeyActionEnd({ name: keyActionName });
        return;
    }

    /**
     * 点击向前冲按钮
     * 优先级：断签 > 已打卡 > 有免任务卡 > 标准形态
     */
    // eslint-disable-next-line sonarjs/cognitive-complexity
    const handleForwardRushBtnClick = async (forceRush = false) => {
        if (!enableForwardRush.value && !forceRush) {
            console.log('向前冲按钮被锁定，无法点击');
            return;
        }

        switch (mainButtonStatus.value) {
            // 断签：拉起补签面板
            case ForwardRushBtnStatus.SIGN_INTERCEPTED:
                openResumeSignInPopup();
                return;
            case ForwardRushBtnStatus.FIRST_DAY_UN_SIGNED_EXPERIMENT:
                enableForwardRush.value = false;
                const task = await openSelectProductSheet({
                    logSource: 'newer_guid',
                });
                await task.end;
                hasFirstSelectedProduct.value = true;
                enableForwardRush.value = true;
                return;
            case ForwardRushBtnStatus.FIRST_DAY_SIGNED_EXPERIMENT:
                if (isCalendarTask.value) {
                    enableForwardRush.value = false;
                    if (!calendarTaskInfo.value?.finished && calendarTaskInfo.value?.taskInfo) {
                        doTask(calendarTaskInfo.value.taskInfo);
                    } else {
                        await checkAndAddCalendar({ needRefreshHome: true, needRefreshTask: true });
                    }
                    enableForwardRush.value = true;
                    clickedFlag.value = true;
                    return;
                }
                if (isInpushTask.value) {
                    enableForwardRush.value = false;
                    doTask(inpushTaskInfo.value?.taskInfo!);
                    clickedFlag.value = true;
                    enableForwardRush.value = true;
                    return;
                }
                toast(exp0714Toast.value || '今天领过啦');
                return;
            // 已打卡：toast提示
            case ForwardRushBtnStatus.SIGNED:
                // 如果是待领取的状态,需要打开待领取弹窗
                if (signInStatus.value === HugeSignInStatus.COMPLETED && isNotNil(homeData.value?.popList?.[0])) {
                    openSummerPopup(homeData.value?.popList[0], {
                        taskType: TaskType.PASSIVE_POPUP,
                    });
                    return;
                }
                if (isFirstDay.value && inFirstDayProcessing.value) {
                    return;
                }
                // 已打卡调用
                enableForwardRush.value = false;
                if (showCityPage.value && !enableEmphasizeLlwd.value) {
                    gotoCityPage({ dayIndex: todayIndex.value });
                } else {
                    await alreadySignedToOpenPopup();
                }
                enableForwardRush.value = true;
                return;
            // 免任务卡：直接完成当日打开
            case ForwardRushBtnStatus.SIGN_FREE:
                handleForwardRush(true, forceRush);
                return;
            // 标准态：有次数向前冲；无次数拉起做任务面板
            case ForwardRushBtnStatus.UN_SIGN:
                if (rushCount.value > 0) {
                    await handleForwardRush(false, forceRush);
                    // 第一天走完流程解锁
                    if (isFirstDay.value) {
                        blockingRefreshHandler(false);
                    }
                    return;
                }
                // 有未完成的可以获取步数的任务
                if (hasUnfinishedGetCountsTask.value) {
                    openSheet();
                    // 防止用户只剩最后一步时，走格子期间用户因为助力之类的任务获得了新次数，
                    // 此时弹出带有继续冲按钮的弹窗拿到了最新次数，但因为主页面还未刷新主接口，此时是没有次数的
                    // 点击继续冲会弹出任务面板
                    // 这里特殊处理一下，将按钮恢复到可点击状态
                    if (forceRush) {
                        enableForwardRush.value = true;
                    }
                    return;
                }
                toast(mainButtonConfig.value?.noStepToast ?? '');
                return;
            // 断签过期、未领取之类的都展示“重新挑战”
            case ForwardRushBtnStatus.OTHER:
                enableForwardRush.value = false;
                await refreshHome('NEED_START_NEW_ROUND');
                enableForwardRush.value = true;
                return;
            default:
                return;
        }
    };

    /**
     * 挑战任务完成后展示花字并且自动跳一步
     */
    const showRushTipThenAutoRush = async () => {
        openChallengeSuccessBanner();
        await handleForwardRush();
    };

    /**
     * 挑战任务完成后执行的逻辑
     */
    const taskSuccess = async () => {
        if (!isNotNil(homeData.value?.chessboard?.progress.currentGridTaskDetail)) {
            // 关闭挑战任务面板
            closeGridTaskSheet();
        }
        if (gCurrentStation.value?.llrewdGridLayout?.TASK_GRID) {
            const { gridLocation } = gCurrentStation.value?.llrewdGridLayout?.TASK_GRID ?? [];
            const { gridTaskStatus } = gCurrentStation.value?.llrewdGridLayout?.TASK_GRID ?? [];
            const currentStep = homeData.value?.chessboard?.progress.currentStep ?? 0;
            const canAutoRush = gridLocation?.find((v, index) => {
                return currentStep === v - 1 && (gridTaskStatus?.[index] ?? -1) === 2;
            });
            if (isNotNil(canAutoRush)) {
                // 关闭分享面板
                await invoke('social.dismissAllShareDialog');
                // 关闭挑战任务面板
                closeGridTaskSheet();
                // 如果停留在挑战格子前一格，则自动跳一步
                addEventTask({
                    event: showRushTipThenAutoRush,
                    options: {
                        priority: -1,
                        name: 'autoRushByTask',
                        log: '用户冷启后停留在已完成任务格子前，自动跳一步',
                        queueTags: [QUEUE_TAGS_TYPE.POPUP, QUEUE_TAGS_TYPE.SHEET, QUEUE_TAGS_TYPE.TASK_SHEET_POPUP],
                    },
                });
            }
        }
    };

    return {
        isFirstDay,
        isSecondDay,
        todayIndex,
        progress,
        signedStep,
        enableForwardRush,
        showForwardRushBubble,
        showSecondDayTipFinished,
        showBeginPopup,
        showGridPopup,
        forwardRushTip,
        showForwardRushTip,
        rushCount,
        mainButton,
        mainButtonStatus,
        buttonAndBubbleInfo,
        stepCount,
        todaySigned,
        llwardAniController,
        showStationBubble,
        afterSelect,
        showBubbleAndAniAfterSelect,
        isNoStep,
        isBiggerGuide,
        stepBubbleOptimize,
        handleForwardRushBtnClick,
        handleForwardRush,
        showRushTipThenAutoRush,
        taskSuccess,
    };
});

export const useForwardRushBtnModel = () => useModel(forwardRushBtnModel);
