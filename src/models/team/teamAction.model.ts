import { createModel, useModel, type InfoDataType } from '@gundam/model';
import { useRestMutationWithType } from '@gundam/model/utils';
import { throwIfHasError, toastIfHasError } from '@pet/base.model-utils-helper';
import type { InsertUserInfo } from '@pet/quantum.share/extendNormalShare';

import {
    summer2025WishTravelTeamControllerExitTeam,
    summer2025WishTravelTeamControllerGetTeamPanel,
    summer2025WishTravelTeamControllerGetInvitationList,
} from '@/services/open-api-docs/home/<USER>/vue-apollo-model-client';

import { ENTRY_STATUS } from './constants';
import { useForwardRushBtnModel } from '../forwardRushBtnModel';
import { useHomeModel } from '../homeModel';
import { useShareModel } from '../shareModel';

const localToastIfHasError = <T>(
    mutate: () => Promise<any>,
    info: InfoDataType<T>,
    options?: {
        allowEmpty?: boolean;
    },
) => {
    const { allowEmpty = false } = options ?? {};
    return toastIfHasError(async () => {
        await mutate();
        // @ts-expect-error
        throwIfHasError(info, allowEmpty);
    });
};

const getLocalMutation = <T, U>(
    mutation: () => Promise<T>,
    info: InfoDataType<U>,
    options?: {
        allowEmpty?: boolean;
    },
) => {
    const mutate = (): Promise<void> => localToastIfHasError(mutation, info, options);
    return {
        mutate,
        error: computed(() => info.error),
        loading: computed(() => info.loading),
        data: computed(() => {
            if (info.error) {
                return null;
            }
            return info.data;
        }),
    };
};

export const teamActionModel = createModel(() => {
    const { homeData } = useHomeModel();
    const { handleForwardRush } = useForwardRushBtnModel();
    const { shareZuDu, shareZuDuAsk, shareZuDuCard } = useShareModel();

    // 存储当前的请求Promise
    let currentTeamPanelPromise: Promise<void> | null = null;
    // 存储下一个等待的Promise的resolve函数
    let nextTeamPanelResolve: (() => void) | null = null;

    const teamPanelMutation = useRestMutationWithType(summer2025WishTravelTeamControllerGetTeamPanel)();
    const { loading: teamPanelLoading, data: teamPanelData } = getLocalMutation(
        () => teamPanelMutation.mutate({}),
        teamPanelMutation.info,
    );

    /** 队伍面板 */
    const teamPanelMutate = async (): Promise<void> => {
        // 如果已经有等待的请求，直接返回那个Promise
        if (nextTeamPanelResolve) {
            return new Promise<void>((resolve) => {
                const prevResolve = nextTeamPanelResolve!;
                nextTeamPanelResolve = () => {
                    prevResolve();
                    resolve();
                };
            });
        }

        // 如果当前有请求在处理
        if (currentTeamPanelPromise) {
            // 创建一个新的Promise，等待当前请求完成后再执行
            return new Promise<void>((resolve) => {
                nextTeamPanelResolve = resolve;
            }).then(() => {
                nextTeamPanelResolve = null;
                return teamPanelMutate();
            });
        }

        // 执行请求
        currentTeamPanelPromise = getLocalMutation(() => teamPanelMutation.mutate({}), teamPanelMutation.info)
            .mutate()
            .finally(() => {
                currentTeamPanelPromise = null;
                // 如果有等待的请求，触发它
                if (nextTeamPanelResolve) {
                    const resolve = nextTeamPanelResolve;
                    nextTeamPanelResolve = null;
                    resolve();
                }
            });

        return currentTeamPanelPromise;
    };

    watch(
        () => homeData.value?.teamEntryView?.newEntryStatus,
        (newEntryStatus, oldEntryStatus) => {
            // newEntryStatus不为空 且 非免签卡状态 且 状态发生变化
            if (newEntryStatus && newEntryStatus < ENTRY_STATUS.CARD && newEntryStatus !== oldEntryStatus) {
                // 刷新teamPanel
                teamPanelMutate();
            }
        },
        {
            immediate: true,
        },
    );

    const inviteListMutation = useRestMutationWithType(summer2025WishTravelTeamControllerGetInvitationList)();
    const {
        /** 获取邀请列表 */
        mutate: inviteListMutate,
        /** 邀请列表加载状态 */
        loading: inviteListLoading,
        data: _inviteListData,
    } = getLocalMutation(() => inviteListMutation.mutate({}), inviteListMutation.info);

    const { mutate: _exitTeamMutate, info: exitInfo } = useRestMutationWithType(
        summer2025WishTravelTeamControllerExitTeam,
    )();
    const {
        /** 退出队伍 */
        mutate: exitTeamMutate,
        /** 退出队伍错误 */
        error: exitTeamError,
    } = getLocalMutation(() => _exitTeamMutate({}), exitInfo, { allowEmpty: true });

    const inviteListData = computed(() => {
        const list = _inviteListData.value ?? [];
        const pageSize = 5;
        const totalPages = Math.floor(list.length / pageSize);
        const paginatedList = list.slice(0, totalPages * pageSize);
        return paginatedList;
    });
    const inviteListPageSize = 5;
    // 从0开始计数
    const inviteListPage = ref(0);

    /** 邀请列表 */
    const inviteList = computed(() => {
        const start = inviteListPage.value * inviteListPageSize;
        return inviteListData.value.slice(start, start + inviteListPageSize);
    });

    /** 换一换（邀请列表下一页） */
    const nextInvitePage = () => {
        inviteListPage.value++;
        if (inviteListPage.value * inviteListPageSize >= inviteListData.value.length) {
            inviteListPage.value = 0;
        }
    };

    /** 邀请 */
    const shareInvite = (userInfo?: InsertUserInfo[]) => {
        toastIfHasError(() => shareZuDu(userInfo));
    };
    /** 组队卡片邀请 */
    const shareCardInvite = (userInfo?: InsertUserInfo[]) => {
        toastIfHasError(() => shareZuDuCard(userInfo));
    };

    /** 提醒队友打卡 */
    const shareRemindToCheckIn = () => {
        const teamMates = (teamPanelData?.value?.teamUser ?? homeData?.value?.teamEntryView?.teamUser ?? [])
            .filter((u) => !u.currentUser)
            .map((u) => ({
                user_id: u.userId,
                user_name: u.nickName,
                headurl: u.userAvatar,
            }));

        toastIfHasError(() => shareZuDuAsk(teamMates));
    };

    // 可能不需要业务定义，走通用逻辑
    /** 提醒队友打卡回流 */
    // async function shareBackflowRemindToCheckIn() {

    // }
    /** 使用直通卡 */
    const useCard = () => {
        handleForwardRush(true);
    };

    return {
        /** 队伍面板 */
        teamPanelData,
        /** 队伍面板加载状态 */
        teamPanelLoading,
        /** 刷新队伍面板 */
        teamPanelMutate,

        /** 邀请列表 */
        inviteList,
        /** 邀请列表加载状态 */
        inviteListLoading,

        /** 换一换（邀请列表下一页） */
        nextInvitePage,

        /** 获取邀请列表 */
        inviteListMutate,

        /** 退出队伍 */
        exitTeamMutate,
        /** 退出队伍错误 */
        exitTeamError,

        /** 邀请 */
        shareInvite,
        /** 组队卡片邀请 */
        shareCardInvite,
        /** 邀请回流，入队 */
        // shareBackflowInvite,

        /** 提醒队友打卡 */
        shareRemindToCheckIn,
        /** 提醒队友打卡回流 */
        // shareBackflowRemindToCheckIn,

        /** 使用直通卡 */
        useCard,
    };
});

export const useTeamActionModel = () => useModel(teamActionModel);
