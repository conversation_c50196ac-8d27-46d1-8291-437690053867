import { useModel, createModel } from '@gundam/model';

import { MAP_ENTRY_STATUS_TEXT } from './constants';
import { useTeamActionModel } from './teamAction.model';
import { useHomeModel } from '../homeModel';

enum PopoverType {
    TEAM = 1, // 组队引导
    CARD = 2, // 未打卡提醒
}

export const teamDataModel = createModel(() => {
    const { homeData } = useHomeModel();
    const { teamPanelData, inviteList, inviteListLoading } = useTeamActionModel();

    const teamEntryViewData = computed(() => homeData.value?.teamEntryView);

    /** 组队面板 */
    const teamPanel = computed(() => {
        const teamPanel = teamPanelData.value;
        if (!teamPanel) {
            return null;
        }

        // 组队成功，且还未拿到直通卡
        const isTeamCreated = teamPanel.teamStatus && (teamPanel.teamStatus === 3 || teamPanel.teamStatus === 4);

        // 已拿到直通卡
        const isReceivedFreeCard = teamPanel.teamStatus && teamPanel.teamStatus === 5;

        return {
            ...teamPanel,
            teamStatus: teamPanel.teamStatus,
            taskFreeCardImg: teamPanel.taskFreeCardImg ?? '',
            taskFreeCardNum: teamPanel.taskFreeCardNum ?? 7,
            togetherSignDays: teamPanel.togetherSignDays ?? 0,
            desc: teamPanel.desc,
            isSelfSigned: teamPanel.teamUser.some((item) => item.currentUser && item.todaySign),
            isAllSigned: teamPanel.teamUser.every((item) => item.todaySign),
            isTeamCreated,
            isReceivedFreeCard,
        };
    });

    /** 组队入口数据 */
    const entry = computed(() => {
        const teamEntryView = teamEntryViewData.value;
        if (!teamEntryView) {
            return null;
        }
        return {
            type: teamEntryView.newEntryStatus,
            text: MAP_ENTRY_STATUS_TEXT[teamEntryView.newEntryStatus],
            teamCount: teamEntryView.teamUser?.length ?? 0,
            teamId: teamEntryView.teamId,
        };
    });

    const teamUser = computed(() => {
        return teamEntryViewData.value?.teamUser ?? [];
    });
    const teamTotalMemberNum = computed(() => {
        return teamEntryViewData.value?.totalMemberNum ?? 0;
    });

    const cardPanelEntry = computed(() => {
        const teamEntryView = teamEntryViewData.value;
        if (!teamEntryView) {
            return null;
        }
        return {
            list: teamEntryView.freeCardList,
            title: teamEntryView.freeCardListTitle,
            subTitle: teamEntryView.freeCardListSubTitle,
            cardImage: teamEntryView.freeCardImg,
        };
    });

    /** 直通卡面板数据 */
    const cardPanel = computed(() => {
        // 以前从entry里取，现在从teamPanel里取
        const teamPanel = teamPanelData.value;
        if (!teamPanel) {
            return null;
        }
        return {
            list: teamPanel.freeCardList,
            title: teamPanel.freeCardListTitle,
            subTitle: teamPanel.freeCardListSubTitle,
            cardImage: teamPanel.freeCardImg,
        };
    });

    /** 退队弹窗数据 */
    const exitTeamPopupData = computed(() => {
        const exitTeamPopup = teamPanel.value?.exitTeamPopup ?? {};
        return {
            subTitle: exitTeamPopup.popupTitle ?? '确定离开小队吗？',
            desc: exitTeamPopup.popupDesc ?? '退队后，小队打卡进度将被清零，且无法恢复',
            icon: teamPanel.value?.taskFreeCardImg ?? '',
            taskFreeCardNum: teamPanel.value?.taskFreeCardNum ?? 0,
            mainButton: exitTeamPopup.confirmButton ?? '再想想',
            subButton: exitTeamPopup.cancelButton ?? '退出',
            confirmPopupSubTitle: exitTeamPopup.confirmPopupTitle ?? '队伍解散',
            confirmPopupDesc:
                exitTeamPopup.confirmPopupDesc ?? '你已退队，队伍解散\n别担心，你的个人打卡天数不受影响！',
            confirmPopupButton: exitTeamPopup.confirmPopupButton ?? '我知道了',
            sponsorLogo: exitTeamPopup.sponsorLogo ?? '',
            sponsorText: exitTeamPopup.sponsorText ?? '',
        };
    });

    /** 邀请好友 */
    const invite = computed(() => {
        return {
            list: inviteList.value,
            loading: inviteListLoading.value,
        };
    });

    const popoverInfo = computed(() => {
        const teamEntryView = teamEntryViewData.value;
        if (!teamEntryView) {
            return null;
        }
        const { newShowPop, popType, number } = teamEntryView;
        return {
            showPop: newShowPop,
            popType,
            number,
        };
    });

    /** 当前是否成功组队，且未发放奖励 */
    const isTeamSuccess = computed(() => {
        return teamEntryViewData.value?.newEntryStatus === 2 || teamEntryViewData.value?.newEntryStatus === 3;
    });

    /** 是否可以展示已打开去组队弹窗标识 */
    const canShowGoTeamPopup = computed(() => {
        // 满足去组队状态
        return teamEntryViewData.value?.newEntryStatus === 1;
    });
    /** 换品是否提示退队 */
    const exitTeamGuide = computed(() => {
        return teamEntryViewData.value?.exitTeamGuide ?? false;
    });

    const realShowPopover = ref(false);

    const closeRealShowPopover = () => {
        realShowPopover.value = false;
    };

    const isTeamMakeGuideBubble = computed(() => popoverInfo.value?.popType === PopoverType.TEAM);
    const isRemindGuideBubble = computed(() => popoverInfo.value?.popType === PopoverType.CARD);

    const guideId = computed<'GUIDE_CARD' | 'GUIDE_TEAM'>(() => `GUIDE_${isRemindGuideBubble.value ? 'CARD' : 'TEAM'}`);

    return {
        realShowPopover,
        closeRealShowPopover,
        /** 组队入口数据 */
        entry,

        // 组队队友列表
        teamUser,

        /** 组队总人数 */
        teamTotalMemberNum,

        /** 直通卡面板数据（入口） */
        cardPanelEntry,

        /** 直通卡面板数据（panel） */
        cardPanel,

        /** 组队面板 */
        teamPanel,

        /** 邀请好友 */
        invite,

        /** 退队弹窗数据 */
        exitTeamPopupData,

        /** 气泡数据 */
        popoverInfo,

        /** 当前是否成功组队 */
        isTeamSuccess,

        /** 换品是否提示退队 */
        exitTeamGuide,

        /** 是否可以展示已打开去组队弹窗 */
        canShowGoTeamPopup,

        /** 未打卡提醒引导气泡 */
        isRemindGuideBubble,
        /** 组队气泡 */
        isTeamMakeGuideBubble,
        guideId,
    };
});
export const useTeamDataModel = () => useModel(teamDataModel);
