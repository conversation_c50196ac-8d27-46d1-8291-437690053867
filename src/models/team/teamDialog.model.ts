import { createModel, useModel } from '@gundam/model';
import { useGuideState } from '@pet/25cny.guide-directive';
import { Task } from '@pet/adapt.queue-api/main/TaskQueue';
import { until, whenever } from '@vueuse/core';

import { useLogger } from '@/init/logger';
import { SoundType, useAudioModel } from '@/models/audioModel';
import { type TeamSignRewardVO, type TeamSuccessPopupVO } from '@/services/open-api-docs/home/<USER>/schemas';
import { reportKeyActionEnd } from '@/utils/log/keyActionLog';

import { ENTRY_STATUS } from './constants';
import { useTeamActionModel } from './teamAction.model';
import { useTeamAnimationModel } from './teamAnimation.model';
import { useTeamDataModel } from './teamData.model';
import { useHomeApiModel } from '../api/homeApiModel';
import { taskApiModel } from '../api/taskApiModel';
import { QUEUE_TAGS_TYPE, TaskType, usePopupModel } from '../popup.model';

export const teamDialogModel = createModel(({ getModelInstance }) => {
    const { openPopup, createPopupTask, addGroupTask } = usePopupModel();
    const { exitTeamPopupData, teamPanel, entry } = useTeamDataModel();
    const { exitTeamMutate, exitTeamError, teamPanelMutate, teamPanelLoading, inviteListMutate } = useTeamActionModel();
    const { receiveTaskFreeDialogFlyStart } = useTeamAnimationModel();
    const { refreshHome } = useHomeApiModel();
    const { sendShow, sendClick } = useLogger();
    const { playSound } = useAudioModel();
    const { showGuideById } = useGuideState();
    const { guideId } = useTeamDataModel();

    const teamPanelTask = ref<Task | null>();

    /** 关闭队伍面板 */
    const closeTeamPanel = async () => {
        const task = teamPanelTask.value;
        if (!task) {
            return;
        }
        task.triggerDestroy();
        await task.end;
    };

    /** 弹起队伍面板 */
    const openTeamPanel = async ({
        enterToast,
        fromSocial = false,
    }: { enterToast?: string; fromSocial?: boolean } = {}) => {
        if (teamPanelTask.value) {
            return;
        }
        teamPanelMutate().then(() => {
            const teamStatus = teamPanel.value?.teamStatus ?? 0;
            if (teamStatus === 1 || teamStatus === 2) {
                inviteListMutate();
            }
        });
        if (!teamPanel.value) {
            await until(teamPanelLoading).toBe(false);
            // eslint-disable-next-line @typescript-eslint/strict-boolean-expressions
            if (!teamPanel.value) {
                return;
            }
        }
        teamPanelTask.value = openPopup({
            component: () => import('@/components/team/TeamPanel.vue'),
            data: {
                enterToast,
                fromSocial,
            },
            options: {
                name: TaskType.SPECIAL_SHEET,
                queueTags: [QUEUE_TAGS_TYPE.SHEET, QUEUE_TAGS_TYPE.POPUP],
            },
        });
        teamPanelTask.value?.end.finally(() => {
            teamPanelTask.value = null;
            if (fromSocial) {
                showGuideById(guideId.value, { enabled: true });
            }
        });
    };

    whenever(
        () => !teamPanel.value,
        () => {
            closeTeamPanel();
        },
    );

    /** 弹起退队确认弹窗 */
    const openExitTeamPopup = () => {
        // todo: 退队结果可能不稳定，是不是不加这个判断比较好
        const needShowSafeTipPopup = ref();

        const safeTipPopup = createPopupTask({
            component: () => import('@/components/common-modals/CommonModal.vue'),
            data: {
                sponsorLogo: exitTeamPopupData.value.sponsorLogo,
                sponsorText: exitTeamPopupData.value.sponsorText,
                subTitle: exitTeamPopupData.value.confirmPopupSubTitle,
                message: exitTeamPopupData.value.confirmPopupDesc,
                mainButton: exitTeamPopupData.value.confirmPopupButton,
                lightType: 'none',
                type: 'dialog',
                btnClick: async (popup) => {
                    popup?.destroy();
                },
            },
            options: {
                name: TaskType.ACTIVE_POPUP,
                ext: {
                    noPlayShowSound: true,
                },
            },
        });

        const isReceiveTaskFreeCard = teamPanel.value?.teamStatus === 5;

        const component = isReceiveTaskFreeCard
            ? () => import('@/components/popups/failure-effect-popup/FailureEffectPopup.vue')
            : () => import('@/components/team/dialog/TaskFreeCardModal.vue');

        const confirmPopup = createPopupTask({
            component,
            data: {
                lightType: 'none',
                ...exitTeamPopupData.value,
                type: 'dialog',
                btnClick: async (popup) => {
                    if (popup.position === 'subClick') {
                        await exitTeamMutate();
                        if (exitTeamError.value) {
                            needShowSafeTipPopup.value = false;
                        } else {
                            closeTeamPanel();
                            needShowSafeTipPopup.value = true;
                        }
                        // 因为退队是异步任务，需要延迟 1 秒刷新状态
                        setTimeout(() => {
                            // 刷新主接口，更新 entry 数据
                            refreshHome().then(() => {
                                // 刷新任务接口，更新组队任务状态
                                getModelInstance(taskApiModel)?.tasksRefetch();
                            });
                        }, 1000);
                    }
                    popup?.destroy();
                },
            },
            options: {
                name: TaskType.ACTIVE_POPUP,
                ext: {
                    noPlayShowSound: true,
                },
            },
        });
        // 添加组任务到队列
        const group = addGroupTask({
            options: {
                name: TaskType.GROUP_TASK,
            },
            tasks: [confirmPopup],
        });

        // 在横幅弹窗结束后添加组队成功弹窗
        confirmPopup?.end?.then((e) => {
            if (needShowSafeTipPopup.value) {
                group?.addSubTask(safeTipPopup);
            }
        });
    };

    /** 弹起组队成功弹窗组 */
    const openTeamSuccessPopup = (teamSuccessPopup: TeamSuccessPopupVO) => {
        reportKeyActionEnd({
            name: 'team_up_success',
            extra_info: {
                team_id: entry.value?.teamId,
            },
        });

        // 创建等待横幅弹窗任务
        const bannerPopup = createPopupTask({
            component: () => import('@/components/popups/banner-popup/BannerPopup.vue'),
            data: {
                rightText: `${teamSuccessPopup.animationTitle}\n${teamSuccessPopup.animationDesc}`,
                onShowLog: () => {
                    sendShow('OP_ACTIVITY_MAKE_TEAM_SUCCESS', {
                        brand_name: teamSuccessPopup.sponsorText,
                        title: teamSuccessPopup.title ?? '组队成功',
                        team_id: entry.value?.teamId,
                    });
                },
            },
            options: {
                name: TaskType.ACTIVE_POPUP,
                queueTags: [QUEUE_TAGS_TYPE.POPUP],
            },
        });

        const teamPopupMainButtonText = teamSuccessPopup.mainButton?.linkText ?? '我知道了';
        // 创建组队成功弹窗任务
        const teamPopup = createPopupTask({
            component: () => import('@/components/team/dialog/TaskFreeCardModal.vue'),
            data: {
                sponsorLogo: teamSuccessPopup.sponsorLogo ?? '',
                subTitle: teamSuccessPopup.title ?? '组队成功',
                desc: teamSuccessPopup.desc ?? '全员连续打卡7天得直通卡 1人断签即失败',
                icon: teamSuccessPopup.icon ?? '',
                mainButton: teamPopupMainButtonText,
                taskFreeCardNum: teamSuccessPopup?.taskFreeCardNum ?? 7,
                type: 'dialog',
                btnClick: (popup) => {
                    if (popup.position === 'mainClick') {
                        sendClick('OP_ACTIVITY_MAKE_TEAM_SUCCESS', {
                            brand_name: teamSuccessPopup.sponsorText,
                            title: teamSuccessPopup.title ?? '组队成功',
                            team_id: entry.value?.teamId,
                            button_name: teamPopupMainButtonText,
                        });
                    }
                    popup?.destroy();
                    // todo: 延迟到弹窗关闭动画结束后打开
                    openTeamPanel();
                },
            },
            options: {
                name: TaskType.ACTIVE_POPUP,
                queueTags: [QUEUE_TAGS_TYPE.POPUP],
            },
        });

        // 添加组任务到队列
        const group = addGroupTask({
            options: {
                name: TaskType.GROUP_TASK,
            },
            tasks: [bannerPopup],
        });

        // 在横幅弹窗结束后添加组队成功弹窗
        bannerPopup?.end?.then(() => {
            group?.addSubTask(teamPopup);
        });
        group.end?.then(() => {
            refreshHome().then(() => {
                getModelInstance(taskApiModel)?.tasksRefetch();
            });
        });
        return group;
    };

    /** 获得免任务卡弹窗 */
    const openReceiveTaskFreeCardPopup = (teamSignReward: TeamSignRewardVO) => {
        const mainButtonText = teamSignReward.mainButton?.linkSubText ?? '开心收下';
        openPopup({
            component: () => import('@/components/team/dialog/TaskFreeCardModal.vue'),
            data: {
                type: 'dialog',
                sponsorLogo: teamSignReward.sponsorLogo ?? '',
                subTitle: teamSignReward.title ?? '',
                desc: teamSignReward.desc ?? '',
                icon: teamSignReward.icon ?? '',
                mainButton: mainButtonText,
                taskFreeCardNum: teamSignReward.taskFreeCardNum ?? 7,
                btnClick: async (popup) => {
                    if (popup.position === 'mainClick') {
                        sendClick('OP_ACTIVITY_MAKE_TEAM_SIGN_SUCCESS', {
                            brand_name: teamSignReward.sponsorText,
                            title: teamSignReward.title,
                            team_id: entry.value?.teamId,
                            button_name: mainButtonText,
                        });
                    }
                    openTeamPanel();
                    popup?.destroy();
                },
                onShow: () => {
                    sendShow('OP_ACTIVITY_MAKE_TEAM_SIGN_SUCCESS', {
                        brand_name: teamSignReward.sponsorText,
                        title: teamSignReward.title,
                        team_id: entry.value?.teamId,
                    });
                },
                onClose: async () => {
                    receiveTaskFreeDialogFlyStart();
                    playSound(SoundType.FLY);
                },
            },
            options: {
                name: TaskType.ACTIVE_POPUP,
                queueTags: [QUEUE_TAGS_TYPE.POPUP],
            },
        });
    };

    const openCardSheet = () => {
        openPopup({
            component: () => import('@/components/team/CardPanel.vue'),
            data: {},
            options: {
                name: TaskType.ACTIVE_POPUP,
                queueTags: [QUEUE_TAGS_TYPE.SHEET],
            },
        });
    };

    /** 打卡组队&直通卡面板 */
    const openTeamOrCardSheet = (type?: ENTRY_STATUS, fromSocial = false) => {
        if (type === ENTRY_STATUS.CARD) {
            openCardSheet();
        } else {
            openTeamPanel({
                fromSocial,
            });
        }
    };

    return {
        /** 队伍面板 Task */
        teamPanelTask,

        /** 弹起队伍面板 */
        openTeamPanel,

        /** 关闭队伍面板 */
        closeTeamPanel,

        /** 弹起退队确认弹窗 */
        openExitTeamPopup,

        /** 弹起组队成功弹窗组 */
        openTeamSuccessPopup,

        /** 获得免任务卡弹窗 */
        openReceiveTaskFreeCardPopup,

        /** 打开直通卡面板 */
        openCardSheet,

        /** 打卡组队&直通卡面板 */
        openTeamOrCardSheet,
    };
});

export const useTeamDialogModel = () => useModel(teamDialogModel);
