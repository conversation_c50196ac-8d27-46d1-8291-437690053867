import { createUseModel } from '@gundam/model';
import { useGuideState } from '@pet/25cny.guide-directive';
import type { Task } from '@pet/adapt.queue-api/main/TaskQueue';
import { useTaskQueueModel } from '@pet/adapt.task-queue/index';
import { toast } from '@pet/adapt.toast';
import useCaptureDebugLog from '@pet/yau.logger';

import { DefaultReplaceProductConfirmText } from '@/common/const/signIn';
import { TOAST_LIMIT_PRODUCT, SELECT_PRODUCT_SUCCESS, TOAST_DEFAULT_PRODUCT } from '@/common/const/toast';
// import CommonModal from '@/components/common-modals/CommonModal.vue';
import { useABTest } from '@/hooks/useABTest';
import { useLogger } from '@/init/logger';
import { useConfigModel } from '@/models/configModel';
import { TaskModel } from '@/models/taskModel';
import { teamDataModel } from '@/models/team/teamData.model';
import { teamDialogModel } from '@/models/team/teamDialog.model';
import {
    type SignInSelectProductView,
    type SignInSelectProductListView,
} from '@/services/open-api-docs/home/<USER>/schemas';
import { reportKeyActionStart, reportKeyActionEnd } from '@/utils/log/keyActionLog';

import { useSelectProductApiModel } from './api/selectProductApiModel';
import { useAudioModel, SoundType } from './audioModel';
import { useCalendarModel } from './calendarModel';
import { useForwardRushGridModel } from './forwardRushGridModel';
import { useHomeModel } from './homeModel';
import { TaskType, CUSTOM_POPUP_TYPE, QUEUE_TAGS_TYPE } from './popup.model';
import { useSnapShotModel } from './snapShotModel';

interface SheetParams {
    popup?: SignInSelectProductListView;
    topTitle?: string;
    logSource?: string;
}

export const useSelectProductModel = createUseModel(({ getModelInstance }) => {
    const { refreshHome, sinInHomeInfo, isNoStep } = useHomeModel();
    const { productListData, selectProductMutation, getSelectProductData } = useSelectProductApiModel();
    const { isProgressSignedAnim } = useSnapShotModel();
    const { refreshCalendar } = useCalendarModel();
    const { kconfConfig } = useConfigModel();
    const { playSound } = useAudioModel();
    const { log } = useCaptureDebugLog('selectProduct');
    const replaceProductConfirmText = computed(() => kconfConfig.value?.replaceProductConfirmText);
    /* 换品弹窗标题 */
    const selectProductSheetTitle = computed(() => productListData.value?.title ?? '');
    /* 换品弹窗副标题 */
    const selectProductSheetSubTitle = computed(() => productListData.value?.subTitle ?? '');
    /* 换品弹窗商品列表 */
    const selectProductList = computed(() => productListData.value?.productList ?? []);
    const firstFreeChangeProduct = ref(false);
    /* 换品动效信息 */
    const choiceProductAnim = reactive({
        play: false,
        afterUrl: '',
    });
    const { currentGuide } = useGuideState();
    const { openPopup, createPopupTask, addGroupTask } = useTaskQueueModel();
    const { afterSelect } = useForwardRushGridModel();
    const { sendClick } = useLogger();
    // 第一次选品时记录选品id
    const firstSelectedProductId = ref();
    const reopenSelectSheet = ref(false);

    // 进度条动效控制
    const setChoiceProductAnim = (show: boolean, afterUrl?: string) => {
        if (show) {
            // TIPS: 选品反馈动效 与 打卡成功动效冲突, 因此在选品反馈动效前, 移除打卡成功动效相关 class
            isProgressSignedAnim.value = false;
            choiceProductAnim.afterUrl = afterUrl ?? '';
            choiceProductAnim.play = true;
        } else {
            // 换品动效重置为默认值, 支持连续换品
            choiceProductAnim.play = false;
        }
    };

    const sheet = ref<Task | null>();
    const confirmPopup = ref<Task | null>();
    const group = ref<Task | null>();

    const getReplaceProductDesc = () => {
        if (firstFreeChangeProduct.value) {
            return replaceProductConfirmText.value?.FIRST ?? DefaultReplaceProductConfirmText.FIRST;
        }
        if (isNoStep.value) {
            return replaceProductConfirmText.value?.NORMAL2 ?? DefaultReplaceProductConfirmText.NORMAL2;
        }
        return replaceProductConfirmText.value?.NORMAL ?? DefaultReplaceProductConfirmText.NORMAL;
    };

    const { enableEmphasizeSelected } = useABTest();
    const selectProduct = async (
        product: SignInSelectProductView,
        logParams: { source: string; button_name: string; good_id: number },
        close?: () => void,
        // eslint-disable-next-line sonarjs/cognitive-complexity
    ) => {
        // 商品数量不足。不可选
        if (product?.productRemainStatus === 1) {
            toast(enableEmphasizeSelected.value ? TOAST_DEFAULT_PRODUCT : TOAST_LIMIT_PRODUCT);
            return;
        }
        if (product?.productId === (firstSelectedProductId.value ?? productListData.value?.selectedProductId)) {
            toast(TOAST_DEFAULT_PRODUCT);
            close?.();
            return;
        }
        const handleSelectProduct = async () => {
            log('handleSelectProduct--请求换品');
            await selectProductMutation.mutate({
                productId: product.productId!,
                templateId: product.templateId!,
            });
            if (selectProductMutation.info.error) {
                console.log('【handleSelectProduct】换品失败');
                log('handleSelectProduct--换品失败', selectProductMutation.info.error);
                await toast(selectProductMutation.info.error?.data?.error_msg ?? '网络错误');
                return false;
            } else {
                log('handleSelectProduct--换品成功');
                console.log('【handleSelectProduct】换品成功');
                firstSelectedProductId.value = undefined;
                firstFreeChangeProduct.value = false;
                // eslint-disable-next-line no-underscore-dangle
                currentGuide.value?.el?.__destroyGuide?.();

                refreshHome().then(() => {
                    // 换品成功后重新写入日历
                    log('handleSelectProduct--换品成功后重新写入日历');
                    reportKeyActionEnd({
                        name: `sign_in_select_product`,
                    });
                    // 换品成功后刷新任务列表
                    getModelInstance(TaskModel)?.tasksRefetch();
                    refreshCalendar();
                });

                return true;
            }
        };

        // 非首次免费换品且组队成功时，需要先退队
        if (!firstFreeChangeProduct.value && getModelInstance(teamDataModel)?.exitTeamGuide.value) {
            // 组队面板二次确认弹窗
            openPopup({
                component: () => import('@/components/common-modals/CommonModal.vue'),
                data: {
                    subTitle: '更换商品需先退队',
                    desc: replaceProductConfirmText.value?.TEAM ?? DefaultReplaceProductConfirmText.TEAM,
                    icon: product.productIcon ?? '',
                    mainButton: '去退队',
                    subButton: '不换了',
                    type: 'dialog',
                    lightType: 'none',
                    btnClick: async (popup) => {
                        const { position } = popup;
                        if (position === 'mainClick') {
                            getModelInstance(teamDialogModel)?.openTeamPanel();
                        }
                        popup?.destroy();
                    },
                },
                options: {
                    name: TaskType.ACTIVE_POPUP,
                    queueTags: [QUEUE_TAGS_TYPE.POPUP],
                    ext: {
                        noPlayShowSound: true,
                    },
                },
            });
        } else {
            const mainButtonText = firstFreeChangeProduct.value ? '就拿它' : '确定更换';
            const notFlyToTarget = ref(false);
            const btnLoading = ref(false);

            setChoiceProductAnim(false);

            const directSelectProduct = async () => {
                log('换品二次确认弹窗--主按钮点击', logParams);
                const resSelect = await handleSelectProduct();
                sendClick('OP_ACTIVITY_REWARD_CHOOSE_RESULT', {
                    source: logParams.source,
                    good_id: logParams.good_id,
                    button_name: mainButtonText,
                    result_type: resSelect ? 'success' : 'fail',
                });
                if (resSelect) {
                    setChoiceProductAnim(true, product.productIcon ?? '');
                    toast(TOAST_DEFAULT_PRODUCT);
                } else {
                    notFlyToTarget.value = true;
                }
            };

            if (enableEmphasizeSelected.value && firstFreeChangeProduct.value) {
                directSelectProduct();
            } else {
                // 换品二次确认弹窗
                confirmPopup.value = createPopupTask({
                    component: () => import('@/components/common-modals/CommonModal.vue'),
                    data: {
                        subTitle: firstFreeChangeProduct.value ? '确定白拿这个吗？' : '更换后进度会清零',
                        desc: getReplaceProductDesc(),
                        icon: product.productIcon ?? '',
                        mainButton: mainButtonText,
                        subButton: '再想想',
                        type: 'dialog',
                        cloneFlyToTarget: 'progress-view',
                        clonePilotProps: {
                            duration: 467,
                            bezier: [0, 0, -47, -84.5],
                        },
                        notFlyToTarget,
                        lightType: 'none',
                        btnLoading,
                        btnClick: async (popup) => {
                            const { position } = popup;
                            playSound(SoundType.FLY_TO_PRODUCT);
                            if (position === 'mainClick') {
                                log('换品二次确认弹窗--主按钮点击', logParams);
                                btnLoading.value = true;
                                const resSelect = await handleSelectProduct();
                                sendClick('OP_ACTIVITY_REWARD_CHOOSE_RESULT', {
                                    source: logParams.source,
                                    good_id: logParams.good_id,
                                    button_name: mainButtonText,
                                    result_type: resSelect ? 'success' : 'fail',
                                });
                                btnLoading.value = false;

                                if (resSelect) {
                                    setChoiceProductAnim(true, product.productIcon ?? '');
                                    toast(SELECT_PRODUCT_SUCCESS);
                                    popup?.destroy();
                                } else {
                                    notFlyToTarget.value = true;
                                    popup?.destroy();
                                }
                            } else if (position === 'subClick') {
                                reopenSelectSheet.value = true;
                                log('换品二次确认弹窗--副按钮点击', logParams);
                                notFlyToTarget.value = true;

                                popup?.destroy();

                                // eslint-disable-next-line @typescript-eslint/no-use-before-define
                                openSelectProductSheet({ logSource: 'choose' });

                                sendClick('OP_ACTIVITY_REWARD_CHOOSE_RESULT', {
                                    source: logParams.source,
                                    good_id: logParams.good_id,
                                    button_name: '再想想',
                                });
                            } else {
                                log('换品二次确认弹窗--其他按钮点击', logParams);
                                notFlyToTarget.value = true;
                                popup?.destroy();
                            }
                        },
                    },
                    options: {
                        name: TaskType.ACTIVE_POPUP,
                        ext: {
                            noPlayShowSound: true,
                        },
                    },
                });

                confirmPopup.value.end.finally(() => {
                    confirmPopup.value = null;
                });

                // 在任务队列中添加二次确认弹窗
                group.value?.addSubTask(confirmPopup.value);
            }
        }

        close?.();
    };

    const handlePickItem = async (
        v?: SignInSelectProductView,
        index?: string,
        btnName?: string,
        logSource?: string,
        close?: () => void,
    ) => {
        if (v) {
            const logParams = {
                source: logSource ?? '',
                button_name: btnName ?? '',
                good_id: v?.productId!,
            };
            sendClick('OP_ACTIVITY_REWARD_CHOOSE_POP', logParams);
            log('handlePickItem--点击商品', logParams);
            reportKeyActionStart({
                name: `sign_in_select_product`,
            });
            selectProduct(v, logParams, close);
        } else {
            console.log('【handlePickItem】无商品信息');
        }
    };

    const options = {
        name: TaskType.ACTIVE_POPUP,
        queueTags: [QUEUE_TAGS_TYPE.POPUP],
        ext: {
            noPlayShowSound: true,
        },
    };

    const openSheetWithPop = (popup: SignInSelectProductListView, params: SheetParams) => {
        firstFreeChangeProduct.value = popup?.freeChange ?? false;
        firstSelectedProductId.value = popup?.selectedProductId;
        log('换品弹窗--后端首日下发', popup);
        // 首日选品触发,不调用接口
        sheet.value = createPopupTask({
            component: () => import('@/components/popups/select-product-sheet/SelectProductSheet.vue'),
            data: {
                popupType: CUSTOM_POPUP_TYPE.SELECT_PRODUCT_POPUP,
                title: popup?.title ?? '',
                subTitle: popup?.subTitle ?? '',
                topTitle: params?.topTitle ?? '',
                firstSelectTag: popup?.firstSelectTag ?? '',
                productList: popup?.productList ?? [],
                finalProduct: sinInHomeInfo?.value?.product!,
                handlePickItem,
                setChoiceProductAnim,
                logSource: params?.logSource ?? '',
            },
            options,
        });
    };

    // 打开换品弹窗
    // eslint-disable-next-line sonarjs/cognitive-complexity
    const openSelectProductSheet = async (params: SheetParams) => {
        const { popup, topTitle, logSource } = params;
        if (popup) {
            openSheetWithPop(popup, params);
        } else {
            const res = await getSelectProductData(logSource);
            firstFreeChangeProduct.value = res?.freeChange ?? false;
            log('换品弹窗--手动触发', res);
            if (res?.firstDayProductPopupView?.selectProductListView) {
                const data = res.firstDayProductPopupView?.selectProductListView;
                openSheetWithPop(data, {
                    ...params,
                    topTitle: res.firstDayProductPopupView?.title || topTitle || '',
                });
            } else if (res) {
                sheet.value = createPopupTask({
                    component: () => import('@/components/popups/select-product-sheet/SelectProductSheet.vue'),
                    data: {
                        popupType: CUSTOM_POPUP_TYPE.SELECT_PRODUCT_POPUP,
                        title: selectProductSheetTitle.value,
                        subTitle: selectProductSheetSubTitle.value,
                        firstSelectTag: productListData.value?.firstSelectTag ?? '',
                        productList: selectProductList.value,
                        finalProduct: sinInHomeInfo?.value?.product!,
                        handlePickItem,
                        setChoiceProductAnim,
                        logSource,
                    },
                    options,
                });
            }
        }

        // 添加组任务到队列
        if (!group.value) {
            group.value = addGroupTask({
                options: {
                    name: TaskType.ACTIVE_POPUP,
                    queueTags: [QUEUE_TAGS_TYPE.POPUP],
                },
                tasks: sheet.value ? [sheet.value] : [],
            });
            /** 下发换品弹窗 */
            group.value.start.then(() => {
                // 首日弹窗
                afterSelect.value = false;
            });

            group.value.end.finally(() => {
                // 首日弹窗 && 点的不是再想想
                if (popup && !reopenSelectSheet.value) {
                    afterSelect.value = true;
                }
                sheet.value = null;
                group.value = null;
            });
        } else if (sheet.value) {
            group.value.addSubTask(sheet.value);
            /** 手动触发换品弹窗 */
            group.value.start.then(() => {
                afterSelect.value = false;
            });

            group.value.end.finally(() => {
                if (reopenSelectSheet.value) {
                    afterSelect.value = true;
                }
            });
        }

        return group.value;
    };

    return {
        selectProductSheetTitle,
        selectProductSheetSubTitle,
        selectProductList,
        /** 资源位弹跳动画 */
        setChoiceProductAnim,
        selectProduct,
        handlePickItem,
        openSelectProductSheet,
        choiceProductAnim,
    };
});
