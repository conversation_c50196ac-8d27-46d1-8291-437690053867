import { createModel, useModel } from '@gundam/model';
import { toast } from '@pet/adapt.toast';
import { addShortcut } from '@pet/quantum.AddShortcut';
import { createExecutor } from '@pet/work.task-list-core/utils/framework/DefaultImpl/TriggerFlowModules/ExecuteModule/createExecutor';
import {
    TaskStatus,
    type ResponseTask,
    type TaskActionShare,
    type TaskInfo,
} from '@pet/work.task-list-core/utils/framework/DefaultImpl/types';
import { type TaskLogParam } from '@pet/work.task-list-core/utils/useLoggerEmits';
import { isNotNil } from '@pet/yau.core';
// import TaskSheet from '@/components/task-list/TaskSheet.vue';
import useCaptureDebugLog from '@pet/yau.logger';
import { useRoute } from '@pet/yau.yoda/route/useRouterBack';
import { computedAsync } from '@vueuse/core';
import { invoke } from '@yoda/bridge';

import AndroidBackup from '@/components/popups/retain-popup/AndroidBackup.vue';
import { useTaskSheetState } from '@/hooks/useTaskSheetState';
import { useLogger } from '@/init/logger';
import { useInpushApiModel } from '@/models/api/inpushApiModel';
import { taskApiModel } from '@/models/api/taskApiModel';
import { useConfigModel } from '@/models/configModel';
import { SubBiz, useShareModel } from '@/models/shareModel';
import { signInModel } from '@/models/signInModel';
import { isInLiveHalf } from '@/utils/live';
import { summerLocalStore } from '@/utils/localStore';
import { reportKeyActionEnd, reportKeyActionStart } from '@/utils/log/keyActionLog';
import { TASK_SOURCE } from '@/utils/log/type';
import { useBlockState } from '@/utils/useBlockTaskAndLiveRefresh';

import { useCalendarModel } from './calendarModel';
import { useCommonTaskModel } from './commonTaskModel';
import { homeModel } from './homeModel';
import { QUEUE_TAGS_TYPE, TaskType, usePopupModel } from './popup.model';
import { useSnapShotModel } from './snapShotModel';
import { ETaskType, PriceType, taskGroupEnum } from '../components/task-list/types';

export const TASK_ENTRY = {
    TASK: 'TASK',
};

const { log } = useCaptureDebugLog('taskModule');
// 创建快捷方式任务完成标识
export const SHORT_CUT_TASK_COMPLETED = 'shortCutTaskCompleted';
export const SUBSCRIBE_CALENDAR_TASK_COMPLETED = 'subscribeCalendarTaskCompleted';
export const SUBSCRIBE_INPUSH_TASK_COMPLETED = 'subscribeInpushTaskCompleted';

const typeToStoreKey = {
    [ETaskType.SUBSCRIBE_CALENDAR_TASK]: SUBSCRIBE_CALENDAR_TASK_COMPLETED,
    [ETaskType.SHORT_CUT_TASK]: SHORT_CUT_TASK_COMPLETED,
    [ETaskType.INPUSH_SUBSCRIBE]: SUBSCRIBE_INPUSH_TASK_COMPLETED,
};

// eslint-disable-next-line sonarjs/cognitive-complexity
export const TaskModel = createModel(({ getModelInstance }) => {
    const {
        rawTaskList,
        taskGroupConfigList,
        bubbleTaskIds,
        maxRefreshRetryTimes,
        minRefreshCycleSec,
        isLoading,
        limitedPeriod,
        taskError,
        taskFrontLog,
        tasksRefetch,
        bubbleTaskList,
    } = useModel(taskApiModel);

    const { share, openSharePopup } = useShareModel();
    const { refreshInpush, pausePolling, resumePolling } = useInpushApiModel();
    const { rushCount } = useSnapShotModel();
    const { openPopup } = usePopupModel();
    const blockState = useBlockState()!;
    const { kconfConfig, kconfUEConfig } = useConfigModel();
    const { checkAndAddCalendar } = useCalendarModel();
    const route = useRoute();

    const onlyShowLLCHTask = ref(false);
    const taskEntry = ref();
    const isLongSignDegrade = ref(false);
    const hasPushPermission = ref(true);

    /** 获取频控数据，只读不写 */
    const getTaskFrequency = async (storeKey: string) => {
        // eslint-disable-next-line @typescript-eslint/prefer-nullish-coalescing
        const frequency = ((await summerLocalStore.get(storeKey)) || { allTime: 0 }) as {
            allTime: number; // 缓存中记了几次
        };
        log('LSQ> getFrequency: key=', storeKey, frequency);
        return frequency;
    };

    const handleTaskShare = (taskInfo: TaskInfo) => {
        /**
         * 分享任务，如果扩展字段下发了 popupType 会拉起弹框
         */
        const extParams = taskInfo?.extParams;
        if ('popupType' in extParams && 'popupView' in extParams) {
            openSharePopup(taskInfo, TASK_SOURCE.TASK);
            return false;
        }
        const action = taskInfo?.action as TaskActionShare;
        const subBiz = action?.subBiz ?? '';
        const taskToken = taskInfo.taskToken;
        share({
            subBiz,
            taskToken,
            logExt: { taskId: taskInfo.id },
            onUserSelect: (e) => {
                if ((e.actionKey && e.actionKey.endsWith('copyLink')) || e.actionKey === 'save') {
                    setTimeout(() => {
                        tasksRefetch();
                    }, 800);
                }
            },
        });
    };
    const { isCurrentTaskSheetShow } = useTaskSheetState();

    const subBizId = computed(() => {
        return rawTaskList.value?.[0]?.subBizId ?? 0;
    });

    const checkPushPermissionState = async () => {
        try {
            const { permitted } = await invoke('system.getPushPermission');
            hasPushPermission.value = permitted ? true : false;
        } catch (e) {
            log('!!!----CHECK PERMISSION ERR---');
        }
    };

    const displayFilterTaskList = computedAsync(async () => {
        const rawList = rawTaskList.value;
        const shortCutTaskFrequency = await getTaskFrequency(SHORT_CUT_TASK_COMPLETED);
        const subSubscribeTaskFrequency = await getTaskFrequency(SUBSCRIBE_CALENDAR_TASK_COMPLETED);
        const subscribeInpushTaskFrequency = await getTaskFrequency(SUBSCRIBE_INPUSH_TASK_COMPLETED);
        const eventId = getModelInstance(signInModel)?.calendarRemindInfo.value?.eventId;

        const filteredItems = await Promise.all(
            rawList.map(async (item) => {
                const filterOpenNotification = !(
                    item.jumpType === 'openNotification' &&
                    item.taskStatus === 'COMPLETING_TASK' &&
                    hasPushPermission.value
                );

                const filterShortCut =
                    !(
                        item.extParams?.taskType === ETaskType.SHORT_CUT_TASK &&
                        item.taskStatus === 'COMPLETING_TASK' &&
                        shortCutTaskFrequency.allTime > 0
                    ) &&
                    !(
                        item.extParams?.taskType === ETaskType.SHORT_CUT_TASK &&
                        item.taskStatus === 'TO_TAKE_TASK' &&
                        shortCutTaskFrequency.allTime > 0
                    );

                const filterSubscribeCalendar =
                    !(
                        item.extParams?.taskType === ETaskType.SUBSCRIBE_CALENDAR_TASK &&
                        item.taskStatus === 'COMPLETING_TASK' &&
                        // eslint-disable-next-line @typescript-eslint/strict-boolean-expressions
                        (eventId || subSubscribeTaskFrequency.allTime > 0)
                    ) &&
                    !(
                        item.extParams?.taskType === ETaskType.SUBSCRIBE_CALENDAR_TASK &&
                        item.taskStatus === 'TO_TAKE_TASK' &&
                        // eslint-disable-next-line @typescript-eslint/strict-boolean-expressions
                        (eventId || subSubscribeTaskFrequency.allTime > 0)
                    );

                const filterSubscribeInpush =
                    !(
                        item.extParams?.taskType === ETaskType.INPUSH_SUBSCRIBE &&
                        item.taskStatus === 'COMPLETING_TASK' &&
                        subscribeInpushTaskFrequency.allTime > 0
                    ) &&
                    !(
                        item.extParams?.taskType === ETaskType.INPUSH_SUBSCRIBE &&
                        item.taskStatus === 'TO_TAKE_TASK' &&
                        subscribeInpushTaskFrequency.allTime > 0
                    );

                // for bigday: 在直播间半屏内过滤掉直播间任务
                const filterBigDayLive = !(
                    isInLiveHalf(route) && item.extParams?.taskType === ETaskType.BIGDAY_LIVE_TASK
                );

                const shouldInclude =
                    filterOpenNotification &&
                    filterShortCut &&
                    filterSubscribeCalendar &&
                    filterSubscribeInpush &&
                    filterBigDayLive;
                return shouldInclude ? item : null;
            }),
        );

        // 过滤掉 null 的项
        return filteredItems.filter((item) => item !== null);
    });

    const {
        bubbleTasks,
        currentBubbleTask,
        currentBusinessBubbleTask,
        taskList,
        taskGroup,
        pendingTaskIds,
        doTask: rawDoTask,
    } = useCommonTaskModel({
        taskGroupConfigList,
        bubbleTaskIds,
        limitedPeriod,
        isLongSignDegrade,
        useTaskParams: {
            subBizId,
            taskList: displayFilterTaskList as unknown as ComputedRef<ResponseTask[]>,
            fetchTaskList: tasksRefetch,
            pollInterval: minRefreshCycleSec,
            pollTimes: maxRefreshRetryTimes,
            customBlockFetchRequest: isNotNil(blockState) ? blockState : false,
            executor: createExecutor({
                share: (type, taskInfo) => {
                    handleTaskShare(taskInfo);
                },
                shareByAvatar: (type, taskInfo) => {
                    handleTaskShare(taskInfo);
                },
                emit: async (type, taskInfo) => {
                    // 桌面小组件任务单独适配
                    if (taskInfo.extParams?.taskType === ETaskType.WINDOW_WIDGET) {
                        invoke('platform.loadUri', { url: taskInfo.jumpLink });
                        return false;
                    }
                    if (taskInfo.extParams?.taskType === ETaskType.EXTERNAL_DOWNLOAD_TASK) {
                        const task = openPopup({
                            component: () => import('@/components/download-task/DownLoadTask.vue'),
                            data: {
                                taskId: taskInfo.id,
                                externalTaskId: taskInfo.extParams?.downloadTaskId,
                                subBizId: subBizId.value,
                                enableDirectReport: taskInfo.extParams?.enableDirectReport,
                            },
                            options: {
                                name: TaskType.ACTIVE_POPUP,
                                queueTags: isCurrentTaskSheetShow.value
                                    ? [QUEUE_TAGS_TYPE.TASK_SHEET_POPUP]
                                    : [QUEUE_TAGS_TYPE.POPUP],
                            },
                        });
                        task.end.then(() => {
                            taskFrontLog('任务列表自定义回调，弹框关闭刷新 mainInfo、taskList、inpush');
                            getModelInstance(homeModel)?.refreshHome();
                            tasksRefetch();
                            refreshInpush();
                        });
                    }
                    if (taskInfo.extParams?.taskType === ETaskType.TAKEN_SHARE_TASK) {
                        // 该任务类型不会透传subBiz所以显式通过这种方式调用
                        const taskToken = taskInfo.taskToken;
                        share({
                            subBiz: SubBiz.SHARE_TASK,
                            taskToken,
                            logExt: { taskId: taskInfo.id },
                            onUserSelect: (e) => {
                                if ((e.actionKey && e.actionKey.endsWith('copyLink')) || e.actionKey === 'save') {
                                    setTimeout(() => {
                                        tasksRefetch();
                                    }, 800);
                                }
                            },
                        });
                    }
                    if (taskInfo.extParams?.taskType === ETaskType.SHORT_CUT_TASK) {
                        const res = await addShortcut(
                            {
                                url: kconfUEConfig.value?.shortcutUrl ?? '',
                                icon: kconfUEConfig.value?.shortcutIcon ?? '',
                                name: kconfConfig.value.shortcutConfig?.name ?? '',
                                id: kconfConfig.value.shortcutConfig?.id ?? '',
                            },
                            {
                                bgImg: kconfUEConfig.value?.shortcutBgImg ?? '',
                                bgColor: kconfConfig.value?.shortcutConfig?.bgColor ?? '',
                                btText: kconfConfig.value?.shortcutConfig?.btText ?? '',
                            },
                        );
                        if (!res.isIOS) {
                            // 安卓必弹，如果开启权限
                            openPopup({
                                component: AndroidBackup,
                                data: {},
                                options: {
                                    name: TaskType.ACTIVE_POPUP,
                                    priority: 99999,
                                    queueTags: [QUEUE_TAGS_TYPE.POPUP],
                                },
                            });
                        }
                    }
                    if (taskInfo.extParams?.taskType === ETaskType.SUBSCRIBE_CALENDAR_TASK) {
                        // 任务执行这里不需要刷任务列表接口
                        const res = await checkAndAddCalendar({
                            needRefreshHome: true,
                            needRefreshTask: false,
                            checkIOSAuth: false,
                        });
                        return !!res;
                    }
                    if (taskInfo.extParams?.taskType === ETaskType.INPUSH_SUBSCRIBE) {
                        return true;
                    }
                },
            }),
            toast: () => {},
            onTaskCompleted(tasks, priorityToastTaskId) {
                tasks.forEach(async (item) => {
                    // @ts-expect-error
                    const storeKey = typeToStoreKey[item.extParams.taskType];
                    if (storeKey) {
                        const frequency = await getTaskFrequency(storeKey);
                        if (frequency.allTime > 0) {
                            return;
                        }
                        frequency.allTime++;
                        await summerLocalStore.set(storeKey, frequency);
                    }
                });
                taskFrontLog('任务完成，刷新 mainInfo 和 inpush');
                // 任务列表的任务完成的时候上报
                reportKeyActionEnd({
                    name: `execute_task_list`,
                });
                getModelInstance(homeModel)?.refreshHome();
                refreshInpush();
            },
            logLevel: 'debug',
        },
    });

    const firstNotFinishNormalTask = computed(
        () => taskGroup.value.find((group) => group.groupName !== taskGroupEnum.HUGE_SIGN)?.list?.[0]?.id,
    );

    /**
     * 常规任务数量，用来判断是否折叠
     * TODO: 长签完成后也在常规任务列表
     */
    const normalTaskNum = computed(() =>
        taskGroup.value.reduce(
            (accumulator, g) =>
                g.groupName === taskGroupEnum.NORMAL || g.groupName === taskGroupEnum.LIMITED_TASKS
                    ? accumulator + g.list.length
                    : accumulator,
            0,
        ),
    );

    const getTaskLogParams = (taskLogParam: TaskLogParam, task: TaskInfo) => {
        return {
            title: task.title,
            task_type: task.extParams?.groupName,
            ...taskLogParam.logParam,
            url: window.location.href,
        };
    };
    const { sendClick, sendShow } = useLogger();
    const taskLogReport = (isPopup: 'TRUE' | 'FALSE') => {
        return function report(taskLogParam: TaskLogParam, task: TaskInfo) {
            if (taskLogParam.logType === 'cardShow') {
                sendShow('OP_ACTIVITY_TASK_ITEM', {
                    ...getTaskLogParams(taskLogParam, task),
                    is_popup: isPopup,
                });
            }
            if (taskLogParam.logType === 'buttonClick') {
                sendClick('OP_ACTIVITY_TASK_ITEM', {
                    ...getTaskLogParams(taskLogParam, task),
                    is_popup: isPopup,
                });
            }
        };
    };
    /** 获取任务结构体 */
    const getTaskInfoById = (taskId: number) => {
        return taskList.value.find((item) => item.id === taskId);
    };

    const doTask = (task: TaskInfo) => {
        // 做任务时重新启动inpush轮训, 避免轮训和任务完成的inpush刷新并发
        pausePolling();
        taskFrontLog('doTask 暂停轮询 inpush');
        reportKeyActionStart({
            name: `execute_task_list`,
        });
        rawDoTask(task);
        resumePolling();
        taskFrontLog('doTask 轮询 inpush');
    };

    /**
     * 做任务
     * @param taskId
     */
    const doTaskById = (taskId: number) => {
        const task = getTaskInfoById(taskId);
        if (task) {
            doTask(task);
        } else {
            toast('网络异常，打开任务列表做任务吧');
        }
    };

    const showTaskSheetPopup = () => {
        openPopup({
            component: () => import('@/components/task-list/TaskSheet.vue'),
            data: {},
            options: {
                name: TaskType.TASK_SHEET_UNIQUE_NAME,
                queueTags: [QUEUE_TAGS_TYPE.SHEET],
                ext: {
                    noPlayShowSound: true,
                },
            },
        });
    };

    const openSheet = (source?: string) => {
        taskEntry.value = source;
        onlyShowLLCHTask.value = false;
        showTaskSheetPopup();
    };

    const openLLCHTaskSheet = () => {
        taskEntry.value = TASK_ENTRY.TASK;
        onlyShowLLCHTask.value = true;
        showTaskSheetPopup();
    };

    const hasUnfinishedGetCountsTask = computed(() => {
        return !!taskList.value.filter(
            (item) =>
                item.extParams.groupName === taskGroupEnum.NORMAL &&
                item.extParams.priceType === PriceType.LUCK_RUSH_CHANCE &&
                item.status === TaskStatus.Doing,
        ).length;
    });

    /**
     * 日历任务信息
     */
    const calendarTaskInfo = computedAsync<{ taskInfo: TaskInfo | undefined; finished: boolean }>(() => {
        const task = taskList.value.find((item) => item.extParams?.taskType === ETaskType.SUBSCRIBE_CALENDAR_TASK);
        return {
            taskInfo: task, // inpush任务信息
            finished: task?.status === TaskStatus.Completed, // 任务是否完成
        };
    });

    /**
     * inpush任务信息
     */
    const inpushTaskInfo = computedAsync<{
        taskInfo: TaskInfo | undefined;
        finished: boolean;
    }>(() => {
        const task = taskList.value.find((item) => item.extParams?.taskType === ETaskType.INPUSH_SUBSCRIBE);
        return {
            taskInfo: task, // inpush任务信息
            finished: task?.status === TaskStatus.Completed, // 任务是否完成
        };
    });

    return {
        taskGroup,
        bubbleTasks,
        bubbleTaskList,
        isLoading,
        taskList,
        firstNotFinishNormalTask,
        isLongSignDegrade,
        pendingTaskIds,
        normalTaskNum,
        rushCount,
        currentBubbleTask,
        currentBusinessBubbleTask,
        taskError,
        taskFrontLog,
        taskLogReport,
        tasksRefetch,
        doTask,
        doTaskById,
        openSheet,
        openLLCHTaskSheet,
        onlyShowLLCHTask,
        hasUnfinishedGetCountsTask,
        taskEntry,
        checkPushPermissionState,
        hasPushPermission,
        getTaskFrequency,
        calendarTaskInfo,
        inpushTaskInfo,
    };
});

export const useTaskModel = () => useModel(TaskModel);
