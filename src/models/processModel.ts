import { createUseModel } from '@gundam/model';
import { useGuideState } from '@pet/25cny.guide-directive';
import { toast } from '@pet/adapt.toast';
import { isNotNil } from '@pet/yau.core';

import { PopupType } from '@/services/open-api-docs/home/<USER>/schemas';
import { BeginnerGuideStrategy } from '@/types/abTest';

import { useHomeApiModel } from './api/homeApiModel';
import { useAssistModel } from './assistModel';
import { useBeginnerGuideModel } from './beginnerGuideModel';
import { useBigDayModel } from './bigDayModel';
import { useForwardRushBtnModel } from './forwardRushBtnModel';
import { useGridTaskSheetModel } from './gridTaskSheetModel';
import { TaskType, usePopupModel } from './popup.model';
import { SubBiz } from './shareModel';
import { useSignInModel } from './signInModel';
import { useSwitchModel } from './switchModel';
import { useTeamDialogModel } from './team/teamDialog.model';

/**
 * ProcessModel
 * 流程化触发剧本
 */
export const useProcessModel = createUseModel(() => {
    const { data: homeData, resumePopup } = useHomeApiModel();
    const { startGuideShow } = useGuideState();
    const { openSummerPopup, checkPopupInQueue, currentTasks } = usePopupModel();
    const { openResumeSignInPopup } = useSignInModel();
    const { openGridTaskSheet } = useGridTaskSheetModel();
    const { hasShowedGridTaskSheet } = useSwitchModel();
    const { openTeamPanel, openTeamSuccessPopup, openReceiveTaskFreeCardPopup } = useTeamDialogModel();
    const { taskSuccess } = useForwardRushBtnModel();

    const { openBeginnerGuide, openBeginnerSelectProductPopup, openEnhanceRewardPopup } = useBeginnerGuideModel(); // 新手引导
    const { assistPromise, setShowToast } = useAssistModel();
    const route = useRoute();
    const { openBigDayBannerPopup } = useBigDayModel();

    // 每次home数据变化后，触发流程
    watch(
        homeData,
        // eslint-disable-next-line sonarjs/cognitive-complexity
        async () => {
            // 存在 model 执行但 home 接口没有返回的情况
            if (!homeData.value) {
                return;
            }
            const { shareId, shareCommonParam } = route.query;
            if (isNotNil(shareCommonParam) && typeof shareId === 'string' && shareId?.length) {
                const assistData = await assistPromise; // 如果有回流
                if (assistData) {
                    // 1. 分享回流
                    const { backFlowData, subBiz } = assistData;
                    // 回流成功，打开组队面板
                    if (backFlowData?.code === 1) {
                        if (subBiz === SubBiz.ZUDU_SHARE || subBiz === SubBiz.ZUDU_TASK) {
                            setShowToast(false);
                            openTeamPanel({ enterToast: backFlowData.toast ?? undefined });
                        }
                        return;
                    }
                    // 其他回流失败，有toast就弹
                    backFlowData?.toast && toast(backFlowData.toast);
                }
            }

            // 下发挑战任务详情 且 从未展示过挑战任务半屏，自动拉起半屏。优先级 -1
            if (
                homeData.value.chessboard?.progress.currentGridTaskDetail?.commonTaskDetail &&
                !hasShowedGridTaskSheet.value
            ) {
                openGridTaskSheet(-1);
            }

            if (homeData.value?.popList?.length) {
                switch (homeData.value.popList[0].popupType) {
                    // 新增 存量用户展示无需步数横幅
                    case PopupType.OLD_USER_TIP:
                        console.log('OLD_USER_TIP');
                        openSummerPopup(homeData.value?.popList[0], {
                            taskType: TaskType.PASSIVE_POPUP,
                        });
                        break;
                    // 2. 新手引导
                    case PopupType.BEGINNER_GUIDE:
                        if (
                            !(
                                homeData.value.abTestConfigView?.beginnerGuideStrategy ===
                                BeginnerGuideStrategy.SecondTest
                            )
                        ) {
                            openBeginnerGuide(homeData.value.popList[0]);
                        }
                        break;
                    // 新·新手引导
                    case PopupType.BEGINNER_SELECT_PRODUCT_POPUP:
                        openBeginnerSelectProductPopup(homeData.value.popList[0]);
                        break;
                    case PopupType.ENHANCE_REWARD_POPUP:
                        openEnhanceRewardPopup(homeData.value.popList[0]);
                        break;
                    // 3. 其他弹窗流程（组合弹窗）
                    case PopupType.HUGE_SIGN_IN_RESUME:
                        // 续签弹窗
                        if (!checkPopupInQueue(homeData.value?.popList[0].popupType) && resumePopup.value) {
                            openResumeSignInPopup();
                        }
                        break;
                    // @ts-expect-error
                    case PopupType.BIG_DAY_BANNER_POPUP:
                        openBigDayBannerPopup();
                        break;
                    case PopupType.TEAM_SUCCESS:
                        // 组队格子弹窗，如果在展示关闭
                        currentTasks.value?.forEach((task) => {
                            task.name === TaskType.TEAM_GRID_POPUP && task.triggerDestroy();
                        });
                        openTeamSuccessPopup(homeData.value?.popList[0]);
                        break;
                    case PopupType.TEAM_SIGN_REWARD:
                        openReceiveTaskFreeCardPopup(homeData.value?.popList[0]);
                        break;
                    // 4. 通用弹窗流程
                    default:
                        openSummerPopup(homeData.value?.popList[0], {
                            taskType: TaskType.PASSIVE_POPUP,
                        });
                        break;
                }
            }

            // 5. 其他流程，如引导挂载
            startGuideShow();

            await taskSuccess();
        },
        {
            immediate: true,
        },
    );

    return {};
});
