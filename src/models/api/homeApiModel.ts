// import { inHeadless, removeSSGRoot } from '@biz/utils/ssg';
import { createUseModel, isDoneLikeState, isLoadingState } from '@gundam/model';
import { useRestQueryWithType } from '@gundam/model/utils';
import { setDynamicSignPaths } from '@gundam/sig3';
import { useCdnLevel } from '@pet/25cny.cdn-image/useCdnLevel';
import { useDowngradeLevel, type LevelType } from '@pet/25cny.downgrade-level';
import { useRenderGroup } from '@pet/vision.effect-item/renderGroup';
import { nativeCloud } from '@pet/yau.cloud-native';
import { isNotNil } from '@pet/yau.core';
import { getUrlSearchParams } from '@pet/yau.core/url';
import useCaptureDebugLog from '@pet/yau.logger';
import { injectVisibility } from '@pet/yau.yoda';
import { until, whenever } from '@vueuse/core';
// import { summerLocalStore } from '@/utils/localStore';

import { homeControllerHomePage } from '@/services/open-api-docs/home/<USER>/vue-apollo-model-client';
import type { HugeSignInInterruptResumePopup } from '@/services/open-api-docs/home/<USER>/schemas';
import { HugeSignInStatus, PopupType } from '@/services/open-api-docs/home/<USER>/schemas';
import { getLocalPrefix, inHeadless, removeSSGRoot, setLocalMapConfig } from '@/utils/ssg';

import { usePopupModel } from '../popup.model';
import { useSocialGuide0714Model } from '../socialGuide0714Model';
import { getDataIfNotError } from '../utils/util';

const { log } = useCaptureDebugLog('homeApi');

/**
 * '' 正常刷新主接口
 * 'LUCK_SHAKE_SUDOKU' 后端不会返回弹窗队列
 * 'NEED_START_NEW_ROUND' 开启新的一轮长签周期活动
 */
type Scene = '' | 'LUCK_SHAKE_SUDOKU' | 'NEED_START_NEW_ROUND';

const transformDowngradeLevel = (level?: number | null): LevelType => {
    return ([0, 1, 2, 3] as const).includes(level as LevelType) ? (level as LevelType) : 0;
};

/**
 * 主接口 Api Model
 * 这里只负责主接口数据的获取和阻塞刷新
 * 其他数据的获取分发和处理请在 homeModel 中进行
 */
// eslint-disable-next-line sonarjs/cognitive-complexity
export const useHomeApiModel = createUseModel(() => {
    const { switchCdnLevel } = useCdnLevel();
    const { initOriginState } = useDowngradeLevel();
    const { setRenderGroup } = useRenderGroup();
    const { currentBlockTasks } = usePopupModel();
    /** 页面是否可见 */
    const pageVisible = injectVisibility()?.visible ?? ref(true);
    const localPrefix = getLocalPrefix();
    const scene = ref<Scene>('');
    const externalTouchParamRef = ref('');

    const { isInitSocialGuide, isFromSocialGuide, canAutoOpenTeamPanel, inSocialProcess } = useSocialGuide0714Model();

    const externalTouchParam = getUrlSearchParams().externalTouchParam;
    if (externalTouchParam) {
        externalTouchParamRef.value = externalTouchParam;
    }
    // 主接口Rest
    const homeRest = useRestQueryWithType(homeControllerHomePage)({
        variables: () => ({
            entry_src: getUrlSearchParams().entry_src,
            // 0714迭代社交导流-如果是通过社交导流进入，第一次请求时需要规避掉派发弹窗
            scene: isFromSocialGuide.value && isInitSocialGuide.value ? 'SOCIAL_TEAM_TASK_ACCESS' : scene.value,
            // 这里是为了保证新逻辑生效请求到续签新数据
            // https://docs.corp.kuaishou.com/d/home/<USER>
            resumeVersion: 'v2',
            // 扩量的端外快链会带这个参数，只需要进页面首次把值带上，watchOnce里把这个值清空了
            externalTouchParam: externalTouchParamRef.value,
            /**
             * ⚠️ ❗️注意：这里如果新增参数，检查vite.config.ts里的预请求配置
             */
        }),
        skip: () => isDoneLikeState(homeRest.status.value),
    });

    /**数据 */
    const data = computed(() => getDataIfNotError(homeRest.info));
    // 续签弹窗数据
    const resumePopup: Ref<HugeSignInInterruptResumePopup | undefined> = ref(undefined);

    // // 清理缓存后门
    // if (getUrlSearchParams()?.clearStore === '1') {
    //     summerLocalStore.remove();
    // }

    whenever(
        data,
        (newData) => {
            isInitSocialGuide.value = false;
            const hasTeamSuccess =
                newData.teamEntryView?.teamStatus === 3 ||
                newData.teamEntryView?.teamStatus === 4 ||
                newData.teamEntryView?.teamStatus === 5;
            canAutoOpenTeamPanel.value =
                isFromSocialGuide.value &&
                !hasTeamSuccess &&
                newData.signInStatus === HugeSignInStatus.PROCESSING &&
                newData.popList?.findIndex((item) => item.popupType !== PopupType.TEAM_EXIT) === -1;
            inSocialProcess.value = isFromSocialGuide.value && canAutoOpenTeamPanel.value;

            const currentStationIndex = newData.chessboard?.currentStationIndex;
            setLocalMapConfig(`${localPrefix}isFirstTimeLoaded`, JSON.stringify(true));
            setLocalMapConfig(`${localPrefix}latest-visited-time`, new Date().toString());
            if (isNotNil(currentStationIndex)) {
                log(
                    'next-map-key的值=',
                    currentStationIndex,
                    newData.chessboard?.stationList?.[currentStationIndex + 1]?.stationInfo?.stationThemeKey || '',
                );
                setLocalMapConfig(
                    `${localPrefix}next-map-key`,
                    newData.chessboard?.stationList?.[currentStationIndex + 1]?.stationInfo?.stationThemeKey || '',
                );
            }
            // externalTouchParam 这个值只需要首次传
            if (externalTouchParam) {
                externalTouchParamRef.value = '';
            }
        },
        { flush: 'pre', once: true },
    );

    // 接口数据变化时，且有数据时
    whenever(
        data,
        (newData) => {
            log(
                '主接口数据变化：',
                'inHeadless=',
                inHeadless(),
                '主接口数据的弹窗数据',
                JSON.stringify(newData.popList),
            );
            // 根据主接口信息动态设置需要加签的路径
            setDynamicSignPaths(newData.needSig3Path ?? []);

            // 降级
            const { cdnLevel, animationLevel, audioLevel, liveLevel, videoLevel, transparentVideoLevel } =
                newData.degradeConfigView ?? {};
            const level = [0, 1, 2].includes(cdnLevel as number)
                ? (`P${cdnLevel as 0 | 1 | 2}` as `P${0 | 1 | 2}`)
                : 'NONE';

            switchCdnLevel(level);
            initOriginState({
                cny2025VideoLevel: transformDowngradeLevel(videoLevel),
                cny2025AnimationLevel: transformDowngradeLevel(animationLevel),
                cny2025AudioLevel: transformDowngradeLevel(audioLevel),
                cny2025TransparentVideoLevel: transformDowngradeLevel(transparentVideoLevel),
            });
            setRenderGroup(newData.homeFEConstantsConfig?.renderGroup ?? 2);
            // 更新续签弹窗数据
            if (data?.value?.popList?.length && data?.value?.popList[0]?.popupType === PopupType.HUGE_SIGN_IN_RESUME) {
                resumePopup.value = data.value.popList[0];
            }
        },
        { flush: 'pre' },
    );

    // 当前是否有被阻塞的刷新请求
    const needRefetch = ref(false);

    const blockingRefresh = ref(false);
    let blockTimeout: ReturnType<typeof setTimeout>;
    /**
     * 刷新主接口的条件，
     * 1. 有些特殊弹窗不会阻塞刷新，不会加length
     * 2. 页面可见
     */
    const canRefetch = computed(
        () => pageVisible.value && currentBlockTasks.value.length === 0 && !blockingRefresh.value,
    );

    const blockingRefreshHandler = (blocking: boolean, timeout = 30000) => {
        clearTimeout(blockTimeout);
        blockingRefresh.value = blocking;
        if (blocking) {
            // 设置超时时间避免锁死
            blockTimeout = setTimeout(() => {
                blockingRefresh.value = false;
            }, timeout);
        }
    };

    /**
     * 刷新主接口方法
     * @param aScene
     *     '' 正常刷新主接口
     *     'LUCK_SHAKE_SUDOKU' 后端不会返回弹窗队列
     *     'NEED_START_NEW_ROUND' 开启新的一轮长签周期活动
     * @returns Promise<HomeData> await后，拿到的homeData一定是最新的
     */
    const refreshHome = async (aScene?: Scene) => {
        log(
            `调用 refreshHome; aScene: ${aScene ?? ''}, canRefetch: ${canRefetch.value}, blockingRefresh: ${blockingRefresh.value}`,
        );
        if (aScene) {
            scene.value = aScene;
            log('刷新主接口(非阻塞Scene)', aScene);
            await homeRest.refetch();
            scene.value = '';
            return data;
        }

        // 如果当前正在请求，就等
        if (homeRest.loading.value) {
            await until(homeRest.info.loading).toBe(false);
            return data;
        }

        // 被阻塞，就改变标志
        if (!canRefetch.value) {
            needRefetch.value = true;
            await until(needRefetch).toBe(false);
            return data;
        }

        // 不被阻塞，直接刷新
        needRefetch.value = false;
        log('刷新主接口(没被阻塞)');
        await homeRest.refetch();
        return data;
    };

    // 如果当前可以刷新且有被阻塞的刷新请求
    whenever(canRefetch, async () => {
        if (needRefetch.value) {
            log('刷新主接口(被阻塞后，由canRefetch变化触发)');
            await homeRest.refetch();
            needRefetch.value = false;
        }
    });

    // 当主接口正常拿到数据后，移除SSG
    whenever(
        () => data.value ?? homeRest.error.value,
        async () => {
            removeSSGRoot(0.5);
            nativeCloud.hide();
        },
    );

    // 页面可见时，自动刷新主接口
    whenever(pageVisible, async () => {
        refreshHome();
    });

    return {
        /** 主接口数据 */
        data,
        /**
         * 刷新主接口方法
         * @param aScene '' 正常刷新主接口
         * 'LUCK_SHAKE_SUDOKU' 后端不会返回弹窗队列
         * 'NEED_START_NEW_ROUND' 开启新的一轮长签周期活动
         */
        refreshHome,
        /** 强制刷新主接口，奇葩需求需要 */
        forceRefreshHome: homeRest.refetch,
        /** 手动阻塞主接口刷新 */
        blockingRefreshHandler,
        /** 加载中 */
        loading: computed(() => isLoadingState(homeRest.status.value)),
        /** 当接口请求失败时的result，平常是 undefined */
        result: computed<number>(() => homeRest.info.error?.data?.result),
        /** 当接口请求失败时的error_msg，平常是 undefined */
        errorMsg: computed<number>(() => homeRest.info.error?.data?.error_msg),
        /** 续签弹窗数据 */
        resumePopup,
        localPrefix,
    };
});
