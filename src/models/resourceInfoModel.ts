import { createModel } from '@gundam/model';
import { isNotNil } from '@pet/yau.core';
import useCaptureDebugLog from '@pet/yau.logger';
import { useOpenPage, useRouterBack, useRoute } from '@pet/yau.yoda';
import { whenever } from '@vueuse/core';
import { invoke } from '@yoda/bridge';

import { useHomeModel } from '@/models/homeModel';
import { LinkTypeEnum } from '@/services/open-api-docs/home/<USER>/schemas';
import type { HomeShowActivityView } from '@/services/open-api-docs/home/<USER>/schemas';
import { isInLiveHalf } from '@/utils/live';

export const resourceInfoModel = createModel(() => {
    const LTSourceInfo = ref<HomeShowActivityView>();
    const LBSourceInfo = ref<HomeShowActivityView>();
    const RTSourceInfo = ref<HomeShowActivityView>();
    const RBSourceInfo = ref<HomeShowActivityView>();
    const { log } = useCaptureDebugLog('resource');
    const route = useRoute();

    const { homeResourceMap } = useHomeModel(); // 主接口
    const openPage = useOpenPage();

    const jsBridgeActionFormatter = (linkUrl: string) => {
        let bridgeInfo: {
            name: string;
            params: Record<string, any>;
            poll?: boolean;
        };
        try {
            bridgeInfo = JSON.parse(linkUrl) as typeof bridgeInfo;
        } catch (e) {
            bridgeInfo = {
                name: '',
                params: {},
            };
        }
        return {
            type: LinkTypeEnum.JS_BRIDGE,
            bridgeName: bridgeInfo.name,
            bridgeParams: bridgeInfo.params,
            needPoll: bridgeInfo.poll,
        };
    };

    const handleResourceClick = (btnView: HomeShowActivityView | null | undefined) => {
        if (!isNotNil(btnView)) {
            return;
        }
        const { linkType, linkUrl } = btnView;
        // 当类型为PULL_TASK_LIST_PANEL时，不需要校验linkUrl
        if (!isNotNil(linkType) || (!isNotNil(linkUrl) && linkType !== LinkTypeEnum.PULL_TASK_LIST_PANEL)) {
            return;
        }

        if (!linkUrl) {
            return;
        }

        switch (linkType) {
            case LinkTypeEnum.JS_BRIDGE:
                const formattedBridgeInfo = jsBridgeActionFormatter(linkUrl);
                invoke(formattedBridgeInfo.bridgeName as any, formattedBridgeInfo.bridgeParams as any);
                break;
            case LinkTypeEnum.JUMP_H5:
            case LinkTypeEnum.KWAI_LINK:
                openPage(linkUrl!, { forceOpenInNewWebview: true, keepQuery: false });
                break;
            case LinkTypeEnum.LIVE_WATCH:
                // 如果已经在直播间半屏内，直接关闭半屏
                if (isInLiveHalf(route)) {
                    log('在直播间半屏内，关闭webview', JSON.stringify(btnView));
                    invoke('webview.close');
                } else {
                    // 否则说明是正常链路，跳转
                    openPage(linkUrl!, { forceOpenInNewWebview: true, keepQuery: false });
                }
                break;
            case LinkTypeEnum.PULL_NEW_TASK:
            case LinkTypeEnum.PULL_TASK_LIST_PANEL:
            case LinkTypeEnum.CLOUD_TRANSITION:
            case LinkTypeEnum.RESERVATION:
                break;
            case LinkTypeEnum.SOCIAL:
                break;
            case LinkTypeEnum.UNKNOWN:
                break;
            case LinkTypeEnum.VERSION_UPGRADE:
                break;
            default:
                break;
        }
    };

    whenever(
        homeResourceMap,
        () => {
            LTSourceInfo.value = homeResourceMap.value?.['RESOURCE_LEFT_TOP'];
            LBSourceInfo.value = homeResourceMap.value?.['RESOURCE_LEFT_BOTTOM'];
            RTSourceInfo.value = homeResourceMap.value?.['RESOURCE_RIGHT_TOP'];
            RBSourceInfo.value = homeResourceMap.value?.['RESOURCE_RIGHT_BOTTOM'];
        },
        { immediate: true },
    );

    return {
        handleResourceClick,
        LTSourceInfo,
        LBSourceInfo,
        RTSourceInfo,
        RBSourceInfo,
    };
});
