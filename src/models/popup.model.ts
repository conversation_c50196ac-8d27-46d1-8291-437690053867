import { createUseModel } from '@gundam/model';
import type { ClickPosition, PopupBtnClick } from '@pet/25cny.packet/type';
import { TaskStatusEnum } from '@pet/adapt.queue-api/main/utils';
import { useTaskQueueModel, type Options } from '@pet/adapt.task-queue/index';
import type { CreatePopupTaskType } from '@pet/adapt.task-queue/task-modules/popupTask';
import { toast } from '@pet/adapt.toast';
import { toastIfHasError } from '@pet/base.model-utils-helper';
import { formatResponseTaskInfo } from '@pet/work.task-list-core/utils/framework/DefaultImpl/ListDataModule/formatter';
import type { ResponseTask } from '@pet/work.task-list-core/utils/framework/DefaultImpl/types';
import { isNotNil, sleep } from '@pet/yau.core';
import useCaptureDebugLog from '@pet/yau.logger';
import { injectVisibility, useOpenPage } from '@pet/yau.yoda';

import PassCardPng from '@/assets/team/pass-card.png';
import { useLogger } from '@/init/logger';
import { configModel as cityConfigModel } from '@/models/city/configModel';
import { cityHomeModel } from '@/models/city/homeModel';
import { configModel } from '@/models/configModel';
import { forwardRushBtnModel } from '@/models/forwardRushBtnModel';
import { homeModel } from '@/models/homeModel';
import { shareModel } from '@/models/shareModel';
import {
    SHORT_CUT_TASK_COMPLETED,
    SUBSCRIBE_CALENDAR_TASK_COMPLETED,
    SUBSCRIBE_INPUSH_TASK_COMPLETED,
    TaskModel,
} from '@/models/taskModel';
import { teamDataModel } from '@/models/team/teamData.model';
import { teamDialogModel } from '@/models/team/teamDialog.model';
import { ROUTE_NAME } from '@/router/name';
import {
    LinkTypeEnum,
    PopupType,
    type CommonTaskDetail,
    type SummerCommonButtonView,
    type SummerWarmupInPushView,
} from '@/services/open-api-docs/home/<USER>/schemas';
import { summerLocalStore } from '@/utils/localStore';
import { reportKeyActionStart, reportKeyActionEnd } from '@/utils/log/keyActionLog';
import { createCnyPopup, openCnyInpush } from '@/utils/popupTransform';
import type { SummerPopup, PopupTaskConfig, POPUP_SOURCE, TASK_SOURCE } from '@/utils/popupTransform/types';

import { useWithDrawModel } from './api/withdrawApiModel';
import { SoundType, useAudioModel } from './audioModel';
import { useCalendarModel } from './calendarModel';
import { ExecuteType, useTaskExecuteModel } from './taskExecuteModel';
import { px2rem } from '@pet/core.mobile';

/** 弹窗任务Name，用于判断当前queue task任务是什么，判断是否需要阻塞主接口 */
export enum TaskType {
    /**
     * 宣导 & 新手引导
     */
    NEW_GUIDE = 'NEW_GUIDE',
    /**
     * 跟手弹窗
     */
    ACTIVE_POPUP = 'ACTIVE_POPUP',
    /**
     * 派发弹窗
     */
    PASSIVE_POPUP = 'PASSIVE_POPUP',
    /**
     * inpush 通知
     */
    INPUSH = 'INPUSH',
    /**
     *  气泡、小手引导
     */
    HAND_GUIDE = 'HAND_GUIDE',

    /**
     * 任务列表，不阻塞接口刷新、其他弹窗的弹出
     */
    SHEET = 'TASK_SHEET',

    /**
     * 子任务无需单独的name
     */
    NONE = 'NONE',

    /**
     * 切页面需要强刷home接口，并且允许其他弹窗的弹出的面板
     */
    SPECIAL_SHEET = 'SPECIAL_SHEET',

    /**
     * 组任务
     */
    GROUP_TASK = 'GROUP_TASK',

    /**
     * 任务列表的name
     */
    TASK_SHEET_UNIQUE_NAME = 'TASK_SHEET_UNIQUE_NAME',

    /**
     * 格子任务弹窗
     */
    GRID_TASK_POPUP = 'GRID_TASK_POPUP',

    /**
     * 补签弹窗的name（避免重复出现&刷主接口）
     */
    HUGE_SIGN_IN_RESUME = PopupType.HUGE_SIGN_IN_RESUME,
    /** 组队格子弹窗 */
    TEAM_GRID_POPUP = PopupType.TEAM_GRID_RECO_FRIENDS_CARD,
}

/** 弹窗任务标签，用于判断当前queue task任务类型，判断是否叠加或者避让 */
export enum QUEUE_TAGS_TYPE {
    /** 弹窗类型 */
    POPUP = 'popup',
    /** 任务列表弹窗 */
    TASK_SHEET_POPUP = 'task-sheet-popup',
    /** 面板类型，支持弹窗叠加 */
    SHEET = 'sheet',
    /** 引导，避让弹窗，面板，事件 */
    GUIDE = 'guide',
    /** inpush */
    INPUSH = 'inpush',
    /** 事件 */
    EVENT = 'event',
    /** 挽留弹窗 */
    RETAIN = 'retain',
    /** 选品弹窗 */
    SELECT_PRODUCT = 'SELECT_PRODUCT',
}

/** 阻塞性弹窗类型 */
export const BLOCK_TASK_TYPES = [
    TaskType.NEW_GUIDE,
    TaskType.ACTIVE_POPUP,
    TaskType.PASSIVE_POPUP,
    TaskType.SHEET,
    TaskType.SPECIAL_SHEET,
    TaskType.GROUP_TASK,
];

export const PASSIVE_POPUP_PRIORITY: { [key: string]: number } = {
    /** 补签 */
    [PopupType.HUGE_SIGN_IN_RESUME]: 100,
    /** 组队奖励弹窗 */
    [PopupType.TEAM_SIGN_REWARD]: 90,
    /** 奖品过期弹窗 */
    [PopupType.FINAL_RECEIVE_EXPIRE]: 80,
    /** 彻底断签弹窗 */
    [PopupType.HUGE_SIGN_IN_CHALLENGE_FAILURE]: 70,
    /** 新手引导 */
    [PopupType.BEGINNER_GUIDE]: 40,
    /** 最终大奖领奖弹窗 */
    [PopupType.FINAL_RECEIVE_LLAWD]: 30,
    /** 预告 inpush 弹窗 */
    [PopupType.HUGE_SIGN_IN_SUBSCRIBE_POPUP]: 20,
};

export enum CUSTOM_POPUP_TYPE {
    /** 日历面板弹窗 */
    CALENDAR_POPUP = 'CALENDAR_POPUP',
    /** 选品面板弹窗 */
    SELECT_PRODUCT_POPUP = 'SELECT_PRODUCT_POPUP',
    // 挑战任务半屏
    GRID_TASK_SHEET = 'GRID_TASK_SHEET',
}

type PopupButtonClickHandler = (
    popup: SummerPopup,
    buttonInfo: SummerCommonButtonView,
    close: () => void,
    position?: ClickPosition,
) => boolean;

/** 分享任务弹窗类型 */
const PopupShareTask = [
    PopupType.LS_TIME_LIMITED_ASSIST_TASK_LLCN,
    PopupType.LS_TIME_LIMITED_ASSIST_TASK_LLCH,
    PopupType.LS_TIME_LIMITED_REFLUX_ASSIST_TASK_LLCH,
    PopupType.LS_TIME_LIMITED_REFLUX_ASSIST_TASK_LLCN, // 限时任务下发弹框
    PopupType.LS_TIME_LIMITED_ASSIST_DOUBLE_LLCN,
    PopupType.LS_TIME_LIMITED_ASSIST_DOUBLE_LLCH,
    // PopupType.LS_SHARE_LLCN_TASK,
];

export const usePopupModel = createUseModel(({ getModelInstance }) => {
    const exemptPopup = ['TASK_SHEET_UNIQUE_NAME'];
    const { log } = useCaptureDebugLog('popup');
    const { sendClick, sendShow } = useLogger();
    const { playSound } = useAudioModel();
    const route = useRoute();

    const {
        openPopup: openPopupByQueue,
        showInpush,
        currentTasks,
        addEventTask,
        createPopupTask: createPopupTaskByQueue,
        addGroupTask,
        createGroupTask,
        queue,
        tasks,
        addQueueInterceptor,
        reset: resetQueue,
    } = useTaskQueueModel();
    const { getWithdrawUrl } = useWithDrawModel();

    const openInpush = (inpush: Partial<SummerWarmupInPushView & { duration: number }>, options?: Options) => {
        openCnyInpush(inpush, { ...options, name: TaskType.INPUSH, queueTags: [QUEUE_TAGS_TYPE.INPUSH] }, showInpush);
    };

    const { executeTask, doGridTask } = useTaskExecuteModel();
    const { checkAndAddCalendar, refreshCalendar } = useCalendarModel();
    const openPage = useOpenPage();

    // 拦截器添加弹窗声音
    addQueueInterceptor({
        // eslint-disable-next-line sonarjs/cognitive-complexity
        onBeforeAddTaskToQueue: (task, setOptions) => {
            // popup类型任务数据统一传入音效方法
            if (task?.type === 'popup') {
                // 判断是否播放音效
                !task.ext?.noPlayShowSound && playSound(SoundType.DIALOG_OPEN);

                if (task.data?.flyToTarget) {
                    const t = task;
                    t.data.playFlySound = playSound(SoundType.FLY);
                }
            }

            if (task?.data?.popupType === CUSTOM_POPUP_TYPE.CALENDAR_POPUP) {
                const existTask = queue.findTaskByPopupType(CUSTOM_POPUP_TYPE.CALENDAR_POPUP);
                if (existTask !== null) {
                    return;
                }
            }

            if (task?.data?.popupType === CUSTOM_POPUP_TYPE.SELECT_PRODUCT_POPUP) {
                const existTask = queue.findTaskByPopupType(CUSTOM_POPUP_TYPE.SELECT_PRODUCT_POPUP);
                if (existTask !== null) {
                    console.error('[首日打卡] 队列中已有选品面板');
                    return;
                }
            }

            // 挑战任务半屏
            if (task?.data?.popupType === CUSTOM_POPUP_TYPE.GRID_TASK_SHEET) {
                const existTask = queue.findTaskByPopupType(CUSTOM_POPUP_TYPE.GRID_TASK_SHEET);
                if (existTask !== null) {
                    return;
                }
            }

            // 挑战任务修改
            if (task?.name === 'autoRushByTask') {
                const existTask = queue.findTaskByName('autoRushByTask');
                if (existTask !== null) {
                    return;
                }
            }

            // 从这之后任务能正常插入队列
            reportKeyActionStart({
                // 根据任务的类型上报链路现在只有 inpush/popup/event 三类
                name: `queue_task_type_${task.type}`,
            });

            // 任务走到start就是开始执行了这里结束，组件通常是mounted执行task.start 表示成功执行
            task.start.then(() => {
                reportKeyActionEnd({
                    name: `queue_task_type_${task.type}`,
                });
            });

            if (task.type === 'popup') {
                // eslint-disable-next-line @typescript-eslint/restrict-template-expressions
                log(`[QUEUE_TASK]: IN_QUEUE; popupType: ${task?.data?.popupType ?? ''}; name: ${task.name ?? ''}`);

                task.end.then(() => {
                    log(
                        // eslint-disable-next-line @typescript-eslint/restrict-template-expressions
                        `[QUEUE_TASK]: INTERCEPT_END; popupType: ${task?.data?.popupType ?? ''}; name: ${task.name ?? ''}`,
                    );
                });
            }

            return task;
        },
    });

    /** 前置处理跟手弹窗优先级的 createPopupTask  */
    const createPopupTask: CreatePopupTaskType = (params) => {
        // 跟手弹窗优先级处理
        return createPopupTaskByQueue({
            ...params,
            options: {
                ...params.options,
                priority:
                    params?.options?.priority ?? (params?.options?.name === TaskType.ACTIVE_POPUP ? 1000 : undefined),
            },
        });
    };

    /** 前置处理跟手弹窗优先级的 openPopup  */
    const openPopup: CreatePopupTaskType = (params) => {
        // 跟手弹窗优先级处理
        return openPopupByQueue({
            ...params,
            options: {
                ...params.options,
                priority:
                    params?.options?.priority ?? (params?.options?.name === TaskType.ACTIVE_POPUP ? 1000 : undefined),
            },
        });
    };

    /** 任务点击处理 */
    // eslint-disable-next-line sonarjs/cognitive-complexity
    const handleTaskPopupBtnClick = async (task: CommonTaskDetail, popup: SummerPopup, close: () => void) => {
        const popupType = popup.popupType;
        // 双端互拉任务
        if (
            popupType === PopupType.LS_INVOKE_APP_LLCH ||
            popupType === PopupType.LS_INVOKE_APP_LLCN ||
            popupType === PopupType.EVE_LS_INVOKE_APP_SHAKE
        ) {
            executeTask(task as ResponseTask, ExecuteType.pullEach);
            close();
        }
        // 观看直播任务
        if (
            popupType === PopupType.LS_WATCH_LIVE_TASK_LLCN ||
            popupType === PopupType.LS_WATCH_LIVE_TASK_LLCH ||
            popupType === PopupType.EVE_LS_WATCH_LIVE_TASK_SHAKE
        ) {
            const res = await executeTask(task as ResponseTask, ExecuteType.JSBridge);
            close();
        }
        // 关注任务
        if (
            popupType === PopupType.LS_FOLLOW_TASK_LLCN ||
            popupType === PopupType.LS_FOLLOW_TASK_BLESS ||
            popupType === PopupType.LS_FOLLOW_TASK_LLCH ||
            popupType === PopupType.EVE_LS_FOLLOW_TASK_SHAKE
        ) {
            const activityId = task.extParams?.activityId;
            const followRefer = Number(task.extParams?.followRefer);
            const res = await executeTask(task as ResponseTask, ExecuteType.followAccount, activityId, followRefer);
            if (res?.followState === 1) {
                toast('关注成功');
                close();
            } else {
                toast('关注失败，请稍后重试');
            }
        }

        // 激励视频任务
        if (popupType === PopupType.LS_COMMON_ZT_TASK_LLCN) {
            executeTask(task as ResponseTask, ExecuteType.encourageVideo);
            close();
        }

        // 暑期观看直播任务调用快链
        if (popupType === PopupType.LS_WATCH_VIDEO_LLCN_TASK) {
            executeTask(task as ResponseTask, ExecuteType.jumpToNative);
            close();
        }

        // push权限任务
        if (popupType === PopupType.LS_PUSH_SWITCH_LLCN_TASK) {
            executeTask(task as ResponseTask, ExecuteType.reservePush);
            close();
        }

        // 端内 inpush 提醒任务
        if (popupType === PopupType.HUGE_SIGN_IN_SUBSCRIBE_POPUP) {
            const formattedTask = formatResponseTaskInfo(task as ResponseTask);
            getModelInstance(TaskModel)?.doTask(formattedTask);
            close();
        }

        // 格子任务统一调用 doTask 去完成（看广告，看直播）
        if (popupType === PopupType.GRID_COMMON_TASK_LLCN) {
            const formattedTask = formatResponseTaskInfo(task as ResponseTask);

            // 复用主页面挑战格子做任务逻辑
            doGridTask(task, formattedTask);

            // getModelInstance(TaskModel)?.doTask(formattedTask);

            // 格子任务点击主按钮不关闭弹窗，直到 用户主动点击关闭弹窗 或 任务已完成自动关闭弹窗
            // 向前冲下发的任务
        }
        if (
            popupType === PopupType.LS_KSWITCH_INPUSH_TASK ||
            popupType === PopupType.LS_ADD_SHORTCUT_TASK ||
            popupType === PopupType.LS_VISIT_SHARE_VIDEO_VENUE_TASK ||
            popupType === PopupType.LS_VISIT_SHARE_OPEN_RED_PACKET_TASK ||
            popupType === PopupType.LS_SEARCH_KEY_WORD_TASK ||
            popupType === PopupType.LS_FOLLOW_RECO_AUTHOR_TASK ||
            popupType === PopupType.LS_SUBSCRIBE_CALENDAR_TASK
        ) {
            getModelInstance(TaskModel)?.doTaskById(task.taskId);
            close();
        }

        if (PopupShareTask.includes(popupType)) {
            getModelInstance(shareModel)?.shareChain({
                subBiz: task.shareSubBiz ?? '',
                taskToken: task.taskToken,
                logExt: {
                    taskId: task.taskId,
                },
            });
            close();
        }

        if (popupType === PopupType.LS_SHARE_LLCN_TASK) {
            getModelInstance(shareModel)?.share({
                subBiz: task.shareSubBiz ?? '',
                taskToken: task.taskToken,
                logExt: {
                    taskId: task.taskId,
                },
            });
            close();
        }
    };

    /** 通用点击按钮事件处理 */
    const handleCommonPopupButtonClick: PopupButtonClickHandler = (
        popup: SummerPopup,
        buttonInfo: SummerCommonButtonView,
        close: () => void,
        // eslint-disable-next-line sonarjs/cognitive-complexity
    ): boolean => {
        // 关闭按钮
        if (buttonInfo.linkType === LinkTypeEnum.CLOSE) {
            close();
            // 存量用户提示弹窗关闭时主动刷新主接口
            if (
                popup.popupType === PopupType.OLD_USER_TIP ||
                popup.popupType === PopupType.UNDERTAKE_RED_PACKET_POPUP
            ) {
                getModelInstance(homeModel)?.refreshHome('');
                console.log('refreshHome old user tip 关闭按钮');
            }
            // 不return因为可以走 extra
        }

        // 跳转
        if (buttonInfo.linkType === LinkTypeEnum.JUMP_H5) {
            if (isNotNil(buttonInfo.linkUrl)) {
                openPage(buttonInfo.linkUrl, { forceOpenInNewWebview: true, keepQuery: false });
            }
            return true;
        }

        // 拉端
        if (buttonInfo.linkType === LinkTypeEnum.KWAI_LINK) {
            if (isNotNil(buttonInfo.linkUrl)) {
                openPage(buttonInfo.linkUrl, { forceOpenInNewWebview: true, keepQuery: false });
            }
            return true;
        }

        if (buttonInfo.linkType === LinkTypeEnum.LUCK_SHAKE_SUDOKU) {
            getModelInstance(homeModel)?.blockingRefreshHandler(true);
            close();
            sleep(333).finally(async () => {
                await getModelInstance(forwardRushBtnModel)?.handleForwardRushBtnClick(true);
            });
            return true;
        }

        // 提现跳转钱包
        if (buttonInfo.linkType === LinkTypeEnum.LLWDW) {
            toastIfHasError(async () => {
                const url = await getWithdrawUrl();
                if (url) {
                    openPage(url, { forceOpenInNewWebview: true, keepQuery: false });
                }
            });
            return true;
        }

        // 组队面板
        if (buttonInfo.linkType === LinkTypeEnum.GO_TEAM) {
            getModelInstance(teamDialogModel)?.openTeamPanel();
            getModelInstance(TaskModel)?.tasksRefetch();
            close();
            return true;
        }

        // 拉起任务面板
        if (buttonInfo.linkType === LinkTypeEnum.PULL_TASK_LIST_PANEL) {
            close();
            if (getModelInstance(TaskModel)?.hasUnfinishedGetCountsTask.value) {
                getModelInstance(TaskModel)?.openSheet();
            } else {
                toast('没有步数可以向前冲啦，明天再来吧');
            }
            return true;
        }

        // 点击按钮刷新主接口并开启新轮次
        if (buttonInfo.linkType === LinkTypeEnum.NEW_ROUND_SIGN) {
            log('NEED_START_NEW_ROUND_MAIN_BUTTON', popup);
            getModelInstance(homeModel)
                ?.refreshHome('NEED_START_NEW_ROUND')
                .then(() => {
                    refreshCalendar();
                    getModelInstance(TaskModel)?.tasksRefetch();
                    close();
                });
            return true;
        }

        // 任务按钮
        if (buttonInfo.linkType === LinkTypeEnum.COMMON_TASK) {
            if ('llpeDetail' in popup) {
                const prizeDetailData = popup.llpeDetail?.[0];

                if (isNotNil(prizeDetailData) && 'taskExtra' in prizeDetailData) {
                    const task = prizeDetailData.taskExtra as unknown as CommonTaskDetail;
                    handleTaskPopupBtnClick(task, popup, close);
                }
            }
            return true;
        }

        // 提醒我打卡
        if (buttonInfo.linkType === LinkTypeEnum.RESERVATION) {
            checkAndAddCalendar({ needRefreshHome: false, checkIOSAuth: false }).then((res) => {
                close();
                return true;
            });
        }

        return false;
    };

    // 弹窗按钮点击
    const onPopupButtonClick = (
        popup: SummerPopup,
        params: Parameters<PopupBtnClick>[0],
        extraClickHandler?: PopupButtonClickHandler,
        // eslint-disable-next-line sonarjs/cognitive-complexity
    ) => {
        // 点击右上角关闭按钮
        if (params.position === 'close') {
            params.destroy();
            // 彻底断签弹窗关闭按钮，开启新一轮
            if (popup.popupType === PopupType.HUGE_SIGN_IN_CHALLENGE_FAILURE) {
                log('NEED_START_NEW_ROUND_CLOSE_BUTTON', popup);
                getModelInstance(homeModel)
                    ?.refreshHome('NEED_START_NEW_ROUND')
                    .then(() => {
                        refreshCalendar();
                        getModelInstance(TaskModel)?.tasksRefetch();
                    });
            }
            // 存量用户提示弹窗关闭时主动刷新主接口
            if (popup.popupType === PopupType.OLD_USER_TIP) {
                getModelInstance(homeModel)?.refreshHome('');
                console.log('refreshHome old user tip 右上角关闭按钮');
            }
            if (popup.popupType === PopupType.TEAM_EXIT) {
                // 队伍解散刷新任务接口更新组队任务
                getModelInstance(TaskModel)?.tasksRefetch();
            }
            return;
        }
        // 点击下标打开钱包页面
        if (params.position === 'bottomClick') {
            // 打开钱包页
            if (route.name === ROUTE_NAME.CITY) {
                openPage(getModelInstance(cityConfigModel)?.kconfUEConfig.value?.walletUrl ?? '', {
                    forceOpenInNewWebview: true,
                    keepQuery: false,
                });
            } else {
                openPage(getModelInstance(configModel)?.kconfUEConfig.value?.walletUrl ?? '', {
                    forceOpenInNewWebview: true,
                    keepQuery: false,
                });
            }
            params.destroy();
            return;
        }

        //  选品弹窗没有 mainButton 到此为止
        if (popup.popupType === PopupType.HUGE_SIGN_IN_PRODUCT) {
            return;
        }

        let button: SummerCommonButtonView | undefined | null = null;
        if (params.position === 'mainClick') {
            button = popup.mainButton;
        } else if (params.position === 'subClick' && 'subButton' in popup) {
            button = popup.subButton;
        }

        /**
         * 对于城市二级页新增的 GRID_COMMON_TASK_LLCN 挑战格子任务弹窗
         * 弹窗并未下发 mainButton，是前端在 gridCommonTaskTransform.ts 中通过任务详情渲染 mainButton
         * 由于次数 popup 为 transform 前的 popup 导致无法继续执行
         * 特殊处理，确保 button 有值，linkType 为 COMMON_TASK，确保继续向下执行任务处理逻辑
         */
        if (!isNotNil(button) && popup.popupType === PopupType.GRID_COMMON_TASK_LLCN) {
            button = {
                linkType: LinkTypeEnum.COMMON_TASK,
            };
        }

        if (isNotNil(button)) {
            if (!handleCommonPopupButtonClick(popup, button, params.destroy)) {
                extraClickHandler?.(popup, button, params.destroy, params.position);
            }
        }
    };

    const createSummerPopup = (
        popupData: SummerPopup,
        options: {
            taskType: TaskType;
            config?: PopupTaskConfig;
            extraClickHandler?: PopupButtonClickHandler;
            popupSource?: POPUP_SOURCE;
        },
        source?: TASK_SOURCE,
    ) =>
        createCnyPopup(
            {
                popupData,
                config: {
                    name: options?.taskType ?? TaskType.NONE,
                    queueTags: [QUEUE_TAGS_TYPE.POPUP],
                    priority: PASSIVE_POPUP_PRIORITY?.[popupData.popupType],
                    ...options?.config,
                },
                extra: {
                    source,
                    popup_source: options.popupSource,
                },
                sendClick,
                sendShow,
                onBtnClick(params) {
                    onPopupButtonClick(popupData, params, options.extraClickHandler);
                },
            },
            createPopupTask,
        );

    const openSummerPopup = (
        popup: SummerPopup,
        options: {
            taskType?: TaskType;
            config?: PopupTaskConfig;
            extraClickHandler?: PopupButtonClickHandler;
            popupSource?: POPUP_SOURCE;
            queueTags?: QUEUE_TAGS_TYPE[];
        },
        source?: TASK_SOURCE,
    ) => {
        const task = createSummerPopup(
            popup,
            {
                taskType: options.taskType ?? TaskType.PASSIVE_POPUP,
                config: {
                    queueTags: [QUEUE_TAGS_TYPE.POPUP, ...(options.queueTags ?? [])],
                    ...options.config,
                    name: options.taskType ?? TaskType.PASSIVE_POPUP,
                },
                popupSource: options.popupSource,
                extraClickHandler: options.extraClickHandler,
            },
            source,
        );

        if (!task) {
            return;
        }
        queue.addTask(task);
        return task;
    };

    // 当前阻塞性任务弹窗
    const currentBlockTasks = computed(() =>
        currentTasks.value.filter((task) => BLOCK_TASK_TYPES.includes(task.name as TaskType)),
    );

    // 是否有弹窗
    const hasPopup = computed(() => currentBlockTasks.value.length > 0);

    const currentIsTaskSheet = computed(
        () =>
            !!currentTasks.value.find(
                (i) => i.type === 'popup' && i.status === TaskStatusEnum.RUNNING && exemptPopup.includes(i.name),
            ),
    );

    const popupIsIdle = computed(
        () => currentTasks.value.filter((i) => i.type === 'popup' && i.status !== TaskStatusEnum.FINISH).length === 0,
    );

    /**
     * 存在盖在任务列表上的弹框
     */
    const existPopupOverTaskSheet = computed(
        () =>
            currentTasks.value.filter(
                (i) =>
                    i.type === 'popup' &&
                    i.queueTags.conflictTags.includes('task-sheet-dialog') &&
                    i.status === TaskStatusEnum.RUNNING,
            ).length > 0,
    );

    /**
     * 需要 block 任务和直播接口刷新的情况
     * - 除任务面板的其他弹框展示时
     */
    const blockTaskAndLiveRefresh = computed(
        () => (!popupIsIdle.value && !currentIsTaskSheet.value) || existPopupOverTaskSheet.value,
    );

    const checkPopupInQueue = (popupType: PopupType) => {
        return [...currentTasks.value, ...tasks.value].find((task) => task.name === popupType);
    };

    const isCurrentTasksIncludePopupOrSheet = computed(() => {
        return !!currentTasks.value.filter((item) =>
            [
                TaskType.ACTIVE_POPUP,
                TaskType.PASSIVE_POPUP,
                TaskType.SHEET,
                TaskType.SPECIAL_SHEET,
                TaskType.TASK_SHEET_UNIQUE_NAME,
                TaskType.HUGE_SIGN_IN_RESUME,
            ].includes(item.name as any),
        ).length;
    });

    const SIGNED_POPUP_TEAM_TASK = 'signedGoTeamUpPopup';

    /** 获取频控数据，只读不写 */
    const getFrequency = async () => {
        const date = new Date();
        const today = `${date.getFullYear()}-${date.getMonth() + 1}-${date.getDate()}`;
        // eslint-disable-next-line @typescript-eslint/prefer-nullish-coalescing
        const frequency = ((await summerLocalStore.get(SIGNED_POPUP_TEAM_TASK)) || { today, dayTime: 0 }) as {
            today: string; // 今天的日期
            dayTime: number; // 今天弹了几次
        };
        if (frequency.today !== today) {
            frequency.today = today;
            frequency.dayTime = 0;
        }
        log('LSQ> getFrequency: key=', SIGNED_POPUP_TEAM_TASK, frequency);
        return frequency;
    };

    /**
     * 已经打开后的点击按钮操作
     */
    const alreadySignedToOpenPopup = async () => {
        let SignedPopupTask;
        const frequency = await getFrequency();
        const isShowTeamUpPopup = getModelInstance(teamDataModel)?.canShowGoTeamPopup.value && frequency.dayTime < 1;
        const action = 'OP_ACTIVITY_CORE_POP';
        const popupTitle = isShowTeamUpPopup ? '今日已打卡 去组队吧' : '今日已打卡，去赚钱吧';
        const buttonName = isShowTeamUpPopup ? '去组队' : '去赚钱';
        const popupType = isShowTeamUpPopup ? 'SIGNED_GO_TEAM_POPUP' : 'SIGNED_GO_TASK_POPUP';
        if (isShowTeamUpPopup) {
            frequency.dayTime++;
            await summerLocalStore.set(SIGNED_POPUP_TEAM_TASK, frequency);
            SignedPopupTask = createPopupTask({
                component: () => import('@/components/team/dialog/TaskFreeCardModal.vue'),
                data: {
                    sponsorLogo: getModelInstance(homeModel)?.titleInfo?.value?.logo ?? '',
                    subTitle: popupTitle,
                    desc: '组队可得直通卡，一键直达当日站点',
                    icon: PassCardPng,
                    mainButton: buttonName,
                    taskFreeCardNum: 7,
                    type: 'dialog',
                    btnClick: (popup: { position: string; destroy: () => void }) => {
                        if (popup.position === 'mainClick') {
                            getModelInstance(teamDialogModel)?.openTeamPanel();
                            sendClick(action, {
                                popup_type: popupType,
                                title: popupTitle,
                                button_name: buttonName,
                            });
                        }
                        popup?.destroy();
                    },
                },
                options: {
                    name: TaskType.ACTIVE_POPUP,
                },
            });
        } else {
            // 兜底引导打开赚现金任务面板弹窗的弹窗
            SignedPopupTask = createPopupTask({
                component: () => import('@pet/25cny.packet/CommonClosePacket.vue'),
                data: {
                    sponsorLogo: getModelInstance(homeModel)?.titleInfo?.value?.logo ?? '',
                    title: popupTitle,
                    blessingTitle: ['邀好友必得现金', '做任务得金币'][Math.floor(Math.random() * 2)],
                    blessing: '可提现至微信零钱',
                    mainButton: {
                        linkText: buttonName,
                    },
                    btnClick: (popup: { position: string; destroy: () => void }) => {
                        if (popup.position === 'mainClick') {
                            getModelInstance(TaskModel)?.openLLCHTaskSheet();
                            sendClick(action, {
                                popup_type: popupType,
                                title: popupTitle,
                                button_name: buttonName,
                            });
                        }
                        popup?.destroy();
                    },
                },
                options: {
                    name: TaskType.ACTIVE_POPUP,
                },
            });
        }
        SignedPopupTask.start.then(() => {
            sendShow(action, {
                popup_type: popupType,
                title: popupTitle,
                button_name: buttonName,
            });
        });

        queue.addTask(SignedPopupTask);
    };

    const prizeShowOpenPopup = async (options: {
        sponsorLogo: string;
        title: string;
        icon: string;
        mainButton: string;
    }) => {
        const SignedPopupTask = createPopupTask({
            component: () => import('@/components/common-modals/CommonModal.vue'),
            data: {
                ...options,
                customStyle: {
                    '--adapt-layer-image-size': px2rem(266),
                    '--adapt-layer-main-button-width': px2rem(210),
                    '--adapt-layer-main-button-primary-background-image':
                        'linear-gradient(to right, #FF7001 0%, #FC2D39 49%, #F31906 100%)',
                },
                btnClick: (popup: { position: string; destroy: () => void }) => {
                    sendClick('GRID_GIFT_POP', {});
                    popup?.destroy();
                },
            },
            options: {
                name: TaskType.ACTIVE_POPUP,
            },
        });
        SignedPopupTask.start.then(() => {
            sendShow('GRID_GIFT_POP', {});
        });
        queue.addTask(SignedPopupTask);
    };

    /** 页面是否可见 */
    const pageVisible = injectVisibility()?.visible ?? ref(true);

    /** 有三个全周期展示一次的任务，但是快照过期弹窗还是会下发，但是这里不应该展示，不能让用户做，所以用前端缓存判断过滤掉 */
    const validShowTaskPopup = async (popupType: PopupType) => {
        if (
            [
                PopupType.LS_SUBSCRIBE_CALENDAR_TASK,
                PopupType.LS_KSWITCH_INPUSH_TASK,
                PopupType.LS_ADD_SHORTCUT_TASK,
            ].includes(popupType)
        ) {
            const typeToStoreKey = {
                [PopupType.LS_SUBSCRIBE_CALENDAR_TASK]: SUBSCRIBE_CALENDAR_TASK_COMPLETED,
                [PopupType.LS_ADD_SHORTCUT_TASK]: SHORT_CUT_TASK_COMPLETED,
                [PopupType.LS_KSWITCH_INPUSH_TASK]: SUBSCRIBE_INPUSH_TASK_COMPLETED,
            };
            // @ts-expect-error
            const storeKey = typeToStoreKey[popupType];
            if (storeKey) {
                const frequency = await getModelInstance(TaskModel)?.getTaskFrequency(storeKey);
                if (frequency?.allTime && frequency.allTime > 0) {
                    return false;
                }
            }
            return true;
        }
        return true;
    };

    /** 特殊逻辑，从后台切前台，部分面板需要刷新弹窗 */
    watch(
        () => pageVisible.value,
        (visible) => {
            if (visible) {
                if (currentTasks.value?.[0]?.name === TaskType.SPECIAL_SHEET) {
                    if (route.name === ROUTE_NAME.CITY) {
                        console.log('[GridTask] 前后台切换，forceRefreshHome');
                        getModelInstance(cityHomeModel)?.forceRefreshHome();
                    } else {
                        getModelInstance(homeModel)?.forceRefreshHome();
                    }
                }
            }
        },
    );

    return {
        queue,
        /** 所有队列任务 */
        tasks: queue.tasks,
        /** 当前阻塞性任务弹窗队列 */
        currentBlockTasks,
        /** 当前队列任务 */
        currentTasks,
        hasPopup,
        /** 统一 inpush */
        openInpush,
        /** 自定义弹窗传component */
        openPopup,
        /** 派发弹窗等统一逻辑 */
        openSummerPopup,
        createSummerPopup,
        /** 创建组任务 */
        createGroupTask,
        /** 添加组任务 */
        addGroupTask,
        /** 添加事件任务 */
        addEventTask,
        /** 创建弹窗任务 */
        createPopupTask,
        /** 判断是否存在 */
        checkPopupInQueue,
        addQueueInterceptor,
        /** 是否需要block任务列表的请求 */
        blockTaskAndLiveRefresh,
        /** 当前是否有弹层正在展示 */
        isCurrentTasksIncludePopupOrSheet,
        /** 已打开点击主按钮触发 */
        alreadySignedToOpenPopup,
        /** 重置队列 */
        resetQueue,
        /** 向前冲下发任务过滤条件 */
        validShowTaskPopup,
        /** 城市奖品预览 */
        prizeShowOpenPopup,
    };
});
