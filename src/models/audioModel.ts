import { createModel, useModel } from '@gundam/model';
import { radar } from '@gundam/weblogger';
import { KPlayer, PlayerType } from '@ks-kplayer/core';
import { useDowngradeLevel } from '@pet/25cny.downgrade-level';
import { popupMuted } from '@pet/25cny.packet/components/Video/muted';
import { isAndroid, isIOS } from '@pet/yau.core';
import useCaptureDebugLog from '@pet/yau.logger';
import { stopLive } from '@pet/yau.yoda/utils';
import { useKAudio } from '@pet/yoda.audio';
import { whenever } from '@vueuse/core';
import type { AudioItem, IAudioOptions } from '@yoda/audio';
import { invoke } from '@yoda/bridge';
import { computed, ref, watch } from 'vue';

import IpDown from '@/components/charge-forward/assets/music/down.m4a';
import Fly from '@/components/charge-forward/assets/music/fly.m4a';
import GridCoin from '@/components/charge-forward/assets/music/gridCoin.m4a';
import IpReady from '@/components/charge-forward/assets/music/ready.m4a';
import StationCoin from '@/components/charge-forward/assets/music/stationCoin.m4a';
import Cheer from '@/modules/home/<USER>/cheer.m4a';
import DialogOpenSrc from '@/modules/home/<USER>/dialog-open.m4a?url';
import FlyToProduct from '@/modules/home/<USER>/fly-product.m4a';
import BgmSrc from '@/modules/home/<USER>/newBgm.m4a?url';
import ProductArrived from '@/modules/home/<USER>/product_arrived.m4a';
import Signed from '@/modules/home/<USER>/signed.m4a';
import { ROUTE_NAME } from '@/router/name';

import '@ks-kplayer/core/dist/css/style.css';
import { isInLiveHalf } from '@/utils/live';

// 主动打开：open 方法
// 私信半屏关闭事件：chatVCDealloc

const defaultOptions = {
    autoPlay: false,
    muteControl: true,
    fadeIn: 0,
    useGlobalSwitch: true,
};

enum AudioDownLevel {
    // 不降级
    NONE = 0,
    // 只针对背景音乐 同层渲染降级到 KAudio
    LOW = 1,
    // 降级，静音全部音频
    ALL = 2,
}

export enum SoundType {
    BGM = 'DEFAULT',
    DIALOG_OPEN = 'DIALOG_OPEN',
    IP_READY = 'IP_READY',
    IP_DOWN = 'IP_DOWN',
    GRID_COIN = 'GRID_COIN',
    STATION_COIN = 'STATION_COIN',
    FLY = 'FLY',
    SIGNED = 'SIGNED',
    CHEER = 'CHEER',
    PRODUCT_ARRIVED = 'PRODUCT_ARRIVED',
    FLY_TO_PRODUCT = 'FLY_TO_PRODUCT',
}

export const soundMap = {
    [SoundType.BGM]: BgmSrc,
    [SoundType.DIALOG_OPEN]: DialogOpenSrc,
    [SoundType.IP_READY]: IpReady,
    [SoundType.IP_DOWN]: IpDown,
    [SoundType.STATION_COIN]: StationCoin,
    [SoundType.GRID_COIN]: GridCoin,
    [SoundType.FLY]: Fly,
    [SoundType.SIGNED]: Signed,
    [SoundType.CHEER]: Cheer,
    [SoundType.PRODUCT_ARRIVED]: ProductArrived,
    [SoundType.FLY_TO_PRODUCT]: FlyToProduct,
};

enum BGMPlayerType {
    PEER_RENDER = 'PEER_RENDER',
    K_AUDIO = 'K_AUDIO',
}

export const AudioModel = createModel(() => {
    const { isAudioDowngrade } = useDowngradeLevel();
    const route = useRoute();
    const { log } = useCaptureDebugLog('audioModel');
    const radarConfig = computed(() => {
        if (radar) {
            log('雷达实例已初始化');
            return {
                radar,
                reportQos: true,
            };
        }

        log('雷达实例未初始化', radar);
        return undefined;
    });

    const { beep, globalSwitch, setSwitch, clear, preload, sdkReady, visible } = useKAudio({
        radar,
        container: 'Yoda',
        bizSource: '25summer',
        followNativeMute: true,
    });

    const canPlayAudio = computed(() => {
        return popupMuted.value && isAudioDowngrade.value !== AudioDownLevel.ALL;
    });

    // 整个页面的背景音效实例
    const pageBgmAudio = ref<any>();
    // 背景音乐的播放器类型
    const playerType = ref<BGMPlayerType>();

    let tempSwitch = false;
    const audioItemMap = new Map<SoundType, AudioItem>();

    preload(BgmSrc);

    /** 播放会场的bgm */
    const playPageBgmAudio = () => {
        if (route.name === ROUTE_NAME.ERROR) {
            return;
        }
        /**
         *  同层渲染 4tab 下在加载页面中切换 tab 会漏音
         * 这里借用 KAudio 的可见性变化来躲避背景音乐播放
         * 后续通过watch visible 恢复
         */
        if (playerType.value === BGMPlayerType.PEER_RENDER && !visible.value) {
            return;
        }
        pageBgmAudio.value?.play();
        invoke('tool.sendWebLog', {
            data: [
                {
                    key: 'web_log',
                    value: {},
                    dimension: {
                        detail: `播放会场背景音乐，type: ${playerType.value ?? ''}，url: ${BgmSrc}，switch: ${globalSwitch.value}，KAudio visible: ${visible.value}`,
                    },
                    event_client_timestamp: Date.now(),
                },
            ],
        });
    };

    /** 停止会场的bgm */
    const stopPageBgmAudio = () => {
        pageBgmAudio.value?.pause();
    };

    const initBgmByKAudio = async () => {
        pageBgmAudio.value = await beep(BgmSrc, true, defaultOptions);
        playerType.value = BGMPlayerType.K_AUDIO;
    };

    const isCompleteUrl = (urlString: string): boolean => {
        try {
            const url = new URL(urlString);
            // 判断是否有 hostname（如 example.com）
            return !!url.hostname;
        } catch (error) {
            // URL 构造失败，说明格式不正确或不完整
            return false;
        }
    };

    // 创建同层渲染播放器
    const createPeerRenderPlayer = async (
        bgmSrc: string,
        errorHandler: (e: Event) => void,
        initHandler: (status: any) => void,
    ) => {
        const src = isCompleteUrl(bgmSrc)
            ? bgmSrc
            : `http://${window.location.hostname}${window.location.port !== '' ? ':' + window.location.port : ''}${bgmSrc}`;
        const container = document.createElement('div');
        container.id = `peer_render_bgm_player`;
        container.setAttribute('style', 'height: 50px; width: 50px; position: absolute; left: -200%; top: -200%');
        document.body.appendChild(container);

        const player = await KPlayer.createPlayer({
            container: document.querySelector('#peer_render_bgm_player') as HTMLDivElement,
            type: PlayerType.VIDEO,
            playerConfig: {
                src,
                autoplay: false,
                loop: true,
                activityTag: '25summerWishTravel',
            },
            radarConfig: radarConfig.value,
        });
        player.on('error', (e: any) => {
            player.destroy();
            errorHandler(e);
        });
        player.on('statuschange', initHandler);

        return player;
    };
    // eslint-disable-next-line sonarjs/cognitive-complexity
    const initBgm = async () => {
        if (isInLiveHalf(route)) {
            log('在直播间半屏内，不播放音效');
            return;
        }
        if (isAndroid()) {
            stopLive();
        }
        if (isAudioDowngrade.value === AudioDownLevel.ALL) {
            return;
        }
        if (isIOS() && isAudioDowngrade.value === AudioDownLevel.NONE) {
            const initHandler = async (status: any) => {
                if (status.data.status === 'init') {
                    await sdkReady;
                    playerType.value = BGMPlayerType.PEER_RENDER;
                    globalSwitch.value ? stopPageBgmAudio() : playPageBgmAudio();
                }
            };
            const errorHandler = async (e: any) => {
                radar?.event?.({
                    name: 'Peer_Render_Bgm_Error',
                    message: '同层渲染播放失败，切换KAudio播放背景音乐',
                    category: 'KAudio',
                    extra_info: {
                        radarParams: {},
                        pageId: '25summerWishTravel',
                        front_timestamp: Date.now(),
                        biz: '25summerWishTravel',
                        err: e,
                    },
                });
                log('同层播放失败，切换KAudio播放背景音乐');
                // 同层渲染报错时，KAudio 兜底播放背景音乐
                await initBgmByKAudio();
                playPageBgmAudio();
            };

            pageBgmAudio.value = await createPeerRenderPlayer(BgmSrc, errorHandler, initHandler);
        } else {
            if (isIOS() && isAudioDowngrade.value === AudioDownLevel.LOW) {
                log('【音频降级:1】仅 IOS 生效，切换KAudio播放背景音乐');
            }
            await initBgmByKAudio();
            playPageBgmAudio();
        }
    };

    /** 播放音效，默认自动播放 */
    const playSound = async (type: SoundType, loop = false, option?: IAudioOptions) => {
        if (isInLiveHalf(route)) {
            log('在直播间半屏内，不播放音效');
            return;
        }
        if (isAudioDowngrade.value === AudioDownLevel.ALL) {
            return;
        }

        const beepOption = option ?? { ...defaultOptions, autoPlay: true };

        if (audioItemMap.size === 0 || !audioItemMap.has(type)) {
            const audioItem = await beep(soundMap[type], loop, beepOption);
            audioItemMap.set(type, audioItem);
            return audioItem;
        }
        const audioItem = audioItemMap.get(type);
        if (beepOption.autoPlay) {
            audioItem?.play();
        }
        return audioItem;
    };

    // 卸载声音
    const clearAllMusic = () => {
        if (playerType.value === BGMPlayerType.PEER_RENDER) {
            pageBgmAudio.value?.destroy();
        }
        clear();
    };

    // 声音开关带本地存储
    const toggleAudioMute = (val: boolean) => {
        if (playerType.value === BGMPlayerType.PEER_RENDER) {
            val ? playPageBgmAudio() : stopPageBgmAudio();
        }
        setSwitch(!val);
    };

    // 临时静音
    const setGlobalSoundMuteOn = () => {
        tempSwitch = globalSwitch.value;
        // async false 不记录当前声音状态到local storage
        setSwitch(true, false);
    };

    // 根据状态判断恢复所有声音
    const setGlobalSoundMuteOffByGlobalSwitch = () => {
        setSwitch(tempSwitch, false);
    };

    /**
     * 同层渲染 4tab 下在加载页面中切换 tab 会漏音
     * 这里借用 KAudio 的可见性变化来恢复背景音乐
     */
    watch(
        () => visible.value,
        (value) => {
            if (playerType.value === BGMPlayerType.PEER_RENDER) {
                if (value && !globalSwitch.value) {
                    playPageBgmAudio();
                }
            }
        },
    );

    watch(
        () => globalSwitch.value,
        () => {
            /**
             * 同层渲染没有和 KAudio 响应式变量耦合
             * 所以需要单独监听 globalSwitch.value 的变化
             */
            if (playerType.value === BGMPlayerType.PEER_RENDER) {
                !globalSwitch.value ? playPageBgmAudio() : stopPageBgmAudio();
            }
        },
    );

    watch(
        () => isAudioDowngrade.value,
        (value) => {
            /**
             * 不支持 ios 背景音乐在运行时从 同层渲染降级为 KAudio
             */
            if (value === AudioDownLevel.ALL) {
                stopPageBgmAudio();
                setGlobalSoundMuteOn();
            } else if (value === AudioDownLevel.NONE) {
                if (playerType.value === BGMPlayerType.PEER_RENDER) {
                    playPageBgmAudio();
                } else {
                    pageBgmAudio.value?.resume();
                }
            }
        },
    );

    whenever(
        () => !canPlayAudio.value,
        () => {
            setGlobalSoundMuteOn();
        },
        {
            immediate: true,
        },
    );

    whenever(canPlayAudio, () => {
        setGlobalSoundMuteOffByGlobalSwitch();
    });

    return {
        /** true为静音 */
        globalSwitch,
        pageBgmAudio,
        playSound,
        setSwitch,
        toggleAudioMute,
        playPageBgmAudio,
        stopPageBgmAudio,
        clearAllMusic,
        preload,
        initBgm,
    };
});

export const useAudioModel = () => useModel(AudioModel);
