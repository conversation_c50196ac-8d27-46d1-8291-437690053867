import { createUseModel } from '@gundam/model';
import { nativeCloud } from '@pet/yau.cloud-native';
import useCaptureDebugLog from '@pet/yau.logger';
import { watchDebounced } from '@vueuse/core';

import { MainBuildingConfig } from '@/components/charge-forward/manager/buildingConfig';
import {
    GridllwardType,
    type BuildingConfig,
    type GridllwardConfig,
    type MapConfig,
    type RenderMapType,
    type StationConfig,
} from '@/components/charge-forward/manager/config';
import { MapConfigData, MapKeyType } from '@/components/charge-forward/manager/constant';
import type { ChargeForwardManager } from '@/components/charge-forward/manager/manager';
import type { StationTagView } from '@/components/charge-forward/types';
import type { ChessProgressView, UserBasicView } from '@/services/open-api-docs/home/<USER>/schemas';
import { GridTaskStatus } from '@/services/open-api-docs/home/<USER>/schemas';

import { useForwardStationModel } from './forwardStationModel';
import { useHomeModel } from './homeModel';
import { useRefreshModel } from './refreshModel';
import { useSnapShotModel } from './snapShotModel';

export const useForwardRushGridModel = createUseModel(() => {
    const { log } = useCaptureDebugLog();
    const { homeData } = useHomeModel();
    const { currentStep, isArrived } = useSnapShotModel();
    const { gPreDoubleStation, gPreStation, gCurrentStation, gNextStation, gNextDoubleStation } =
        useForwardStationModel();
    const { isShowCloud } = useRefreshModel();
    // 是否点击过向前冲按钮
    const showCloud = ref(false);
    const afterSelect = ref(false);
    const firstSigned = ref(false);
    const showBubbleAndAniAfterSelect = computed(() => !firstSigned.value && afterSelect.value);

    const manager: Ref<ChargeForwardManager | null> = ref(null);

    const container = shallowRef<HTMLDivElement>();

    const renderMap: ComputedRef<RenderMapType | null | undefined> = computed(() => manager.value?.renderMap);

    // 当前进度
    const gProgress = computed<ChessProgressView | undefined>(() => homeData.value?.chessboard?.progress);
    // 地图背景信息
    const mapKey = computed(() =>
        (gCurrentStation.value?.stationInfo.stationThemeKey ?? '') in MapConfigData
            ? gCurrentStation.value?.stationInfo.stationThemeKey
            : MapKeyType.Default,
    );
    /** 是否是剧本日 */
    const isFakeDay = computed(
        () =>
            !gCurrentStation.value?.stationInfo.stationDayIndex ||
            gCurrentStation.value?.stationInfo.stationDayIndex <= (homeData.value?.chessboard?.initStationCount ?? 5),
    );
    // 今天
    const todayIndex = computed(() => gCurrentStation.value?.stationInfo.stationDayIndex);
    const isLastStation = computed<boolean>(() => homeData.value?.chessboard?.progress?.lastStation ?? false);
    // 站点标签文案
    const stationTagView = computed<StationTagView | undefined>(
        () => homeData.value?.homeFEConstantsConfig?.stationTagView,
    );
    // 玩家信息
    const gUserInfo = computed<UserBasicView | undefined>(() => homeData.value?.chessboard?.userInfo!);

    // 建筑信息
    const buildingConfig = computed<BuildingConfig>(() => {
        const mainBuilding = MainBuildingConfig[mapKey.value as keyof typeof MainBuildingConfig];
        return {
            data: [...(homeData.value?.chessboard?.buildingInfos ?? []), mainBuilding ?? {}],
            interval: homeData.value?.homeFEConstantsConfig?.buildingInterval ?? 24,
        };
    });
    // 是否正在移动
    const moving = computed(() => manager.value?.isMoving.value ?? false);
    // 地图配置选择
    const mapConfig = computed<MapConfig>(() => MapConfigData[mapKey.value as keyof typeof MapConfigData]);
    // 格子贴皮 for 商业化
    const gridSkinConfig = computed(() => ({
        gridSkinUrl: gCurrentStation.value?.stationInfo?.gridSkinUrl ?? '',
        gridSkinSponsor: gCurrentStation.value?.stationInfo?.gridSkinSponsor ?? '',
        gridSkinLocation: gCurrentStation.value?.stationInfo?.gridSkinLocation ?? [],
    }));
    // 背景style
    // [废弃] 为了使用高级格式图片，不采用这种方式渲染背景图片了
    // const backgroundImage = computed(() => {
    //     return mapConfig.value?.imgs.url!;
    // });
    const backgroundStyle = computed(() => {
        return {
            backgroundPosition: `right 0 top ${(renderMap.value?.background.offset ?? 0) / 100}rem`,
        };
    });
    const popoverContainerStyle = computed(() => {
        return {
            transform: `translateY(${(renderMap.value?.popover.offset ?? 0) / 100}rem)`,
        };
    });
    // ip样式
    const ipStyle = computed(() => {
        return {
            transform: `translate(${(renderMap.value?.ip.position.x ?? 0) / 100}rem, ${(renderMap.value?.ip.position.y ?? 0) / 100}rem)`,
        };
    });
    const ipOnStation = computed(() => renderMap.value?.ip.onStation ?? false);
    const ipOnBirthStation = computed(() => {
        // 如果是第一天，出生地永远不会是站点
        if (todayIndex.value === 1) {
            return false;
        }
        return renderMap.value?.ip.onBirth ?? false;
    });
    /**
     * 剧本日：最多最多展示明后天的站点
     * 非剧本日：最多展示当天的站点
     */
    const todayShowStations = computed(() => {
        log('FakeDay', isFakeDay.value, 'stations:', [
            gPreDoubleStation.value,
            gPreStation.value,
            gCurrentStation.value,
            gNextStation.value,
            gNextDoubleStation.value,
        ]);
        if (isFakeDay.value) {
            return [
                gPreDoubleStation.value,
                gPreStation.value,
                gCurrentStation.value,
                gNextStation.value,
                gNextDoubleStation.value,
            ].filter(
                (item) =>
                    !!item &&
                    item.stationInfo.stationDayIndex &&
                    item.stationInfo.stationDayIndex <= (homeData.value?.chessboard?.initStationCount ?? 5),
            );
        }
        return [gPreStation.value, gCurrentStation.value].filter((item) => !!item);
    });

    const showStationBubble = ref(false);
    // 奖励格子信息
    const gllwardGridLayout = ref<GridllwardConfig>({});
    // 站点配置
    const stationConfig = ref<StationConfig>([]);

    // 获取当前挑战格子的状态
    const getChallengeGridStatus = (location: number): GridTaskStatus => {
        const taskGridInfo = (homeData.value?.chessboard?.stationList ?? []).find((s) => {
            return (s?.stationInfo?.stationDayIndex ?? 0) === todayIndex.value;
        })?.llrewdGridLayout['TASK_GRID'];
        return (
            taskGridInfo?.gridTaskStatus?.find((_, index) => {
                return taskGridInfo?.gridLocation?.[index] === location;
            }) ?? GridTaskStatus.COMPLETED
        );
    };

    /**
     * 站点
     * 1. 如果是昨天站点，step = 0作为出发点
     * 2. 如果是明天之后的站点，
     *   (1) 如果是剧本日，step += 今天需要走的步数
     *   (2) 判断该站点距离当天的站点的step是否小于等于10个，如满足，则展示
     *  【废弃】(2) 如果不是剧本日 step = 今天需要走的步数 + 2
     *
     * 奖励
     * 1. 昨天的奖励不展示
     * 2. 今天的奖励展示，不受剧本影响
     * 3. 明天的奖励
     *  (1) 如果是剧本日，展示，step += 前一个站点的step
     *  (2) 如果不是剧本日，不展示
     */
    watch(
        [todayIndex, todayShowStations, isFakeDay],
        // eslint-disable-next-line sonarjs/cognitive-complexity
        ([todayIndex]) => {
            if (!todayIndex) {
                return;
            }
            gllwardGridLayout.value = {};
            stationConfig.value = [];
            const totalStations: StationConfig = [];
            const firstStep = -(gPreStation.value?.stationInfo.stationTotalStep ?? 0);
            todayShowStations.value.reduce(
                (pre, cur) => {
                    if (!cur) {
                        return pre;
                    }
                    const station = cur.stationInfo;
                    const info = {
                        ...station,
                        step: station.stationTotalStep!,
                    };
                    if (todayIndex > station.stationDayIndex!) {
                        if (todayIndex === station.stationDayIndex! + 1) {
                            // 昨天的站点放在起点, 不展示奖励
                            info.step = 0;
                        } else if (todayIndex === station.stationDayIndex! + 2) {
                            // 前天的站点从起点往前放
                            info.step = pre.step;
                        }
                    } else if (todayIndex <= station.stationDayIndex!) {
                        // 如果是今天或者剧本的明后天，展示奖励
                        if (
                            todayIndex === station.stationDayIndex! ||
                            (todayIndex < station.stationDayIndex! && isFakeDay.value)
                        ) {
                            // 明后的奖励
                            const grid = cur.llrewdGridLayout ?? {};
                            const gridInfo: GridllwardConfig = {};

                            Object.entries(grid).forEach(([key, value]) => {
                                const locals = (
                                    gllwardGridLayout.value?.[key as GridllwardType]?.gridLocation ?? []
                                ).concat((value.gridLocation ?? []).map((item) => item + (pre.step ?? 0)));
                                gridInfo[key as GridllwardType] = {
                                    ...value,
                                    gridLocation: Array.from(new Set(locals)),
                                };
                            });
                            gllwardGridLayout.value = {
                                ...gllwardGridLayout.value,
                                ...gridInfo,
                            };
                        }
                        // 明后的站点step累加
                        info.step = pre.step + info.step;
                    }
                    totalStations.push(info);
                    return info;
                },
                {
                    step: firstStep,
                },
            );
            stationConfig.value = totalStations;
        },
        {
            immediate: true,
        },
    );
    // 奖励信息
    const llwardGrids = computed(() => {
        return renderMap.value?.grids;
    });
    // 两侧建筑信息
    const buildings = computed(() => {
        return renderMap.value?.buildings;
    });
    // 站点信息，包含站点渲染位置
    const stations = computed(() => {
        return renderMap.value?.stations;
    });
    // 商业化格子贴皮
    const gridSkins = computed(() => {
        return renderMap.value?.skins;
    });

    // 当前格子信息
    const currentGrid = computed(() => {
        return renderMap.value?.currentGrid;
    });
    // 背景图偏移
    const offset = computed(() => renderMap.value?.background.offset ?? 0);
    // 新鲜事标题
    const stationDesc = computed(() => homeData.value?.chessboard?.stationHotInfoViews?.stationDesc ?? '');
    // 新鲜事内容
    const todayNews = computed(() => homeData.value?.chessboard?.stationHotInfoViews?.hotInfos ?? []);
    // 新鲜事频空
    const todayNewsInterval = computed(() => homeData.value?.chessboard?.stationHotInfoViews?.liveTime ?? 10000);

    watch([offset, mapKey], ([val, val2]) => {
        // 没有点击过向前冲按钮
        if (!showCloud.value && val) {
            const windowOffset = (window as any).offset;
            const windowMapKey = (window as any).mapKey;
            const isFirstTimeLoaded = (window as any).isFirstTimeLoaded;
            setTimeout(() => {
                console.log('出转场==', val, Math.floor(val), windowOffset, val2, windowMapKey, isFirstTimeLoaded);
            }, 1500);
            // ssg和csr的地图偏移量 diff 绝对值
            const absDiff = Math.abs(Math.floor(val) - parseInt(windowOffset ?? '0', 10));
            // absDiff>5 或 地图key变了 出卷轴
            if ((absDiff > 5 || val2 !== windowMapKey) && !isFirstTimeLoaded) {
                isShowCloud.value = true;
                nativeCloud.show();
                setTimeout(() => {
                    nativeCloud.hide();
                    isShowCloud.value = false;
                }, 2000);
            }
            showCloud.value = true;
        }
    });

    const dayIsToday = (dayIndex: number | null | undefined) => {
        return !!dayIndex && !!todayIndex.value && dayIndex === todayIndex.value;
    };

    const dayIsTomorrow = (dayIndex: number | null | undefined) => {
        return !!dayIndex && !!todayIndex.value && dayIndex === todayIndex.value + 1;
    };

    const dayIsMoreThanToday = (dayIndex: number | null | undefined) => {
        return !!dayIndex && !!todayIndex.value && dayIndex >= todayIndex.value + 1;
    };

    watchDebounced(
        renderMap,
        () => {
            log('render map info', renderMap.value);
        },
        {
            immediate: true,
            deep: true,
            debounce: 3000,
        },
    );
    return {
        moving,
        renderMap,
        manager,
        ipStyle,
        ipOnStation,
        isArrived, // 判断到站弹窗是否已经展示, 用于判断站牌&ip位置
        llwardGrids,
        buildings,
        stations,
        gridSkinConfig,
        gridSkins,
        currentGrid,
        mapConfig,
        gllwardGridLayout,
        isLastStation,
        gProgress,
        gCurrentStation,
        gNextStation,
        gUserInfo,
        buildingConfig,
        container,
        currentStep,
        stationConfig,
        backgroundStyle,
        afterSelect,
        firstSigned,
        showBubbleAndAniAfterSelect,
        // backgroundImage,
        stationTagView,
        offset,
        stationDesc,
        todayNews,
        todayNewsInterval,
        mapKey,
        showStationBubble,
        popoverContainerStyle,
        ipOnBirthStation,
        dayIsToday,
        dayIsTomorrow,
        dayIsMoreThanToday,
        getChallengeGridStatus,
    };
});
