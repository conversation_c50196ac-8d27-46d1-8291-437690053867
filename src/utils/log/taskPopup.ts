import { isNotNil } from '@pet/yau.core';

import { PopupType } from '@/services/open-api-docs/home/<USER>/schemas';

import { POPUP_ACTION, type BasePopupLog, type PopupTransformValue, TASK_SOURCE, type LogTransformFn } from './type';
import { isPopup, type GetArrayItemType, type SummerPopup } from '../popupTransform/types';

const marketPopups = [
    PopupType.LINK_MARKETING_MATCH,
    // PopupType.PK_MARKETING_MATCH,
    PopupType.TASK_MARKETING_MATCH,
    PopupType.SIGN_MARKETING_MATCH,
];
const taskPopupType = [
    // 翻倍任务
    PopupType.LS_TIME_LIMITED_ASSIST_DOUBLE_LLCH,
    PopupType.LS_TIME_LIMITED_ASSIST_DOUBLE_LLCN,
    // 限时完成几个任务
    PopupType.LS_TIME_LIMITED_COUNT_TASK_LLCH,
    PopupType.LS_TIME_LIMITED_COUNT_TASK_LLCN,
    // 预约红包雨任务
    // PopupType.LS_RESERVE_LEEE_RAIN_LLCH,
    // PopupType.LS_RESERVE_LEEE_RAIN_LLCN,
    // PopupType.EVE_LS_RESERVE_LEEE_RAIN_SHAKE,
    // 关注涨粉任务
    PopupType.LS_FOLLOW_TASK_BLESS,
    PopupType.LS_FOLLOW_TASK_LLCH,
    PopupType.LS_FOLLOW_TASK_LLCN,
    PopupType.EVE_LS_FOLLOW_TASK_SHAKE,
    // 观看直播任务
    PopupType.LS_WATCH_LIVE_TASK_LLCH,
    PopupType.LS_WATCH_LIVE_TASK_LLCN,
    PopupType.LS_WATCH_VIDEO_LLCN_TASK,
    PopupType.EVE_LS_WATCH_LIVE_TASK_SHAKE,
    // 限时邀人拉回任务
    PopupType.LS_TIME_LIMITED_REFLUX_ASSIST_TASK_LLCH,
    PopupType.LS_TIME_LIMITED_REFLUX_ASSIST_TASK_LLCN,
    // 限时邀人任务，不限制人群
    PopupType.LS_TIME_LIMITED_ASSIST_TASK_LLCH,
    PopupType.LS_TIME_LIMITED_ASSIST_TASK_LLCN,
    // 双端互拉任务
    PopupType.LS_INVOKE_APP_LLCH,
    PopupType.LS_INVOKE_APP_LLCN,
    PopupType.EVE_LS_INVOKE_APP_SHAKE,
    // 常驻拉新任务
    PopupType.TL_ALWAYS_PULL_TASK,
    // 魔表任务
    PopupType.SHARE_VIDEO_TASK,
    // 新增导流营销弹窗
    ...marketPopups,
    // 大额金币任务
    PopupType.LS_COMMON_ZT_TASK_LLCN,
    PopupType.LS_COMMON_ZT_TASK_LLCH,
    PopupType.LS_SHARE_LLCN_TASK,
    PopupType.HUGE_SIGN_IN_STATION_POPUP,
    PopupType.HUGE_SIGN_IN_STATION_NEW_POPUP,
    // Push权限通知弹窗
    PopupType.LS_PUSH_SWITCH_LLCN_TASK,
    // 看视频得金币任务弹窗
    PopupType.LS_WATCH_VIDEO_LLCN_TASK,
    // 首日打卡 inpush 提醒任务弹窗
    PopupType.HUGE_SIGN_IN_SUBSCRIBE_POPUP,
    // 挑战格子
    PopupType.GRID_COMMON_TASK_LLCN,
    PopupType.LS_KSWITCH_INPUSH_TASK,
    PopupType.LS_ADD_SHORTCUT_TASK,
    PopupType.LS_VISIT_SHARE_VIDEO_VENUE_TASK,
    PopupType.LS_VISIT_SHARE_OPEN_RED_PACKET_TASK,
    PopupType.LS_SEARCH_KEY_WORD_TASK,
    PopupType.LS_FOLLOW_RECO_AUTHOR_TASK,
    PopupType.LS_SUBSCRIBE_CALENDAR_TASK,
] satisfies PopupType[];

export interface TaskPopupLog extends BasePopupLog {
    task_source: TASK_SOURCE;
    task_id: string;
    button_name: string;
    popup_type: TaskPopupType;
}

export type TaskPopupType = GetArrayItemType<typeof taskPopupType>;

// eslint-disable-next-line sonarjs/cognitive-complexity
export const taskPopupTransform: LogTransformFn<TaskPopupLog> = (popupData: SummerPopup, source?: TASK_SOURCE) => {
    if (isPopup(popupData, taskPopupType)) {
        // @ts-expect-error
        const mainButton = popupData?.mainButton;
        let taskId;
        let task_name;
        let task_type;

        // @ts-expect-error
        let title = popupData.title ?? '';
        let button_name = mainButton?.linkText ?? '';

        if ('llpeDetail' in popupData) {
            const detailData = popupData?.llpeDetail?.[0];
            if (isNotNil(detailData) && 'taskExtra' in detailData) {
                taskId = detailData.taskExtra?.taskId ?? 0;
                task_name = detailData.taskExtra?.title ?? '';
                task_type = detailData.taskExtra?.extParams?.groupName ?? '';

                if (popupData.popupType === PopupType.GRID_COMMON_TASK_LLCN) {
                    title = detailData.taskExtra?.title ?? '';
                    button_name = detailData.taskExtra?.displayText || '去完成';
                }
            }
        } else if ('taskId' in popupData) {
            taskId = popupData.taskId;
        }

        let taskSource;
        if (Boolean(source)) {
            taskSource = source;
        } else if (popupData.popupType === PopupType.SHARE_VIDEO_TASK) {
            taskSource = TASK_SOURCE.OTHER;
        } else if (marketPopups.includes(popupData.popupType)) {
            taskSource = TASK_SOURCE.PUSH_TASK;
        } else {
            taskSource = TASK_SOURCE.SHAKE;
        }

        return {
            show: POPUP_ACTION.TASK_SHOW,
            click: POPUP_ACTION.TASK_SHOW,
            close: POPUP_ACTION.TASK_SHOW,
            logValue: {
                task_source: taskSource as TASK_SOURCE,
                task_id: taskId,
                task_type,
                task_name,
                button_name,
                // @ts-expect-error
                brand_name: popupData.sponsorText ?? '',
                title,
                popup_type: popupData.popupType,
            },
        };
    }
};
