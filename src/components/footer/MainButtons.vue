<!-- 页面底部组件，包含向前冲按钮、邀组队、赚步数按钮 -->
<script lang="ts" setup>
import { vGuide } from '@pet/25cny.guide-directive/index';
import GuideHand from '@pet/adapt.guide/index.vue';
import { sleep } from '@pet/yau.core';

import { ForwardRushBtnStatus, useConfigModel } from '@/models/configModel';
import { useForwardRushBtnModel } from '@/models/forwardRushBtnModel';
import { useForwardRushGridModel } from '@/models/forwardRushGridModel';
import { useSideBtnModel } from '@/models/sideBtnModel';

import SideButton from './SideButton.vue';
import ForwardRushBubble from '../forward-rush/ForwardRushBubble.vue';
import ForwardRushButton from '../forward-rush/ForwardRushButton.vue';
import ForwardRushTip from '../forward-rush/ForwardRushTip.vue';

const { kconfConfig, weakGuideConfig } = useConfigModel();
const leftText = computed(() => kconfConfig.value?.mainButtonConfig?.leftSideButtonText ?? '');
const rightText = computed(() => kconfConfig.value?.mainButtonConfig?.rightSideButtonText ?? '');

const {
    isSecondDay,
    showSecondDayTipFinished,
    progress,
    stepCount,
    showForwardRushBubble,
    showForwardRushTip,
    forwardRushTip,
    mainButtonStatus,
    buttonAndBubbleInfo,
} = useForwardRushBtnModel();
const { handleClickLeftSideBtn, handleClickRightSideBtn } = useSideBtnModel();
const { isLastStation } = useForwardRushGridModel();

const clearForwardRushTip = async () => {
    forwardRushTip.value = '';
    showForwardRushTip.value = false;
    await sleep(500);
    if (isSecondDay.value || isLastStation.value) {
        showSecondDayTipFinished.value = true;
    }
};
</script>

<template>
    <div class="main-buttons-area">
        <div class="main-bubbles">
            <!-- 花字提示 -->
            <ForwardRushTip :show="showForwardRushTip" :text="forwardRushTip" @onTimesUp="clearForwardRushTip" />
            <!-- 主按钮气泡 -->
            <ForwardRushBubble
                class="forward-bubble-wrap"
                :show="showForwardRushBubble"
                :show-progress-bar="mainButtonStatus === ForwardRushBtnStatus.UN_SIGN"
                :main-text="buttonAndBubbleInfo.bubbleText"
                :sub-text="buttonAndBubbleInfo.bubbleSubText"
                :btn-status="mainButtonStatus"
                :step-count="stepCount"
                :progress="progress"
                :main-button-name="buttonAndBubbleInfo.buttonText"
            />
        </div>
        <div class="main-buttons">
            <SideButton
                v-click-log="{
                    action: 'OP_ACTIVITY_TASK_BUTTON',
                    params: {
                        title: leftText,
                    },
                }"
                direction="left"
                :text="leftText"
                @click="handleClickLeftSideBtn"
            />
            <ForwardRushButton />
            <SideButton
                v-click-log="{
                    action: 'OP_ACTIVITY_TASK_BUTTON',
                    params: {
                        title: rightText,
                    },
                }"
                v-guide.target="{
                    id: `GUIDE_RIGHT_BUTTON`,
                    priority: 10,
                    enabled: false,
                    component: GuideHand,
                    offset: {
                        top: 40,
                        left: 40,
                    },
                    // 消失时间
                    duration: 10000,
                    // 频控
                    totalLimitCount: 3,
                    nonInterval: 3000,
                    onTargetClick: () => {
                        handleClickRightSideBtn();
                        return true;
                    },
                    ...weakGuideConfig?.GUIDE_RIGHT_BUTTON,
                }"
                class="main-buttons__right"
                direction="right"
                :text="rightText"
                @click="handleClickRightSideBtn"
            />
        </div>
    </div>
</template>

<style lang="scss" scoped>
.main-buttons-area {
    .main-bubbles {
        display: flex;
        justify-content: center;
        width: 100%;
        position: absolute;
        bottom: 130px;
        pointer-events: none;
    }
    .main-buttons {
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        width: 100%;
        height: 128px;
    }
    .main-buttons__right {
        --adapt-guide-hand-box-width: 130px;
        --adapt-guide-hand-margin: -65px;
    }
}
</style>
