<!-- 页面底部挂件组件，包含我的钱包 去偷金币两个按钮 -->
<script setup lang="ts">
import { useGuideState, vGuide } from '@pet/25cny.guide-directive/index';
import { whenever } from '@vueuse/core';

import TeamWidget from '@/components/team/TeamWidget.vue';
import CardPopover from '@/components/team/popover/CardPopover.vue';
import TeamPopover from '@/components/team/popover/TeamPopover.vue';
import { useLogger } from '@/init/logger';
import { useAssistModel } from '@/models/assistModel';
import { useConfigModel } from '@/models/configModel';
import { useHomeModel } from '@/models/homeModel';
import { usePopoverModel } from '@/models/popoverModel';
import { SubBiz } from '@/models/shareModel';
import { useSocialGuide0714Model } from '@/models/socialGuide0714Model';
import { useTeamActionModel } from '@/models/team/teamAction.model';
import { useTeamDataModel } from '@/models/team/teamData.model';
import { useTeamDialogModel } from '@/models/team/teamDialog.model';

const { entry, popoverInfo, realShowPopover, closeRealShowPopover, isTeamMakeGuideBubble, guideId } =
    useTeamDataModel();
const { shareRemindToCheckIn } = useTeamActionModel();
const { openTeamOrCardSheet } = useTeamDialogModel();
const { weakGuideConfig } = useConfigModel();
const { currentGuide } = useGuideState();
const { sendShow, sendClick } = useLogger();
const { assistPromise } = useAssistModel();
const { refreshHome } = useHomeModel();
const { canAutoOpenTeamPanel, inSocialProcess } = useSocialGuide0714Model();
const { showCardPopover, showTeamPopover } = usePopoverModel();

// 是否展示气泡
const showPopover = computed(() => {
    return popoverInfo.value?.showPop ?? false;
});

assistPromise.then(async (assistData) => {
    if (assistData?.subBiz === SubBiz.ZUDU_SHARE) {
        await refreshHome('LUCK_SHAKE_SUDOKU');
    }
    watch(
        showPopover,
        async (val) => {
            realShowPopover.value = val;
        },
        { immediate: true },
    );
});

// 切换组队面板显示隐藏
function toggleTeamPanel(fromSocial = false) {
    openTeamOrCardSheet(entry.value?.type, fromSocial);
}

/** 气泡点击去提醒 */
const handleShare = (e: Event) => {
    e.stopPropagation();
    shareRemindToCheckIn();
};

const onTargetClick = (e: Event) => {
    const target = e.target as HTMLElement | null;
    if (isTeamMakeGuideBubble.value) {
        openTeamOrCardSheet(entry.value?.type);
    } else {
        if (target?.className === 'btn-main-text') {
            handleShare(e);
        } else {
            openTeamOrCardSheet(entry.value?.type);
        }
    }
    // eslint-disable-next-line no-underscore-dangle
    currentGuide.value?.el?.__destroyGuide?.();
    // 点击按钮不关闭气泡，走到 handleShare 再关闭
    return target !== null && target.className !== 'btn-main-text';
};

/**
 * 组队气泡的展示情况，通过这个值来隐藏主按钮的气泡
 * @param val
 */
const onShow = (val: boolean) => {
    if (!val) {
        inSocialProcess.value = false;
    }
    if (isTeamMakeGuideBubble.value) {
        showTeamPopover.value = val;
    } else {
        showCardPopover.value = val;
    }
};

whenever(
    canAutoOpenTeamPanel,
    () => {
        toggleTeamPanel(true);
    },
    { once: true, immediate: true },
);

const limit = computed(() => {
    if (inSocialProcess.value) {
        return {
            everydayLimitCount: Number.MAX_SAFE_INTEGER,
            duration: 10000,
        };
    }
    return {};
});
</script>

<template>
    <div v-if="entry?.type" class="pendant-buttons">
        <!-- 组队入口 -->
        <TeamWidget
            v-guide.target="{
                id: guideId,
                priority: inSocialProcess ? 40 : 20,
                enabled: inSocialProcess ? false : realShowPopover,
                component: guideId === 'GUIDE_TEAM' ? TeamPopover : CardPopover,
                props: {
                    popoverInfo,
                    handleShare,
                    sendShow: sendShow,
                    sendClick: sendClick,
                    duration: 10000,
                    onShow: onShow,
                },
                offset: {},
                onTargetClick,
                afterHide: closeRealShowPopover,
                overLimit: closeRealShowPopover,
                // 消失时间
                duration: 10000 + 233,
                fadeOutDuration: 233,
                nonInterval: inSocialProcess ? 0 : 3000,
                // 频控管理
                // totalLimitCount: 10,
                // 每日频控
                everydayLimitCount: 10,
                ...weakGuideConfig?.[guideId],
                ...limit,
            }"
            @click="() => toggleTeamPanel()"
        />
    </div>
</template>

<style lang="scss" scoped>
.pendant-buttons {
    position: absolute;
    // 挂件按钮的边距
    display: flex;
    justify-content: flex-end;
    bottom: 130px;
    right: -2px;
}
</style>
