<script setup lang="ts">
import Button from '@pet/adapt.button/index.vue';

import { inHeadless } from '@/utils/ssg';

defineProps<{
    direction: 'left' | 'right';
    text: string;
}>();

defineEmits<{
    (e: 'click'): void;
}>();
</script>

<template>
    <div :id="`side-button-wrap-${direction}`" class="side-button-wrap">
        <Button class="side-button" @click="$emit('click')">
            <div class="side-button-icon" :class="direction"></div>
            <div v-if="!inHeadless()" class="side-button-text">
                <span class="side-button-text-text">{{ text ?? '' }}</span>
            </div>
        </Button>
    </div>
</template>

<style lang="scss" scoped>
.side-button-wrap {
    position: relative;
    margin-top: 32px;

    .side-button {
        --adapt-button-width: 84px;
        --adapt-button-height: 58px;
        --adapt-button-border-radius: 0;
        --adapt-button-primary-background-color: transparent;
        --adapt-button-padding: 0;
        --adapt-button-font-size: 18px;
        --adapt-button-font-weight: 400;
        --adapt-button-main-font-family: KuaiYuanHuiTi;

        &-icon {
            position: absolute;
            &.left {
                width: 40px;
                height: 52px;
                @include bg('@/assets/side-button/money-icon.png');
                background-size: 100% 100%;
                top: -33px;
                left: 12px;
            }
            &.right {
                width: 69px;
                height: 62.5px;
                @include bg('@/assets/side-button/step-icon.png');
                background-size: 100% 100%;
                top: -34px;
                right: 2px;
            }
        }

        &-text {
            height: var(--adapt-button-height);
            position: relative;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;

            &-text {
                color: #fff;
                font-family: KuaiYuanHuiTi;
                font-size: 18px;
                font-style: normal;
                font-weight: 400;
                letter-spacing: 0.5px;
                position: relative;
            }
        }
    }
}

#side-button-wrap-left {
    .side-button {
        @include bg('@/assets/side-button/left-side-btn-bg.png');
        background-size: 100% 100%;
        transform-origin: left;
        &-text {
            transform: skew(-7deg);
            margin-right: 10px;
        }
    }
}

#side-button-wrap-right {
    .side-button {
        @include bg('@/assets/side-button/right-side-btn-bg.png');
        background-size: 100% 100%;
        transform-origin: right;
        &-text {
            transform: skew(-7deg);
            margin-left: 10px;
        }
    }
}
</style>
