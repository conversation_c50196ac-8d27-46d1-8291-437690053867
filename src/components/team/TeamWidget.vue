<!-- 组队挂件 -->
<script setup lang="ts">
import AvatarGroup from '@pet/adapt.avatar/avatar-group.vue';
import { whenever } from '@vueuse/core';

import fallbackImage from '@/assets/team/team-default.png';
import { ENTRY_STATUS } from '@/models/team/constants';
import { useTeamAnimationModel } from '@/models/team/teamAnimation.model';
import { useTeamDataModel } from '@/models/team/teamData.model';

const MAX_AVATAR_NUM = 3;
const { entry, teamPanel, cardPanelEntry } = useTeamDataModel();
const { receiveTaskFreeDialogFlyStatus } = useTeamAnimationModel();

// 头像列表
const avatarList = computed(() => {
    // 头像不足MAX_AVATAR_NUM时，填充
    const res = teamPanel.value?.teamUser?.slice(0, MAX_AVATAR_NUM).map((item) => item.userAvatar);
    if (!teamPanel.value || !res) {
        return Array(MAX_AVATAR_NUM).fill('');
    }
    while (res.length < MAX_AVATAR_NUM) {
        res.push('');
    }
    return res;
});
// 判断展示组队还是直通卡
const showCard = computed(() => {
    return entry.value?.type === ENTRY_STATUS.CARD;
});
</script>
<template>
    <div
        id="team-widget"
        v-log.both="{
            action: 'OP_ACTIVITY_MAKE_TEAM_BUTTON',
            params: {
                entry_status: entry?.type,
                number: entry?.teamCount,
            },
            type: 'show',
        }"
        class="team-widget"
    >
        <div
            v-if="showCard"
            class="team-card"
            :class="{
                'is-bounce': receiveTaskFreeDialogFlyStatus === 'fly-bounce',
            }"
            :style="{ 'background-image': `url(${cardPanelEntry?.cardImage})` }"
        ></div>
        <AvatarGroup
            v-else
            class="head"
            :srcs="avatarList"
            :max="3"
            :width="24"
            :gap="16"
            default-type="light"
            :fallback-head="fallbackImage"
            :reverse="true"
        />
        <div class="btn">{{ entry?.text }}</div>
    </div>
</template>

<style lang="scss" scoped>
@keyframes bounceAnimation {
    0% {
        transform: scale(1, 1);
    }
    38.462% {
        transform: scale(1.15, 0.85);
    }
    69.231% {
        transform: scale(0.85, 1.15);
    }
    100% {
        transform: scale(1, 1);
    }
}

.team-widget {
    display: flex;
    align-items: flex-end;
    flex-direction: column;
    box-sizing: border-box;
    width: 83px;
    height: 59px;
    padding-right: 6px;
    background: url('@/assets/team/team-widget.png') center/100% no-repeat;
    transition: transform 150ms ease-in-out;
    transform-origin: right center;
    .head {
        /* 边框变量 */
        --adapt-avatar-border-width: 1px;
        --adapt-avatar-border-color: #fff;
        margin-bottom: 4px;
        :deep(.avatar img) {
            border-radius: 50%;
        }
    }
    .team-card {
        width: 44px;
        height: 44px;
        margin: -17px 3px 0 0;
        background-size: 100%;
        background-position: center;
        background-repeat: no-repeat;
        transform-origin: center bottom;

        &.is-bounce {
            animation: bounceAnimation 0.433s 0s cubic-bezier(0.333, 0, 0.667, 1);
        }
    }
    .btn {
        color: #00437c;
        font-size: 13px;
        font-weight: 600;
        font-family: PingFang SC;
        letter-spacing: 0px;
        line-height: 18.2px;
        &::after {
            display: inline-block;
            content: '';
            width: 8px;
            height: 8px;
            margin-left: 2px;
            background: url('@/assets/team/team-arrow.png') center/100% no-repeat;
        }
    }
    &:active {
        transform: scale(0.95);
        opacity: 0.5;
    }
}
</style>
