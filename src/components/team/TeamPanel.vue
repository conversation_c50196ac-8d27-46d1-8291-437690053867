<script setup lang="ts">
/**
 * 团队面板组件
 * @description 展示团队信息、成员状态、道具信息等，支持邀请新成员和退出团队等操作
 */

// Vue 核心

// 外部依赖
import AdaptStroke from '@pet/adapt.stroke-text/index.vue';
import { toast } from '@pet/adapt.toast';
import AdaptTransition from '@pet/adapt.transition/index.vue';
import { sleep } from '@pet/yau.core';
import { useServerTimeNow } from '@pet/yau.yoda';
import { whenever } from '@vueuse/core';
import { computed, defineModel, defineProps, defineEmits } from 'vue';

// 内部组件
import CommonSheet from '@/components/common-sheet/CommonSheet.vue';
// Models
import { useLogger } from '@/init/logger';
import { useConfigModel } from '@/models/configModel';
import { useTeamActionModel } from '@/models/team/teamAction.model';
import { useTeamDataModel } from '@/models/team/teamData.model';
import { useTeamDialogModel } from '@/models/team/teamDialog.model';
import type { TeamUserView } from '@/services/open-api-docs/home/<USER>/schemas';
import { reportKeyActionStart } from '@/utils/log/keyActionLog';

// 类型定义

// 组件
import TeamInfoCard from './components/TeamInfoCard.vue';
import TeamInviteCard from './components/TeamInviteCard.vue';
import TeamPanelBroadcast from './components/TeamPanelBroadcast.vue';
import TeamPanelButton from './components/TeamPanelButton.vue';
import type { ITeamPanelButtonProps } from './components/types';
const Effect8536 = defineAsyncComponent(() => import('@/@effect/effect_8536/EffectIndex.vue'));
// 常量
import { EMPTY_MEMBER } from './const';
import type { ITeamStatus, ITeamProp } from './types';

interface Props {
    /** 面板展开后的toast提示 */
    enterToast?: string;
    fromSocial?: boolean;
}

interface Emits {
    /** 面板关闭后触发 */
    (e: 'end'): void;
}

// Props & Emits
const props = defineProps<Props>();
const emit = defineEmits<Emits>();

// v-model 绑定
const show = defineModel<boolean>('show', {
    type: Boolean,
    default: false,
});

// Composables
const { sendClick } = useLogger();
const { now } = useServerTimeNow();
const { shareInvite, shareRemindToCheckIn, teamPanelMutate } = useTeamActionModel();
const { teamPanel } = useTeamDataModel();
const { openExitTeamPopup, closeTeamPanel, openCardSheet } = useTeamDialogModel();
const { socialGuide0714 } = useConfigModel();

// 常量定义
const INVITE_BUTTON_TEXT = '立即邀请';

/**
 * 计算结束时间戳
 * @returns {number} 结束时间戳
 */
const endTime = ref<number>(0);

whenever(
    () => teamPanel.value?.remainingTeamBuildTime,
    (val) => {
        // useServerTimeNow 的 hasUpdate 会晚一些，countdown 内部又会向上取整，所以减 900
        const currentTime = now.value.getTime() - 900;
        const remainingTimeMs = val ?? 0;

        endTime.value = currentTime + remainingTimeMs;
    },
    { immediate: true },
);

/**
 * 计算团队状态信息
 * @returns {ITeamStatus} 团队状态
 */
const status = computed<ITeamStatus>(() => {
    const panel = teamPanel.value;
    // todo: status check
    const text = panel?.teamStatus === 2 ? panel?.teamBuildExpireDesc : panel?.togetherSignDesc;

    return {
        text: text ?? '',
        teamStatus: panel?.teamStatus ?? 0,
        canQuit: panel?.showExitTeam ?? false,
    };
});

/**
 * 处理团队成员数据
 * 1. 限制最多3个成员
 * 2. 不足则补充空成员
 * @returns {TeamUserView[]} 处理后的成员列表
 */
const members = computed<TeamUserView[]>(() => {
    const teamMembers = teamPanel.value?.teamUser ?? [];
    const maxTeamUser = teamPanel.value?.maxTeamUser;
    if (typeof maxTeamUser === 'number') {
        const currentLength = Math.min(teamMembers.length, maxTeamUser);
        const emptyMembersCount = maxTeamUser - currentLength;
        return [...teamMembers.slice(0, maxTeamUser), ...Array(emptyMembersCount).fill(EMPTY_MEMBER)];
    }
    return teamMembers;
});

/**
 * 计算道具信息
 * @returns {ITeamProp} 道具状态
 */
const prop = computed<ITeamProp>(() => {
    const panel = teamPanel.value;

    return {
        image: panel?.taskFreeCardImg ?? '',
        count: panel?.togetherSignDays ?? 0,
        name: '直通卡',
        description: panel?.taskFreeCardDesc ?? '',
        desc: panel?.taskFreeCardDesc ?? '',
        total: panel?.taskFreeCardNum ?? 7,
    };
});

const showTeamMask = ref(false);
const showTeamGuide = ref(false);
// 事件处理函数
const handleSheetAfterEnter = async () => {
    props.enterToast && toast(props.enterToast);
    if (props.fromSocial) {
        showTeamMask.value = true;
        await sleep(socialGuide0714.value?.maskHideTime ?? 3000);
        if (showTeamMask.value) {
            showTeamMask.value = false;
            showTeamGuide.value = true;
        }
    }
};

const sendClickLog = (clickArea: string) => {
    sendClick('OP_ACTIVITY_MAKE_TEAM_POP', {
        team_status: teamPanel.value?.teamStatus ?? '',
        click_area: clickArea,
    });
};

const handleRemindToCheckIn = () => {
    shareRemindToCheckIn();
};

const handleInvite = (clickArea: string, userInfo?: TeamUserView) => {
    reportKeyActionStart({
        name: 'team_up_success',
        extra_info: {
            clickArea,
        },
    });
    shareInvite(
        userInfo
            ? [
                  {
                      user_id: userInfo.userId,
                      user_name: userInfo.nickName,
                      headurl: userInfo.userAvatar,
                  },
              ]
            : [],
    );
    sendClickLog(clickArea);
};

const handleInviteAvatar = () => {
    handleInvite('invite+');
};

const handleInviteRecommend = (userInfo?: TeamUserView) => {
    handleInvite('head', userInfo);
};

const handleGotoTask = async () => {
    closeTeamPanel();
};

const handleCountdownEnd = () => {
    teamPanelMutate();
    closeTeamPanel();
};

const handleQuit = () => {
    openExitTeamPopup();
    sendClickLog('exit');
};

const showFreeCardEntry = computed(() => {
    // 拿到直通卡，且可用直通卡数量为0
    return (
        Boolean(teamPanel.value?.isReceivedFreeCard) &&
        teamPanel.value?.availableFreeCardNum === 0 &&
        (teamPanel.value?.freeCardList ?? [])?.length > 0
    );
});

/**
 * 根据团队状态返回底部按钮配置
 */
const button = computed<ITeamPanelButtonProps>(() => {
    const panel = teamPanel.value;
    const generateProp = (prop: ITeamPanelButtonProps) => {
        return {
            ...prop,
            onClick: () => {
                if (prop.onClick) {
                    prop.onClick();
                    sendClickLog(prop.text);
                }
            },
        };
    };

    if (panel?.teamSuccessMode) {
        if (panel.isAllSigned) {
            return generateProp({
                text: '我知道了',
                onClick: closeTeamPanel,
            });
        }

        if (panel.isSelfSigned) {
            return generateProp({
                text: '提醒好友',
                onClick: handleRemindToCheckIn,
            });
        }

        return generateProp({
            text: '去打卡',
            onClick: handleGotoTask,
        });
    }

    return {
        text: INVITE_BUTTON_TEXT,
        icon: 'wechat',
        onClick: () => {
            if (showTeamMask.value) {
                showTeamMask.value = false;
            }
            showTeamGuide.value = false;
            handleInvite(INVITE_BUTTON_TEXT);
        },
    };
});
const handleFreeCardEntryClick = async () => {
    await closeTeamPanel();
    openCardSheet();
};

// 还需要邀请多少人
const lackCount = computed(() => {
    const teamMembers = teamPanel.value?.teamUser ?? [];
    const maxTeamUser = Number(teamPanel.value?.maxTeamUser || 0);
    const diff = maxTeamUser - teamMembers.length;
    return diff > 0 ? diff : 0;
});
// 文案转换
const maskTip = computed(() => {
    if (!socialGuide0714.value?.maskTip || !lackCount.value) {
        return '';
    }
    return socialGuide0714.value.maskTip?.replace(/{count}/g, `${lackCount.value}`);
});
</script>

<template>
    <CommonSheet
        v-log="{
            type: 'show',
            action: 'OP_ACTIVITY_MAKE_TEAM_POP',
            params: {
                team_status: teamPanel?.teamStatus ?? '',
            },
        }"
        class="team-panel"
        :class="{ 'team-panel-mask': showTeamMask }"
        :show="show"
        :title="teamPanel?.title"
        @close="show = false"
        @after-enter="handleSheetAfterEnter"
        @after-leave="$emit('end')"
    >
        <template #header>
            <TeamPanelBroadcast class="broadcast" :message-list="teamPanel?.desc ?? []" />
        </template>
        <TeamInfoCard
            :end-time="endTime"
            :status="status"
            :members="members"
            @quit="handleQuit"
            @invite="handleInviteAvatar"
            @countdown-end="handleCountdownEnd"
        />
        <TeamInviteCard v-if="!teamPanel?.teamSuccessMode" class="card-invite-user" @invite="handleInviteRecommend" />
        <TeamPanelButton v-bind="button">
            <template #guide>
                <div v-if="showTeamGuide && maskTip" class="team-panel__invite-tag">{{ maskTip }}</div>
                <Effect8536 v-if="showTeamMask || showTeamGuide" class="team-panel__guide" :allow-hide="true" />
            </template>
        </TeamPanelButton>
        <div v-if="showFreeCardEntry" class="team-panel__free-card-entry" @click="handleFreeCardEntryClick">
            <div class="team-panel__free-card-entry-content">查看直通卡</div>
        </div>
        <div class="team-panel__last-padding"></div>
    </CommonSheet>
    <Teleport to="#app #bottom-button">
        <AdaptTransition
            name="fade-social-guide"
            :duration="{ enter: 200, leave: 240 }"
            :appear="showTeamMask"
            :delay="{ enter: 0, leave: 0 }"
        >
            <div v-if="showTeamMask" class="team-panel__mask">
                <AdaptStroke v-if="maskTip" class="mask-tip" :text="`${maskTip}!`" />
            </div>
        </AdaptTransition>
    </Teleport>
</template>

<style lang="scss" scoped>
.team-panel {
    --adapt-sheet-max-show-height: 800px;
}
:deep() {
    .header {
        height: 160px !important;
    }
    .sheet-content {
        padding: 0 16px !important;
        min-height: 0 !important;
    }
    .sheet-inner {
        overflow: unset !important;
    }
}
.team-panel-mask {
    :deep(.sheet-close) {
        z-index: 0 !important;
    }
}
.card-invite-user {
    margin-top: 16px;
}

.broadcast {
    position: absolute;
    bottom: 20px;
    left: 24px;
}

.team-panel__free-card-entry {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 16px;
}

.team-panel__free-card-entry-content {
    &::after {
        content: '';
        display: inline-block;
        width: 8px;
        height: 8px;
        margin-left: 2px;
        background-image: url('./assets/arrow-right-plain.svg');
        background-position: center;
        background-repeat: no-repeat;
        background-size: contain;
    }

    color: #9c9c9c;
    font-size: 12px;
    line-height: 17px;
}

.team-panel__invite-tag {
    position: absolute;
    top: -16px;
    right: -21px;
    height: 26px;
    box-sizing: content-box;
    border: 1px solid #fff;
    white-space: nowrap;
    font-family: PingFang SC;
    font-weight: 600;
    font-size: 13px;
    line-height: 26px;
    letter-spacing: 0px;
    padding: 0 6px;
    color: #222;
    background: linear-gradient(85.81deg, #85f5ef 9.92%, #d3ff29 72.47%, #efff34 114.35%);
    border-top-left-radius: 16px;
    border-top-right-radius: 16px;
    border-bottom-left-radius: 4px;
    border-bottom-right-radius: 16px;
    transform-origin: bottom left;
    opacity: 0;
    transform: scale(0.17, 0.17) rotate(6deg);
    animation: hash3f7722e2_0_keyframe_0 0.6s cubic-bezier(0.333, 0, 0.667, 1) forwards;
}
@keyframes hash3f7722e2_0_keyframe_0 {
    0% {
        opacity: 0;
        transform: scale(0.17, 0.17) rotate(6deg);
    }
    33.333% {
        transform: scale(1.03, 1.03) rotate(-10deg);
    }
    34.473% {
        opacity: 1;
        transform: scale(1.03, 1.03) rotate(-10deg);
    }
    66.667% {
        opacity: 1;
        transform: scale(0.99, 0.99) rotate(2deg);
    }
    100% {
        opacity: 1;
        transform: scale(1, 1) rotate(0deg);
    }
}

.team-panel__guide {
    position: absolute;
    top: -2px !important;
    right: -40px !important;
    opacity: 1 !important;
}

.team-panel__last-padding {
    height: 53px;
}
.team-panel__mask {
    position: fixed;
    background-color: rgba(0, 0, 0, 0.85);
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 10000;
    display: flex;
    flex-direction: row;
    justify-content: center;
    --adapt-stroke-text-color: linear-gradient(92.68deg, #ff6b00 7.48%, #ff0f00 83.91%);
    .mask-tip {
        position: absolute;
        height: 46px;
        bottom: 147px;
        font-family: KuaiYuanHuiTi;
        font-weight: 400;
        font-size: 30px;
        line-height: 46px;
        letter-spacing: 0px;
        opacity: 0;
        transform: scale(2.27, 2.27);
        transform-origin: bottom center;
        animation: hash454b3ddc_0_keyframe_0 2.8s 0.187s cubic-bezier(0.333, 0, 0.667, 1) forwards;
    }
}

@keyframes hash454b3ddc_0_keyframe_0 {
    0% {
        opacity: 0.2;
        transform: scale(2.27, 2.27);
    }
    8.929% {
        transform: scale(0.9, 0.9);
    }
    10.714% {
        opacity: 1;
    }
    16.071% {
        opacity: 1;
        transform: scale(1.05, 1.05);
    }
    23.214% {
        opacity: 1;
        transform: scale(1, 1);
    }
    85.714% {
        opacity: 1;
        transform: scale(1, 1);
    }
    92.856% {
        opacity: 1;
        transform: scale(1.1, 1.1);
    }
    100% {
        opacity: 0;
        transform: scale(0.2, 0.2);
    }
}
</style>
