<script setup lang="ts">
import GuideHead from '@pet/adapt.guide/index.vue';
import Popover from '@pet/adapt.popover/index.vue';
import { ref, onBeforeUnmount } from 'vue';

import { summerLocalStore } from '@/utils/localStore';

const props = defineProps<{
    popoverInfo: {
        showPop: boolean;
        popType: number;
        number: number;
    } | null;
    duration: number;
    sendShow: (action: string, params: Record<string, any>) => void;
    onShow: (show: boolean) => void;
}>();

const show = ref(false);

const showHand = ref(false);
onMounted(async () => {
    show.value = true;
    props.onShow(true);
    if (props.duration) {
        setTimeout(() => {
            show.value = false;
            props.onShow(false);
        }, props.duration);
    }
    const handShowed = await summerLocalStore.get('team-hand-showed');
    if (!handShowed) {
        showHand.value = true;
        summerLocalStore.set('team-hand-showed', true);
    }
    props.sendShow('OP_ACTIVITY_MAKE_TEAM_BUBBLE_REMIND_POP', {
        title: `3人组队一起打卡 挑战成功可${props.popoverInfo?.number || 7}天一键直达站点！`,
    });
});

onBeforeUnmount(() => {
    show.value = false;
    props.onShow(false);
});
</script>

<template>
    <Popover
        class="popover-team"
        :show="show"
        :position="{ right: 83, top: -22 }"
        arr-direction="right"
        arr-align="center"
        :show-arr="false"
        animation-type="scale-in-out-debounce"
    >
        <div class="popover-team__icon"></div>
        <div class="popover-team__content">
            <div class="popover-team__title"><span class="popover-team__num">3人组队</span>一起打卡，挑战</div>
            <div>
                成功可<span class="popover-team__num">{{ popoverInfo?.number }}天一键直达</span>站点！
            </div>
        </div>
        <div class="popover-team__arr"></div>
    </Popover>
    <GuideHead v-if="showHand" class="popover-team__hand" />
</template>

<style lang="scss" scoped>
.popover-team {
    display: flex;
    align-items: center;
    width: 233px;
    color: #000000;
    font-size: 14px;
    font-weight: 600;
    font-family: PingFang SC;
    letter-spacing: 0px;
    line-height: 20px;
    --adapt-tool-tip-inner-padding: 20px 8px;
    --adapt-tool-tip-background-color: linear-gradient(273deg, #6aff00 -15.36%, #e0ff95 81.4%);
    --adapt-tool-tip-border: 2px #fff solid;
    --adapt-tool-tip-radius: 24px;
    &__arr {
        position: absolute;
        top: 50%;
        transform: translateY(-50%);
        right: -8px;
        width: 8px;
        height: 14px;
        background: url('@/assets/team/popover-right.png') center/100% no-repeat;
    }
    &__icon {
        flex-shrink: 0;
        width: 42px;
        height: 42px;
        margin-right: 4px;
        background: url('@/assets/team/popover-icon.png') no-repeat;
        background-size: 100% 100%;
    }
    &__title {
        margin-bottom: 4px;
    }
    &__num {
        color: #fe3666;
    }
}

.popover-team__hand {
    --adapt-guide-hand-box-top: 40px;
    --adapt-guide-hand-box-left: -40px;
}
</style>
