<script setup lang="ts">
import Button from '@pet/adapt.button/index.vue';
import Popover from '@pet/adapt.popover/index.vue';
import { ref, onBeforeUnmount } from 'vue';

const props = defineProps<{
    popoverInfo: {
        showPop: boolean;
        popType: number;
        number: number;
    } | null;
    duration: number;
    handleShare: (e: Event) => void;
    sendShow: (action: string, params: Record<string, any>) => void;
    sendClick: (action: string, params: Record<string, any>) => void;
    onShow: (show: boolean) => void;
}>();
const popoverText = computed(() => {
    return `还有${props.popoverInfo?.number ?? 0}名队员没打卡`;
});

const show = ref(false);

function onClick(e: Event) {
    props.sendClick('OP_ACTIVITY_BUBBLE_REMIND_POP', {
        title: popoverText.value,
        remind_num: props.popoverInfo?.number,
        click_position: '去提醒',
    });
    props.handleShare(e);
}
onMounted(() => {
    show.value = true;
    props.onShow(true);
    props.sendShow('OP_ACTIVITY_BUBBLE_REMIND_POP', {
        title: popoverText.value,
        remind_num: props.popoverInfo?.number,
    });
    if (props.duration) {
        setTimeout(() => {
            show.value = false;
            props.onShow(false);
        }, props.duration);
    }
});

onBeforeUnmount(() => {
    show.value = false;
    props.onShow(false);
});
</script>

<template>
    <Popover
        class="popover-card"
        :show="show"
        :position="{ right: 0, top: -85 }"
        arr-direction="bottom"
        :show-arr="false"
        :enter-duration="2367"
        :leave-duration="333"
        animation-type="scale-in-out-remind"
    >
        <div class="popover-card__container">
            <div class="popover-card__container-bg">
                <div class="popover-card__container-icon"></div>
                <div class="popover-card__container-title">{{ popoverText }}</div>
                <Button
                    v-click-log="{
                        action: 'OP_ACTIVITY_BUBBLE_REMIND_POP',
                        params: {
                            title: popoverText,
                            remind_num: popoverInfo?.number,
                            click_position: '去提醒',
                        },
                    }"
                    class="popover-card__container-btn"
                    :height="30"
                    @click.stop="onClick"
                >
                    去提醒
                </Button>
            </div>
        </div>
        <div class="popover-card__arr"></div>
    </Popover>
</template>

<style lang="scss" scoped>
.popover-card {
    --adapt-tool-tip-inner-padding: 0;
    --adapt-tool-tip-background-color: transparent;
    --adapt-tool-tip-border: none;
    box-shadow: none !important;
    &__arr {
        position: absolute;
        right: var(--adapt-tool-arr-default-distance);
        bottom: -7px;
        width: 14px;
        height: 9px;
        background: url('@/assets/team/popover-down.png') center/100% no-repeat;
    }
    &__container {
        position: relative;
        display: flex;
        align-items: flex-end;
        height: 75px;
        overflow-y: hidden;
        &-bg {
            box-sizing: border-box;
            width: 255px;
            height: 51px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            background: linear-gradient(273deg, #6aff00 -15.36%, #e0ff95 81.4%);
            border: 2px #fff solid;
            border-radius: 14px;
            padding: 11px 12px 11px 6px;
        }
        &-icon {
            position: absolute;
            left: 4px;
            bottom: -10px;
            background: url('@/assets/team/team-tip.png') center/100% no-repeat;
            width: 50px;
            height: 75px;
            transform-origin: 36px 72px;
            animation: handRotation 2.4s 0.033s forwards infinite;

            @keyframes handRotation {
                0% {
                    transform: rotate(0deg);
                    animation-timing-function: cubic-bezier(0.17, 0, 0.67, 1);
                }

                5.29% {
                    /* 129ms */
                    transform: rotate(13deg);
                    animation-timing-function: cubic-bezier(0.33, 0, 0.67, 1);
                }

                10.57% {
                    /* 258ms */
                    transform: rotate(-9deg);
                    animation-timing-function: cubic-bezier(0.33, 0, 0.67, 1);
                }

                15.82% {
                    /* 386ms */
                    transform: rotate(7deg);
                    animation-timing-function: cubic-bezier(0.33, 0, 0.67, 1);
                }

                21.11% {
                    /* 515ms */
                    transform: rotate(-5deg);
                    animation-timing-function: cubic-bezier(0.33, 0, 0.67, 1);
                }

                28.69% {
                    /* 700ms */
                    transform: rotate(3deg);
                    animation-timing-function: cubic-bezier(0.33, 0, 0.83, 1);
                }

                38.24% {
                    /* 933ms */
                    transform: rotate(0deg);
                }

                100% {
                    /* 2440ms */
                    transform: rotate(0deg);
                }
            }
        }
        &-title {
            flex: 1;
            color: #000000;
            font-size: 14px;
            font-weight: 500;
            font-family: PingFang SC;
            letter-spacing: 0px;
            line-height: 18px;
            margin: 0 8px 0 52px;
            white-space: nowrap;
        }
        &-btn {
            --adapt-button-height: 28px;
            --adapt-button-line-height: 12px;
            --adapt-button-font-size: 11px;
            --adapt-button-padding: 0 8px;
            --adapt-button-primary-background-color: #f42110ff;
        }
    }
}
</style>
