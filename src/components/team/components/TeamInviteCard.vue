<script setup lang="ts">
/**
 * 团队邀请卡片组件
 * @description 展示推荐的组队好友列表，支持换一换和邀请功能
 * @example
 * ```vue
 * <TeamInviteCard />
 * ```
 */
import { useTeamActionModel } from '@/models/team/teamAction.model';
import { useTeamDataModel } from '@/models/team/teamData.model';
import type { TeamUserView } from '@/services/open-api-docs/home/<USER>/schemas';

import TeamMembers from './TeamMembers.vue';
import { CARD_INVITE_USER_CONSTANTS } from './const';

const emit = defineEmits<{
    (e: 'invite', userInfo?: TeamUserView): void;
}>();

const { invite, teamPanel } = useTeamDataModel();
const { nextInvitePage } = useTeamActionModel();

// 处理邀请列表数据
const members = computed(() => invite.value.list ?? []);

// 事件处理
const handleInvite = (userInfo?: TeamUserView) => {
    emit('invite', userInfo);
};

const handleRefresh = () => {
    nextInvitePage();
};
</script>

<template>
    <div v-if="members.length" class="card-invite">
        <h3 class="card-invite__title">{{ CARD_INVITE_USER_CONSTANTS.TITLE_TEXT }}</h3>
        <div
            v-click-log="{
                action: 'OP_ACTIVITY_MAKE_TEAM_POP',
                params: {
                    team_status: teamPanel?.teamStatus ?? '',
                    click_area: 'change',
                },
            }"
            class="card-invite__refresh"
            @click="handleRefresh"
        >
            <img class="card-invite__refresh-icon" src="../assets/refresh.svg" alt="刷新" />
            <span class="card-invite__refresh-text">{{ CARD_INVITE_USER_CONSTANTS.REFRESH_TEXT }}</span>
        </div>

        <TeamMembers
            class="card-invite__members"
            :members="members"
            :show-invite-button="true"
            :gap="CARD_INVITE_USER_CONSTANTS.DEFAULT_GAP"
            :avatar-size="CARD_INVITE_USER_CONSTANTS.DEFAULT_AVATAR_SIZE"
            @invite="handleInvite"
        />
    </div>
</template>

<style lang="scss" scoped>
.card-invite__members :deep(.team-members__name) {
    margin-top: 4px;
}

.card-invite {
    position: relative;
    box-sizing: border-box;
    padding-top: 69px;
    height: 168px;
    border-radius: 24px;
    background: #ffffff;

    .card-invite__title {
        position: absolute;
        left: 32px;
        top: 20px;
        margin: 0;
        font-size: 16px;
        font-weight: 500;
        color: #000;
        line-height: 22px;
    }

    .card-invite__refresh {
        position: absolute;
        right: 14px;
        top: 19px;
        box-sizing: border-box;
        width: 58px;
        height: 24px;
        padding: 0 7px;
        border-radius: 30px;
        background: rgba(0, 0, 0, 0.04);
        display: flex;
        justify-content: flex-end;
        align-items: center;
        cursor: pointer;
        transition: opacity 0.2s;

        &:hover {
            opacity: 0.8;
        }

        &:active {
            opacity: 0.6;
        }
    }

    .card-invite__refresh-icon {
        position: absolute;
        left: 4px;
        width: 13px;
        height: 13px;
    }

    .card-invite__refresh-text {
        min-width: 33px;
        font-size: 11px;
        line-height: 15.4px;
        color: #9c9c9c;
    }
}
</style>
