<script setup lang="ts">
/**
 * 团队面板按钮组件
 * @description 展示团队面板不同场景下的操作按钮，支持带图标的按钮展示
 * @example
 * ```vue
 * <TeamPanelButton
 *   text="确认"
 *   type="primary"
 *   :disabled="false"
 *   @click="handleClick"
 * />
 * ```
 */
import Button from '@pet/adapt.button/index.vue';

import type { ITeamPanelButtonProps } from './types';

const props = defineProps<ITeamPanelButtonProps>();

// Emits 定义
const emit = defineEmits<{
    /** 按钮点击事件 */
    (e: 'click'): void;
}>();

const handleClick = () => {
    emit('click');
};
</script>

<template>
    <div id="bottom-button" class="bottom-button">
        <Button class="bottom-button__btn" :type="props.type || 'primary'" :height="72" @click="handleClick">
            <template v-if="props.icon === 'wechat'" #icon>
                <img src="../assets/wechat.svg" />
            </template>
            {{ props.text }}
            <slot name="guide"></slot>
        </Button>
    </div>
</template>

<style lang="scss" scoped>
.bottom-button {
    font-family: KuaiYuanHuiTi;
    display: flex;
    justify-content: center;
    padding-top: 26px;
    position: relative;

    .bottom-button__btn {
        position: relative;
        z-index: 10001;
        --adapt-button-width: 260px;
        --adapt-button-primary-font-linear: #fff;
        --adapt-button-primary-background-image: linear-gradient(
            100deg,
            #ff7001 -1.65%,
            #fc2d39 47.88%,
            #f31906 98.68%
        );
        --adapt-button-font-weight: 400;
        --adapt-button-font-size: 24px;
        --adapt-button-icon-width: 30px;
        --adapt-button-icon-height: 30px;
    }
}
</style>
