<script setup lang="ts">
/**
 * 团队成员列表组件
 * @description 展示团队成员列表，支持显示成员状态和邀请按钮
 * @example
 * ```vue
 * <TeamMembers
 *   :members="membersList"
 *   :show-invite-button="true"
 *   :show-sign-status="true"
 *   @invite="handleInvite"
 * />
 * ```
 */
import XImage from '@pet/adapt.image/index.vue';
import { px2rem } from '@pet/core.mobile';
import { debounce, isInKwai, isInNebula } from '@pet/yau.core';

import type { TeamUserView } from '@/services/open-api-docs/home/<USER>/schemas';

import { TEAM_MEMBERS_CONSTANTS } from './const';
import type { ITeamMembersProps } from './types';

const props = withDefaults(defineProps<ITeamMembersProps>(), {
    gap: TEAM_MEMBERS_CONSTANTS.DEFAULT_GAP,
    avatarSize: TEAM_MEMBERS_CONSTANTS.DEFAULT_AVATAR_SIZE,
});

// 样式计算
const style = computed(() => ({
    gap: px2rem(props.gap),
    avatarSize: px2rem(props.avatarSize),
}));

// 事件定义
const emit = defineEmits<{
    (e: 'invite', userInfo?: TeamUserView): void;
}>();

// 事件处理
const handleInvite = debounce((userInfo?: TeamUserView) => {
    emit('invite', userInfo);
}, 200);

// 获取显示名称
const getDisplayName = (member: TeamUserView) => {
    return member.currentUser ? TEAM_MEMBERS_CONSTANTS.CURRENT_USER_TEXT : member.nickName;
};

// 获取打卡状态文本
const getSignStatusText = (isSigned: boolean | null | undefined) => {
    return Boolean(isSigned) ? TEAM_MEMBERS_CONSTANTS.SIGNED_TEXT : TEAM_MEMBERS_CONSTANTS.UNSIGNED_TEXT;
};

const proto = (() => {
    if (isInNebula()) {
        return 'ksnebula';
    } else if (isInKwai()) {
        return 'kwai';
    }
})();

const handleMemberClick = (member: TeamUserView) => {
    if (!props.isGotoProfile) {
        return;
    }
    const protoOfGoto = proto ?? 'kwai';
    const path = member.currentUser ? 'myprofile' : 'profile/' + member.userId;
    window.location.href = `${protoOfGoto}://${path}`;
};
</script>

<template>
    <div class="team-members">
        <template v-for="(member, index) in members" :key="index">
            <!-- 真实成员 -->
            <div v-if="member.userId" class="team-members__item" @click="handleMemberClick(member)">
                <div class="team-members__avatar-container">
                    <XImage
                        :src="member.userAvatar"
                        :aspect-ratio="1"
                        class="team-members__avatar"
                        object-fit="cover"
                    />
                    <div
                        v-if="showSignStatus"
                        class="team-members__sign-status"
                        :class="{ 'is-signed': member.todaySign }"
                    >
                        {{ getSignStatusText(member.todaySign) }}
                    </div>
                </div>
                <div v-if="showInviteButton" class="team-members__invite-badge" @click="() => handleInvite(member)">
                    <span class="team-members__invite-text">{{ TEAM_MEMBERS_CONSTANTS.INVITE_TEXT }}</span>
                </div>
                <span class="team-members__name" :title="getDisplayName(member)">
                    {{ getDisplayName(member) }}
                </span>
            </div>
            <!-- 空成员（邀请槽） -->
            <div v-else class="team-members__item" @click="() => handleInvite()">
                <div class="team-members__avatar-container">
                    <div class="team-members__empty-avatar" role="img" aria-label="空头像"></div>
                </div>
                <div class="team-members__invite-btn">
                    {{ TEAM_MEMBERS_CONSTANTS.INVITE_TEXT }}
                </div>
            </div>
        </template>
    </div>
</template>

<style scoped lang="scss">
.team-members {
    display: flex;
    justify-content: center;

    .team-members__item {
        display: flex;
        flex-direction: column;
        align-items: center;
        position: relative;
        width: calc(v-bind('style.gap') + v-bind('style.avatarSize'));
        cursor: pointer;
    }

    .team-members__avatar-container {
        position: relative;
        width: v-bind('style.avatarSize');
        height: v-bind('style.avatarSize');
        border-radius: 50%;
    }

    .team-members__avatar {
        width: 100%;
        height: 100%;
        border-radius: 50%;
    }

    .team-members__empty-avatar {
        width: 100%;
        height: 100%;
        border-radius: 50%;
        background: #f5f5f5 url('../assets/avatar-empty.svg') center/cover no-repeat;
    }

    .team-members__sign-status {
        position: absolute;
        bottom: -4px;
        left: 50%;
        transform: translateX(-50%);
        display: flex;
        align-items: center;
        justify-content: center;
        height: 18px;
        width: 38px;
        border-radius: 16px;
        font-size: 10px;
        font-weight: 600;
        color: #fff;
        background-color: #fe3666;
        &.is-signed {
            background-color: #00000080;
        }
    }

    .team-members__name {
        width: 100%;
        height: 17px;
        margin-top: 2px;
        font-size: 12px;
        color: #9c9c9c;
        line-height: 17px;
        text-align: center;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }

    .team-members__invite-badge {
        z-index: 1;
        position: relative;
        width: 42px;
        height: 23px;
        margin-top: -11px;
        border-radius: 12px;
        background: #ffe6e4;
        display: flex;
        justify-content: center;
        align-items: center;
        transition: opacity 0.2s;
    }

    .team-members__invite-text {
        display: block;
        font-size: 11px;
        line-height: 16px;
        font-family: 'PingFang SC', sans-serif;
        font-weight: 500;
        color: #f42110;
    }

    .team-members__invite-btn {
        position: absolute;
        bottom: 0;
        left: 50%;
        transform: translateX(-50%);
        width: 48px;
        height: 24px;
        border-radius: 20px;
        background: #f42110;
        color: #ffffff;
        font-size: 11px;
        line-height: 24px;
        font-family: 'PingFang SC', sans-serif;
        font-weight: 500;
        text-align: center;
        transition: opacity 0.2s;
    }

    .team-members__item:hover .team-members__invite-btn {
        opacity: 0.8;
    }

    .team-members__item:active .team-members__invite-btn {
        opacity: 0.6;
    }
}
</style>
