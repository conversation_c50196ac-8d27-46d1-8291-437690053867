<script setup lang="ts">
import PopupTransTitleEnter from '@effect/popup-trans-title-enter/EffectIndex.vue';
import { useDowngradeLevel } from '@pet/25cny.downgrade-level';
import Header from '@pet/adapt.heading/index.vue';
import Logo from '@pet/adapt.logo/index.vue';
import AdaptTransition from '@pet/adapt.transition/index.vue';
import { sleep } from '@pet/yau.core';
import { watch } from 'vue-demi';

import { REWARD_TRANS_POPUP_TEXT } from '@/common/const/progress';
import { useLogger } from '@/init/logger';
import { SoundType, useAudioModel } from '@/models/audioModel';
import { useConfigModel } from '@/models/configModel';
import type { PopupType } from '@/services/open-api-docs/home/<USER>/schemas';
import { POPUP_ACTION } from '@/utils/log/type';

const CommonModal = defineAsyncComponent(() => import('@/components/common-modals/CommonModal.vue'));
const { sendShow } = useLogger();
const { playSound } = useAudioModel();
const { effectShowStatus } = useDowngradeLevel();
const { kconfConfig } = useConfigModel();

const rewardTransPopupText = computed(() => kconfConfig.value?.rewardTransPopupText ?? REWARD_TRANS_POPUP_TEXT);

const props = withDefaults(
    defineProps<{
        popupType: PopupType;
        show?: boolean;
        sponsorLogo?: string;
        sponsorText?: string;
        title?: string;
        subTitle?: string;
        desc?: string;
        icon?: string;
        productId?: string;
    }>(),
    {
        show: false,
    },
);

const title = computed(() => props.title || rewardTransPopupText.value.title);
const subTitle = computed(() => props.subTitle || rewardTransPopupText.value.subTitle);

const emit = defineEmits<{
    (event: 'update:show', show: boolean): void;
    (event: 'end'): void;
}>();

const infosTransDelay = computed(() => {
    return {
        enter: 300,
        leave: 0,
    };
});

const infosTransDuration = computed(() => {
    return {
        enter: 500,
        leave: 0,
    };
});

// Debug value
const RATIO = 1;

watch(
    () => props.show,
    async (val) => {
        if (val) {
            playSound(SoundType.CHEER);

            sendShow(POPUP_ACTION.CORE_POP, {
                popup_type: props.popupType,
                brand_name: props.sponsorText ?? '',
                title: props.title ?? '',
                coupon_id: '',
                good_id: props.productId ?? '',
            });

            const allEffectEndDuration = 3300;
            await sleep(allEffectEndDuration * RATIO);
            emit('update:show', false);
            emit('end');
        }
    },
    { immediate: true },
);
</script>

<template>
    <CommonModal
        class="full_trans_modal"
        :show="show"
        type="layer"
        ani-type="no-effect"
        :light-type="'cheers-plus'"
        @after-leave="emit('end')"
    >
        <AdaptTransition appear name="no-effect" :delay="{ enter: 133, leave: 0 }">
            <PopupTransTitleEnter v-if="effectShowStatus.L1" class="effect_title_enter" :loop="false" />
        </AdaptTransition>

        <AdaptTransition appear name="fade" :delay="infosTransDelay" :duration="infosTransDuration">
            <div class="top_container">
                <Logo
                    v-if="sponsorLogo || sponsorText"
                    class="logo"
                    :src="sponsorLogo"
                    :brand-logo-degrade-text="sponsorText"
                />

                <Header class="header" :title="title" :sub-title="subTitle" :show-lottie="effectShowStatus.L1">
                </Header>
            </div>
        </AdaptTransition>

        <img :class="['img_prize', effectShowStatus.L1 ? 'effect_prize' : '']" :src="icon" />

        <AdaptTransition appear name="fade" :delay="{ enter: 133, leave: 0 }">
            <div class="desc">{{ desc }}</div>
        </AdaptTransition>
    </CommonModal>
</template>

<!-- eslint-disable-next-line vue-scoped-css/enforce-style-type -->
<style lang="scss">
.full_trans_modal .transform-wrapper {
    transform: translateY(-50px);
}
</style>

<style lang="scss" scoped>
.effect_title_enter {
    position: absolute;
    top: 10px;
    z-index: 1;
}

.header {
    :deep(.title-item) {
        font-family: KuaiYuanHuiTi;
        font-weight: 400;
        font-size: 36px;
        line-height: 34px;
        letter-spacing: 0px;
        text-align: center;
        vertical-align: middle;
        --adapt-heading-title-font-color: linear-gradient(357.01deg, #ffeeb1 -11.05%, #ffffff 88.91%);
    }

    :deep(.top) {
        --adapt-heading-title-margin-bottom: 16.25px;
    }

    :deep(.sub-title) {
        --adapt-heading-sub-title-font-family: PingFang SC;
        --adapt-heading-sub-title-font-color: #fff;
        --adapt-heading-sub-title-font-size: 13px;
        vertical-align: middle;
        line-height: 16px;
        opacity: 0.4;
    }
}

.img_prize {
    width: 200px;
    height: 200px;
    object-fit: cover;
    margin-top: 25px;
    z-index: 2;

    &.effect_prize {
        transform-origin: center center;
        animation:
            prize_enter_a 666.667ms 0ms linear forwards,
            prize_leave_a 200ms 2.9s linear forwards;
    }
}

.desc {
    font-family: KuaiYuanHuiTi;
    font-weight: 400;
    font-size: 24px;
    line-height: 100%;
    letter-spacing: 0px;
    text-align: center;
    color: #fef3c8;
    animation: element_show 0.08s 0s linear forwards;
}

@keyframes element_show {
    0% {
        opacity: 0;
    }

    100% {
        opacity: 1;
    }
}

@keyframes prize_enter_a {
    0% {
        opacity: 1;
        transform: matrix3d(0.366, 0, 0, 0, 0, 0.366, 0, 0, 0, 0, 1, 0, -0.031, -92.821, 0, 1);
    }

    15% {
        opacity: 1;
        transform: matrix3d(0.482, 0, 0, 0, 0, 0.482, 0, 0, 0, 0, 1, 0, -0.048, -180.049, 0, 1);
    }

    30% {
        opacity: 1;
        transform: matrix3d(0.593, 0, 0, 0, 0, 0.593, 0, 0, 0, 0, 1, 0, 0.006, -192.207, 0, 1);
    }

    45% {
        opacity: 1;
        transform: matrix3d(0.731, 0, 0, 0, 0, 0.731, 0, 0, 0, 0, 1, 0, 0.15, -135.542, 0, 1);
    }

    60% {
        opacity: 1;
        transform: matrix3d(1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 0.5, 21.009, 0, 1);
    }

    75% {
        opacity: 1;
        transform: matrix3d(1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 0.302, 12.12, 0, 1);
    }

    90% {
        opacity: 1;
        transform: matrix3d(1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1);
    }
}

@keyframes prize_leave_a {
    0% {
        opacity: 1;
        transform: matrix3d(1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1);
    }

    50% {
        opacity: 0.678;
        transform: matrix3d(0.893, 0, 0, 0, 0, 0.893, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1);
    }

    100% {
        opacity: 0;
        transform: matrix3d(0.1, 0, 0, 0, 0, 0.1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1);
    }
}
</style>
