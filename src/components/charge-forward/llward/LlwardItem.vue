<script lang="ts" setup>
import { useDowngradeLevel } from '@pet/25cny.downgrade-level';

import { useLogger } from '@/init/logger';
import { useAnimationPause } from '@/models/animationVisibility';
import { useForwardRushGridModel } from '@/models/forwardRushGridModel';
import { GridTaskStatus } from '@/services/open-api-docs/home/<USER>/schemas';

import { GridllwardType } from '../manager/config';

const { sendShow } = useLogger();

const { effectShowStatus } = useDowngradeLevel();
const { isAnimationPause } = useAnimationPause();
const { getChallengeGridStatus } = useForwardRushGridModel();

const props = defineProps<{
    icon: string;
    step: number;
    type: GridllwardType;
}>();

const downgrade = computed(() => {
    return !effectShowStatus.value.L3;
});

const challengeStatus = computed(() => {
    return getChallengeGridStatus(props.step);
});

watch(
    () => props.type,
    () => {
        if (props.type === GridllwardType.TEAM_GRID) {
            sendShow('OP_ACTIVITY_MAKE_TEAM_ICON', {});
        }
    },
    {
        immediate: true,
    },
);
</script>
<template>
    <div class="llward-item">
        <template v-if="type === GridllwardType.TASK_GRID && challengeStatus !== GridTaskStatus.COMPLETED">
            <img src="../assets/challenge/challengeIcon.png" class="llward-challenge-downgrade" />
            <img
                v-if="challengeStatus === GridTaskStatus.IN_PROGRESS"
                src="../assets/challenge/challengeInProgress.png"
                class="llward-challenge-progress"
            />
        </template>
        <template v-else-if="type !== GridllwardType.TASK_GRID">
            <img
                :src="icon"
                class="llward-icon"
                :class="{
                    'llward-icon-ani': !downgrade,
                    'llward-icon-ani-pause': !downgrade && isAnimationPause,
                    'llward-icon-team': type === GridllwardType.TEAM_GRID,
                }"
            />
            <div v-if="!downgrade" class="llward-bottom"></div>
        </template>
    </div>
</template>
<style lang="scss" scoped>
.llward-item {
    position: absolute;
    width: 99px;
    height: 118px;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    margin: auto;
    .llward-challenge {
        position: absolute;
        top: -30px;
        left: -30px;
        bottom: 0;
        right: 0;
        margin: auto;
        width: 150px;
        height: 170px;
        object-fit: contain;
    }
    .llward-challenge-downgrade {
        position: absolute;
        top: -10px;
        left: 0;
        bottom: 0;
        right: 0;
        margin: auto;
        width: 96px;
        height: 102px;
        object-fit: contain;
    }
    .llward-challenge-progress {
        position: relative;
        top: 76px;
        left: 21px;
        margin: auto;
        width: 59px;
        height: 27px;
        object-fit: contain;
        z-index: 9;
    }
    .llward-icon {
        position: absolute;
        top: 0px;
        left: 0;
        bottom: 0;
        right: 0;
        margin: auto;
        width: 76px;
        height: 76px;
        object-fit: contain;
    }
    .llward-icon-team {
        width: 93px;
        height: 85px;
    }
    .llward-bottom {
        position: absolute;
        width: 76px;
        height: 35px;
        left: 0;
        bottom: 18px;
        right: 0;
        margin: auto;
        transform-origin: 38px 17.5px;
        animation: llward-bottom-ani 3000ms 0ms steps(10) infinite;
        @include bg('../assets/bg/llwdBottom.png');
        background-size: 100% 100%;
        z-index: -1;
    }
}
.llward-icon-ani {
    transform-origin: 29px 29px;
    animation: llward-animation 3000ms 0ms steps(10) infinite;
    animation-play-state: running;
}

.llward-icon-ani-pause {
    animation-play-state: paused;
}

@keyframes llward-animation {
    0% {
        opacity: 1;
        transform: matrix3d(1, 0.022, 0, 0, -0.022, 1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1);
    }

    3.3% {
        opacity: 1;
        transform: matrix3d(1, 0.014, 0, 0, -0.014, 1, 0, 0, 0, 0, 1, 0, 0, -0.099, 0, 1);
    }

    6.7% {
        opacity: 1;
        transform: matrix3d(1, 0.007, 0, 0, -0.007, 1, 0, 0, 0, 0, 1, 0, 0, -0.38, 0, 1);
    }

    10% {
        opacity: 1;
        transform: matrix3d(1, 0.003, 0, 0, -0.003, 1, 0, 0, 0, 0, 1, 0, 0, -0.813, 0, 1);
    }

    13.3% {
        opacity: 1;
        transform: matrix3d(1, 0, 0, 0, -0, 1, 0, 0, 0, 0, 1, 0, 0, -1.371, 0, 1);
    }

    16.7% {
        opacity: 1;
        transform: matrix3d(1, 0, 0, 0, -0, 1, 0, 0, 0, 0, 1, 0, 0, -2.028, 0, 1);
    }

    20% {
        opacity: 1;
        transform: matrix3d(1, 0.003, 0, 0, -0.003, 1, 0, 0, 0, 0, 1, 0, 0, -2.756, 0, 1);
    }

    23.3% {
        opacity: 1;
        transform: matrix3d(1, 0.008, 0, 0, -0.008, 1, 0, 0, 0, 0, 1, 0, 0, -3.528, 0, 1);
    }

    26.7% {
        opacity: 1;
        transform: matrix3d(1, 0.015, 0, 0, -0.015, 1, 0, 0, 0, 0, 1, 0, 0, -4.315, 0, 1);
    }

    30% {
        opacity: 1;
        transform: matrix3d(1, 0.023, 0, 0, -0.023, 1, 0, 0, 0, 0, 1, 0, 0, -5.092, 0, 1);
    }

    33.3% {
        opacity: 1;
        transform: matrix3d(0.999, 0.032, 0, 0, -0.032, 0.999, 0, 0, 0, 0, 1, 0, 0, -5.831, 0, 1);
    }

    36.7% {
        opacity: 1;
        transform: matrix3d(0.999, 0.042, 0, 0, -0.042, 0.999, 0, 0, 0, 0, 1, 0, 0, -6.504, 0, 1);
    }

    40% {
        opacity: 1;
        transform: matrix3d(0.999, 0.052, 0, 0, -0.052, 0.999, 0, 0, 0, 0, 1, 0, 0, -7.085, 0, 1);
    }

    43.3% {
        opacity: 1;
        transform: matrix3d(0.998, 0.063, 0, 0, -0.063, 0.998, 0, 0, 0, 0, 1, 0, 0, -7.545, 0, 1);
    }

    46.7% {
        opacity: 1;
        transform: matrix3d(0.997, 0.072, 0, 0, -0.072, 0.997, 0, 0, 0, 0, 1, 0, 0, -7.858, 0, 1);
    }

    50% {
        opacity: 1;
        transform: matrix3d(0.997, 0.082, 0, 0, -0.082, 0.997, 0, 0, 0, 0, 1, 0, 0, -7.996, 0, 1);
    }

    53.3% {
        opacity: 1;
        transform: matrix3d(0.996, 0.09, 0, 0, -0.09, 0.996, 0, 0, 0, 0, 1, 0, 0, -7.932, 0, 1);
    }

    56.7% {
        opacity: 1;
        transform: matrix3d(0.995, 0.096, 0, 0, -0.096, 0.995, 0, 0, 0, 0, 1, 0, 0, -7.673, 0, 1);
    }

    60% {
        opacity: 1;
        transform: matrix3d(0.995, 0.101, 0, 0, -0.101, 0.995, 0, 0, 0, 0, 1, 0, 0, -7.249, 0, 1);
    }

    63.3% {
        opacity: 1;
        transform: matrix3d(0.995, 0.104, 0, 0, -0.104, 0.995, 0, 0, 0, 0, 1, 0, 0, -6.688, 0, 1);
    }

    66.7% {
        opacity: 1;
        transform: matrix3d(0.995, 0.104, 0, 0, -0.104, 0.995, 0, 0, 0, 0, 1, 0, 0, -6.021, 0, 1);
    }

    70% {
        opacity: 1;
        transform: matrix3d(0.995, 0.102, 0, 0, -0.102, 0.995, 0, 0, 0, 0, 1, 0, 0, -5.276, 0, 1);
    }

    73.3% {
        opacity: 1;
        transform: matrix3d(0.995, 0.097, 0, 0, -0.097, 0.995, 0, 0, 0, 0, 1, 0, 0, -4.485, 0, 1);
    }

    76.7% {
        opacity: 1;
        transform: matrix3d(0.996, 0.091, 0, 0, -0.091, 0.996, 0, 0, 0, 0, 1, 0, 0, -3.676, 0, 1);
    }

    80% {
        opacity: 1;
        transform: matrix3d(0.997, 0.082, 0, 0, -0.082, 0.997, 0, 0, 0, 0, 1, 0, 0, -2.879, 0, 1);
    }

    83.3% {
        opacity: 1;
        transform: matrix3d(0.997, 0.073, 0, 0, -0.073, 0.997, 0, 0, 0, 0, 1, 0, 0, -2.123, 0, 1);
    }

    86.7% {
        opacity: 1;
        transform: matrix3d(0.998, 0.063, 0, 0, -0.063, 0.998, 0, 0, 0, 0, 1, 0, 0, -1.438, 0, 1);
    }

    90% {
        opacity: 1;
        transform: matrix3d(0.999, 0.052, 0, 0, -0.052, 0.999, 0, 0, 0, 0, 1, 0, 0, -0.854, 0, 1);
    }

    93.3% {
        opacity: 1;
        transform: matrix3d(0.999, 0.042, 0, 0, -0.042, 0.999, 0, 0, 0, 0, 1, 0, 0, -0.399, 0, 1);
    }

    96.7% {
        opacity: 1;
        transform: matrix3d(0.999, 0.032, 0, 0, -0.032, 0.999, 0, 0, 0, 0, 1, 0, 0, -0.105, 0, 1);
    }

    100% {
        opacity: 1;
        transform: matrix3d(1, 0.022, 0, 0, -0.022, 1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1);
    }
}

@keyframes llward-bottom-ani {
    0% {
        opacity: 0.8;
        transform: matrix3d(0.696, 0, 0, 0, 0, 0.696, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1);
    }

    3.3% {
        opacity: 0.8;
        transform: matrix3d(0.711, 0, 0, 0, 0, 0.711, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1);
    }

    6.7% {
        opacity: 0.668;
        transform: matrix3d(0.727, 0, 0, 0, 0, 0.727, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1);
    }

    10% {
        opacity: 0.433;
        transform: matrix3d(0.742, 0, 0, 0, 0, 0.742, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1);
    }

    13.3% {
        opacity: 0.234;
        transform: matrix3d(0.757, 0, 0, 0, 0, 0.757, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1);
    }

    16.7% {
        opacity: 0.042;
        transform: matrix3d(0.772, 0, 0, 0, 0, 0.772, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1);
    }

    20% {
        opacity: 0;
        transform: matrix3d(0.788, 0, 0, 0, 0, 0.788, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1);
    }

    23.3% {
        opacity: 0;
        transform: matrix3d(0.797, 0, 0, 0, 0, 0.797, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1);
    }

    26.7% {
        opacity: 0;
        transform: matrix3d(0.782, 0, 0, 0, 0, 0.782, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1);
    }

    30% {
        opacity: 0;
        transform: matrix3d(0.768, 0, 0, 0, 0, 0.768, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1);
    }

    33.3% {
        opacity: 0;
        transform: matrix3d(0.753, 0, 0, 0, 0, 0.753, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1);
    }

    36.7% {
        opacity: 0;
        transform: matrix3d(0.739, 0, 0, 0, 0, 0.739, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1);
    }

    40% {
        opacity: 0;
        transform: matrix3d(0.724, 0, 0, 0, 0, 0.724, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1);
    }

    43.3% {
        opacity: 0;
        transform: matrix3d(0.709, 0, 0, 0, 0, 0.709, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1);
    }

    46.7% {
        opacity: 0;
        transform: matrix3d(0.695, 0, 0, 0, 0, 0.695, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1);
    }

    50% {
        opacity: 0;
        transform: matrix3d(0.68, 0, 0, 0, 0, 0.68, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1);
    }

    53.3% {
        opacity: 0;
        transform: matrix3d(0.665, 0, 0, 0, 0, 0.665, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1);
    }

    56.7% {
        opacity: 0;
        transform: matrix3d(0.651, 0, 0, 0, 0, 0.651, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1);
    }

    60% {
        opacity: 0;
        transform: matrix3d(0.636, 0, 0, 0, 0, 0.636, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1);
    }

    63.3% {
        opacity: 0;
        transform: matrix3d(0.621, 0, 0, 0, 0, 0.621, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1);
    }

    66.7% {
        opacity: 0;
        transform: matrix3d(0.607, 0, 0, 0, 0, 0.607, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1);
    }

    70% {
        opacity: 0;
        transform: matrix3d(0.592, 0, 0, 0, 0, 0.592, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1);
    }

    73.3% {
        opacity: 0;
        transform: matrix3d(0.578, 0, 0, 0, 0, 0.578, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1);
    }

    76.7% {
        opacity: 0;
        transform: matrix3d(0.563, 0, 0, 0, 0, 0.563, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1);
    }

    80% {
        opacity: 0.199;
        transform: matrix3d(0.576, 0, 0, 0, 0, 0.576, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1);
    }

    83.3% {
        opacity: 0.382;
        transform: matrix3d(0.596, 0, 0, 0, 0, 0.596, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1);
    }

    86.7% {
        opacity: 0.545;
        transform: matrix3d(0.616, 0, 0, 0, 0, 0.616, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1);
    }

    90% {
        opacity: 0.702;
        transform: matrix3d(0.636, 0, 0, 0, 0, 0.636, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1);
    }

    93.3% {
        opacity: 0.8;
        transform: matrix3d(0.656, 0, 0, 0, 0, 0.656, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1);
    }

    96.7% {
        opacity: 0.8;
        transform: matrix3d(0.676, 0, 0, 0, 0, 0.676, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1);
    }

    100% {
        opacity: 0.8;
        transform: matrix3d(0.696, 0, 0, 0, 0, 0.696, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1);
    }
}
</style>
