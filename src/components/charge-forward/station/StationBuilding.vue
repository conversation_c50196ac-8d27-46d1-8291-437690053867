<!-- eslint-disable no-underscore-dangle -->
<script lang="ts" setup>
import { useDowngradeLevel } from '@pet/25cny.downgrade-level';
import { useGuideState, vGuide } from '@pet/25cny.guide-directive/index';
import XImage from '@pet/adapt.image/index.vue';
import { debounce, useBarInfo } from '@pet/yau.core';
import { watchImmediate, whenever } from '@vueuse/core';
import { watch } from 'vue';

import TianjinStatic from '@/components/charge-forward/assets/effects/baoZa/assets/baoziStatic.png';
import { useABTest } from '@/hooks/useABTest';
import { useAnimationPause } from '@/models/animationVisibility';
import { useForwardRushBtnModel } from '@/models/forwardRushBtnModel';
import { useForwardRushGridModel } from '@/models/forwardRushGridModel';
import { useSnapShotModel } from '@/models/snapShotModel';
import { useSocialGuide0714Model } from '@/models/socialGuide0714Model';

import GuidePopover from './GuidePopover.vue';
import StationBubble from './StationBubble.vue';
import { BuildingSideEnum } from '../manager/config';
import type { RenderStationItem } from '../manager/config';
import {
    StationBgEffect,
    StationEffectEnum,
    StationHasFoodType,
    StationKeyEnum,
    StationTypeEnum,
} from '../manager/stationConfig';

const { todayIndex, todaySigned, isFirstDay, isSecondDay, showBubbleAndAniAfterSelect } = useForwardRushBtnModel();
const { currentStep } = useSnapShotModel();
const { dayIsToday, dayIsTomorrow, dayIsMoreThanToday, renderMap, firstSigned } = useForwardRushGridModel();
const { currentGuide, showGuideById } = useGuideState();
const { isAnimationPause } = useAnimationPause();
const effectPlay = ref(false);

const { effectShowStatus } = useDowngradeLevel();

const downgrade = computed(() => {
    return !effectShowStatus.value.L1;
});

const props = defineProps<{
    station: RenderStationItem;
}>();

// 天津站是否有奖励
const hasTianjinLlwd = computed(() => !!props.station.stationBubbleText || !!props.station.tomorrowBubbleText);
const style = computed(() => ({
    transform: `translate(${props.station.position.x / 100}rem, ${props.station.position.y / 100}rem`,
    zIndex: props.station.position.zIndex,
}));
const showEffect = ref(false);

/**
 * 今明两天挂载即展示
 */
const hasShow = ref(false);
const guideId = computed(() => `GUIDE_${(props.station.stationDayIndex ?? 0) + ''}_REWARD`);
const { enableEmphasizeLlwd } = useABTest();

// 站点奖励
const stationLlwdInfo = computed(() => {
    let text = '';
    if (dayIsToday(props.station.stationDayIndex)) {
        text = props.station.stationBubbleText ?? '';
    }
    if (dayIsTomorrow(props.station.stationDayIndex) && !enableEmphasizeLlwd.value) {
        text = props.station.tomorrowBubbleText ?? '';
    }
    return {
        text,
        icon: props.station.llrewdIcon ?? '',
        time: props.station.bubbleShowSeconds ?? 10000,
    };
});
const showPop = computed(
    () =>
        (dayIsTomorrow(props.station.stationDayIndex) ||
            (dayIsToday(props.station.stationDayIndex) && !todaySigned.value)) &&
        !!stationLlwdInfo.value.text,
);

/** 显示奖励气泡逻辑 */
const showPopoverFn = () => {
    if (!showPop.value) {
        // eslint-disable-next-line no-underscore-dangle
        currentGuide.value?.id === guideId.value && currentGuide.value?.el?.__destroyGuide?.();
    }

    if (showPop.value && !hasShow.value) {
        // todo 显示引导逻辑（站点在页面上才显示）
        showGuideById(guideId.value, {
            enabled: true,
        });
        hasShow.value = true;
    }
};

// // /** 数据变更时判断是否显示气泡 */
// watch([todayIndex, props], () => {
//     showPopoverFn();
// });

const { inSocialProcess } = useSocialGuide0714Model();
watch(
    () => !inSocialProcess.value,
    (val) => {
        if (val) {
            showPopoverFn();
        }
    },
);

onMounted(() => {
    // 非第一天在挂载的时候播放；第一天: 未签到在弹窗关闭后手动展示
    if ((!isFirstDay.value || (isFirstDay.value && todaySigned.value)) && !inSocialProcess.value) {
        // 挂载后触发显示
        showPopoverFn();
    }
});

// onBeforeUnmount(() => {
//     console.log('current guide', currentGuide.value?.el);
//     currentGuide.value?.el?.__destroyGuide?.();
// });

const showTag = computed(
    () => dayIsToday(props.station.stationDayIndex) || dayIsMoreThanToday(props.station.stationDayIndex),
);
// 今日打卡后展示新的气泡
const ifTodaySigned = computed(() => dayIsToday(props.station.stationDayIndex) && todaySigned.value);

watch(ifTodaySigned, () => {
    currentGuide.value?.id === guideId.value && currentGuide.value?.el?.__destroyGuide?.();
});

/** 点击站点如果有气泡则显示 */
const handleClickStation = debounce(() => {
    if (showPop.value) {
        if (currentGuide.value?.id !== guideId.value) {
            showGuideById(guideId.value, {
                enabled: true,
                important: true,
                nonInterval: 0,
            });
        } else {
            currentGuide.value?.id === guideId.value && currentGuide.value?.el?.__destroyGuide?.();
        }
    }
}, 800);

// 注意，返回的是一个对象，包含loader和其他配置
const StationEffectConfig = computed(() => {
    if (!props.station.uniqueKey || downgrade.value) {
        return null;
    }
    if (props.station.uniqueKey in StationEffectEnum) {
        return StationEffectEnum[props.station.uniqueKey as keyof typeof StationEffectEnum];
    }
});

const StationBg = computed(() => {
    if (!props.station.uniqueKey || downgrade.value) {
        return null;
    }
    if (StationHasFoodType.includes(props.station.uniqueKey as StationKeyEnum)) {
        return StationBgEffect[StationTypeEnum.Food];
    } else {
        return StationBgEffect[StationTypeEnum.Building];
    }
});

/**
 * 每张地图都会有一个特殊站点动效
 * jinghua：天津包子：
 * xile：索菲亚教堂
 */
const StationEffect = shallowRef();

const loadEffect = () => {
    // 如果有限制天，判断当前的day是否满足限制条件，不满足返回
    if (StationEffectConfig.value?.limitDay) {
        if (todayIndex.value && !StationEffectConfig.value.limitDay.includes(todayIndex.value)) {
            return;
        }
    }
    const isTianjin = props.station.stationDayIndex === 2 && props.station.uniqueKey === StationKeyEnum.tianjin;

    // 天津站额外处理一下，第二天签到完成不播放了;第一天没有签到也不展示
    if (
        isTianjin &&
        (!hasTianjinLlwd.value ||
            (hasTianjinLlwd.value &&
                ((!todaySigned.value && isFirstDay.value) || (todaySigned.value && isSecondDay.value))))
    ) {
        return;
    }
    // 如果没有限制条件或者满足限制day & 有动画 & 不降级，异步加载
    if (StationEffectConfig.value?.loader && !downgrade.value) {
        StationEffectConfig.value
            .loader()
            .then((mod) => {
                StationEffect.value = mod.default;
                showEffect.value = true;
            })
            .catch(() => {
                console.error('load effect error');
            });
        return;
    }
    // 否则 重置动画参数
    StationEffect.value = null;
    showEffect.value = false;
    effectPlay.value = false;
};
// 挂载的时候播一次
watchImmediate([StationEffectConfig, downgrade, todayIndex], () => {
    loadEffect();
});
// 【废弃】 第一天签到状态发生变化的时候要再播放一次
// whenever(todaySigned, () => {
//     if (isFirstDay.value && !hasBeginPopup.value) {
//         loadEffect();
//         if (props.station.stationDayIndex === 2) {
//             playSound(SoundType.STATION_COIN);
//         }
//     }
// });

// 站点静态图片
const chessStationIcon = computed(() => {
    // 专为天津站定制的第一天的展示逻辑：已签到 ｜ （未签到 & 弹窗后）
    const showTianjinSpecial =
        (isFirstDay.value && (showBubbleAndAniAfterSelect.value || firstSigned.value)) ||
        (isSecondDay.value && !todaySigned.value);
    if (
        showTianjinSpecial &&
        props.station.stationDayIndex === 2 &&
        hasTianjinLlwd.value &&
        props.station.uniqueKey === StationKeyEnum.tianjin
    ) {
        return TianjinStatic;
    }
    return props.station.chessStationIcon ?? '';
});
const effectEnd = () => {
    showEffect.value = false;
    effectPlay.value = false;
};

// 越过建筑暂停;距离建筑两步再播放
const pause = computed(() => {
    return (
        currentStep.value > props.station.position.step ||
        props.station.position.step - currentStep.value > 2 ||
        isAnimationPause.value
    );
});

const duration = computed(() => {
    return stationLlwdInfo.value.time ?? 10000;
});

const { statusBarHeight } = useBarInfo();

const offset = computed(() => {
    const appEl = document?.getElementById('app')?.getBoundingClientRect();
    return {
        x: (window.innerWidth - (appEl?.width ?? 0)) / 2,
        y: (renderMap.value?.popover?.offset ?? 0) + statusBarHeight,
    };
});

whenever(
    showBubbleAndAniAfterSelect,
    () => {
        if (isFirstDay.value) {
            loadEffect();
            showPopoverFn();
        }
    },
    {
        immediate: true,
    },
);
</script>
<template>
    <div class="station-building" :style="style">
        <div
            v-guide="{
                id: guideId,
                priority: 30,
                // 挂载时不触发，通过函数 showGuideById 触发
                enabled: false,
                component: GuidePopover,
                props: {
                    targetId: station.uniqueKey,
                    icon: stationLlwdInfo.icon,
                    text: stationLlwdInfo.text ?? '',
                    side: station.position.side === BuildingSideEnum.Right ? 'right' : 'left',
                    duration: duration,
                    offset: offset,
                    onClick: handleClickStation,
                },
                offset: {},
                onlyOne: true,
                duration: duration + 60,
                nonInterval: 3000,
                fadeOutDuration: 60,
            }"
            class="station-content"
        >
            <div class="station-wrapper" @click="handleClickStation">
                <!-- 站点图标动效：服务端下发的静态图兜底，静态图兜底 -->
                <template v-if="showEffect">
                    <component
                        :is="StationEffect"
                        class="station-effect"
                        @loaded="effectPlay = true"
                        @ended="effectEnd"
                    />
                </template>
                <template v-else>
                    <!-- 站点氛围 -->
                    <component :is="StationBg" class="station-bg-effect" :pause="pause"></component>
                </template>
                <XImage :src="chessStationIcon" class="station-icon" :class="[effectPlay && 'station-icon-play']" />
                <StationBubble
                    v-if="showTag"
                    :name="station.stationName ?? ''"
                    :day-index="station.stationDayIndex ?? 0"
                />
            </div>
        </div>
    </div>
</template>
<style lang="scss" scoped>
.station-building {
    position: absolute;
    top: -70px;
    left: -76px;
    .station-content {
        width: 150px;
        height: 150px;
        position: absolute;
        .station-wrapper {
            position: absolute;
            width: 100%;
            height: 100%;
        }
        .station-icon {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
        }
        .station-llward {
            position: absolute;
            top: 0;
            left: 0;
            width: 50px;
            height: 50px;
        }
        .station-effect {
            position: absolute;
        }
        .station-bg-effect {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
        }
        .station-icon-play {
            opacity: 0;
        }
    }
}
</style>
