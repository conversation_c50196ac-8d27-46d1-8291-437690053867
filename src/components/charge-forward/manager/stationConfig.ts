const BaoZaConfig = {
    loader: () => import('../assets/effects/baoZa/EffectIndex.vue'),
    limitDay: [1, 2], // 天津金币包子只在第一第二天展示
};
const XianConfig = {
    loader: () => import('../assets/effects/xian/EffectIndex.vue'),
};
const SofiaConfig = {
    loader: () => import('../assets/effects/sofia/EffectIndex.vue'),
};
const QingdaoConfig = {
    loader: () => import('../assets/effects/qingdao/EffectIndex.vue'),
};
const ShanghaiConfig = {
    loader: () => import('../assets/effects/shangHai/EffectIndex.vue'),
};
const LiuzhouConfig = {
    loader: () => import('../assets/effects/liuzhou/EffectIndex.vue'),
};
const ChongQingConfig = {
    loader: () => import('../assets/effects/chongQing/EffectIndex.vue'),
};
const NanPingConfig = {
    loader: () => import('../assets/effects/nanPing/EffectIndex.vue'),
};
const ShenYangConfig = {
    loader: () => import('../assets/effects/shenYang/EffectIndex.vue'),
};
const FoodBgEffect = defineAsyncComponent({
    loader: () => import('../assets/effects/foodBg/EffectIndex.vue'),
    delay: 150,
});
const BuildingBgEffect = defineAsyncComponent({
    loader: () => import('../assets/effects/buildingBg/EffectIndex.vue'),
    delay: 150,
});

// 站点的key
export enum StationKeyEnum {
    // 1-10
    'tianjin' = 'tianjin',
    'beijing' = 'beijing',
    'shijiazhuang' = 'shijiazhuang',
    'baoding' = 'baoding',
    'handan' = 'handan',
    'tangshan' = 'tangshan',
    'cangzhou' = 'cangzhou',
    'langfang' = 'langfang',
    'zhangjiakou' = 'zhangjiakou',
    'qinhuangdao' = 'qinhuangdao',
    // 11-20
    'changchun' = 'changchun',
    'yanbian' = 'yanbian',
    'jian' = 'jian',
    'dalian' = 'dalian',
    'shenyang' = 'shenyang',
    'anshan' = 'anshan',
    'tieling' = 'tieling',
    'mohe' = 'mohe',
    'qiqihaer' = 'qiqihaer',
    'haerbin' = 'haerbin',
    // 21-30
    'xian' = 'xian',
    'huayin' = 'huayin',
    'yanan' = 'yanan',
    'taiyuan' = 'taiyuan',
    'datong' = 'datong',
    'jincheng' = 'jincheng',
    'huhehaote' = 'huhehaote',
    'eerduosi' = 'eerduosi',
    'wulanchabu' = 'wulanchabu',
    'baotou' = 'baotou',
    // 31-40
    'jinan' = 'jinan',
    'qingdao' = 'qingdao',
    'heze' = 'heze',
    'weifang' = 'weifang',
    'zibo' = 'zibo',
    'zhengzhou' = 'zhengzhou',
    'xuchang' = 'xuchang',
    'kaifeng' = 'kaifeng',
    'zhumadian' = 'zhumadian',
    'luoyang' = 'luoyang',
    // 41-50
    'suzhou' = 'suzhou',
    'xuzhou' = 'xuzhou',
    'nanjing' = 'nanjing',
    'shanghai' = 'shanghai',
    'hefei' = 'hefei',
    'hangzhou' = 'hangzhou',
    'quzhou' = 'quzhou',
    'jinyun' = 'jinyun',
    'yiwu' = 'yiwu',
    'jinhua' = 'jinhua',
    // 51-60
    'quanzhou' = 'quanzhou',
    'fuzhou' = 'fuzhou',
    'xiamen' = 'xiamen',
    'putian' = 'putian',
    'shaxian' = 'shaxian',
    'fuding' = 'fuding',
    'zhangzhou' = 'zhangzhou',
    'nanping' = 'nanping',
    'taibei' = 'taibei',
    'nantouxian' = 'nantouxian',
    // 61-70
    'guangzhou' = 'guangzhou',
    'shenzhen' = 'shenzhen',
    'chaozhou' = 'chaozhou',
    'nanning' = 'nanning',
    'guilin' = 'guilin',
    'liuzhou' = 'liuzhou',
    'haikou' = 'haikou',
    'sanya' = 'sanya',
    'xianggang' = 'xianggang',
    'aomen' = 'aomen',
    // 71-80
    'chongqing' = 'chongqing',
    'chengdu' = 'chengdu',
    'leshan' = 'leshan',
    'daochengxian' = 'daochengxian',
    'guiyang' = 'guiyang',
    'rongjiang' = 'rongjiang',
    'kunming' = 'kunming',
    'dali' = 'dali',
    'ruili' = 'ruili',
    'lasa' = 'lasa',
    // 81-90
    'aletai' = 'aletai',
    'kashi' = 'kashi',
    'wulumuqi' = 'wulumuqi',
    'dunhuang' = 'dunhuang',
    'tianshui' = 'tianshui',
    'lanzhou' = 'lanzhou',
    'wuwei' = 'wuwei',
    'jiayuguan' = 'jiayuguan',
    'xining' = 'xining',
    'yinchuan' = 'yinchuan',
    // 91-100
    'zhangjiajie' = 'zhangjiajie',
    'liuyang' = 'liuyang',
    'changsha' = 'changsha',
    'xiangxi' = 'xiangxi',
    'wuhan' = 'wuhan',
    'huanggang' = 'huanggang',
    'shennongjia' = 'shennongjia',
    'jingdezhen' = 'jingdezhen',
    'nanchang' = 'nanchang',
    'wuyuan' = 'wuyuan',
    // bigday
    'bigday-cq' = 'bigday-chongqing-1',
    'bigday-sy' = 'bigday-shenyang-1',
}

export enum StationTypeEnum {
    Food = 'food',
    Building = 'building',
}
export const StationBgEffect = {
    [StationTypeEnum.Food]: FoodBgEffect,
    [StationTypeEnum.Building]: BuildingBgEffect,
};

// 站点key -- 类型：食物
export const StationHasFoodType = [
    StationKeyEnum.tianjin,
    StationKeyEnum.baoding,
    StationKeyEnum.tangshan,
    StationKeyEnum.qiqihaer,
    StationKeyEnum.jinhua,
    StationKeyEnum.shaxian,
    StationKeyEnum.chongqing,
    StationKeyEnum.nanchang,
    StationKeyEnum.chengdu,
    StationKeyEnum.chaozhou,
    StationKeyEnum.liuzhou,
    StationKeyEnum.shaxian,
    StationKeyEnum['bigday-cq'],
];

// 站点key -- 动效
export const StationEffectEnum: {
    [key in StationKeyEnum]?: {
        loader: () => Promise<any>;
        limitDay?: number[];
    };
} = {
    [StationKeyEnum.tianjin]: BaoZaConfig,
    [StationKeyEnum.xian]: XianConfig,
    [StationKeyEnum.haerbin]: SofiaConfig,
    [StationKeyEnum.qingdao]: QingdaoConfig,
    [StationKeyEnum.shanghai]: ShanghaiConfig,
    [StationKeyEnum.liuzhou]: LiuzhouConfig,
    [StationKeyEnum.chongqing]: ChongQingConfig,
    [StationKeyEnum['bigday-cq']]: ChongQingConfig,
    [StationKeyEnum.nanping]: NanPingConfig,
    [StationKeyEnum['bigday-sy']]: ShenYangConfig,
};
