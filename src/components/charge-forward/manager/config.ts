/**
 * 走格子介绍
 * 走格子的抽象方式类似于虎年走格子, 有相机和3d场景的抽象
 * 实际dom渲染位置通过相机和物体在3d场景中的位置计算得到
 * 这样抽象的目的是为了更好的通用性, 之后可以无痛切换渲染方式
 */

import type { Picture } from 'vite-imagetools';

import type { BuildingInfoView, GridInfoView, StationInfoView } from '@/services/open-api-docs/home/<USER>/schemas';

import type { BuildingItemConfig } from './buildingConfig';

// 玩法配置
// IP移动到相邻格子所需的时间, 单位: ms value from ipAnim/config.ts
export const IPBeforeJumpTime = ((78 - 74) * 1000) / 25;
export const IPAfterJumpTime = ((95 - 86) * 1000) / 25;
export const IPMoveTime = ((86 - 82) * 1000) / 25;
export enum IPMoveDirection {
    Forward = 1,
    Backward = -1,
}

export enum GridShape {
    Square = 'square',
    Circle = 'circle',
}
// 地图配置
export type MapConfig = {
    imgs: {
        // url: string;
        width: number;
        height: number;
    };
    offset: number;
    maskHeight: number;
    gridShape: GridShape;
};

// camera配置 camera lookAt [0, 0, 0]
export const CameraWidth = 414;
export const CameraHeight = 896;
export const CameraRotate = (30 / 180) * Math.PI; // 单位: rad

// IP配置
// 移动距离
export const IPConfig = {
    move: {
        x: 80,
        y: 75,
    },
    directionLoop: [-1, -1, 1, 1],

    // 出生点, step 0所处的位置
    birthPos: {
        x: 90,
        y: -340,
    },
    stationAltitude: 150, // 站点高度
    arrivedOffset: {
        x: 50,
        y: -100,
    },
};

// 格子奖励配置
export enum GridllwardType {
    LLCH_GRID = 'LLCH_GRID', // 现金奖励
    LLCN_GRID = 'LLCN_GRID', // 金币奖励
    TASK_GRID = 'TASK_GRID', // 任务挑战格子
    TEAM_GRID = 'TEAM_GRID', // 组队任务
}
export type GridllwardConfig = {
    [key in GridllwardType]?: GridInfoView;
};

// 建筑配置
export enum BuildingType {
    Decoration = 'decoration',
    Game = 'game',
    Guide = 'guide',
}
export enum BuildingName {
    Business = 'business',
}

export enum BuildingSideEnum {
    Left = -1,
    Right = 1,
}

/**
 * type
 */
export type BuildingConfig = {
    data: BuildingItemConfig[];
    interval: number;
};

// 站点配置
export type StationConfig = Array<
    StationInfoView & {
        step: number;
    }
>;

// 2d渲染信息
export type RenderllwardItem = GridInfoView & {
    type: GridllwardType;
    position: {
        zIndex: number;
        step: number;
        x: number;
        y: number;
    }[];
};
export type RenderllwardType = Array<RenderllwardItem>;

export type RenderBuildingItem = BuildingItemConfig & {
    position: {
        pos: number;
        x: number;
        y: number;
        side: number;
        zIndex: number;
    }[];
};

export type RenderBuildingType = Array<RenderBuildingItem>;

export type RenderStationItem = StationInfoView & {
    position: {
        zIndex: number;
        step: number;
        side: BuildingSideEnum;
        x: number;
        y: number;
    };
};

// TODO 替换swagger的类型
export type SkinConfig = {
    gridSkinUrl: string;
    gridSkinSponsor: string;
    gridSkinLocation: Array<number>;
};
export type RenderSkinType = Array<{
    step: number;
    x: number;
    y: number;
}>;
export type RenderStationType = Array<RenderStationItem>;
export type RenderMapType = {
    background: {
        offset: number;
    };
    popover: {
        offset: number;
    };
    grids: RenderllwardType;
    buildings: RenderBuildingType;
    stations: RenderStationType;
    skins: RenderSkinType;
    ip: {
        onStation: boolean;
        onBirth: boolean;
        llwardGridName: keyof typeof GridllwardType | null;
        position: {
            x: number;
            y: number;
        };
        iconUrl?: string;
    };
    currentGrid: {
        position: {
            x: number;
            y: number;
        };
        shape: GridShape;
        step: number;
    };
};
