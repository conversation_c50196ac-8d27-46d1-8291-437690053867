<script lang="ts" setup>
import { useDowngradeLevel } from '@pet/25cny.downgrade-level';
import Sheet from '@pet/adapt.sheet/index.vue';
import PictureImg from '@pet/quantum.PictureImg';
import { decodeText } from '@pet/yau.core/text/replaceText';
import { onMounted } from 'vue';

import headerDowngradeImg from './assets/header-img.png?preset=modern';
import headerImg from './assets/sheet-banner.png?preset=modern';

const CartoonEffect = defineAsyncComponent(() => import('@effect/effect_8478/EffectIndex.vue'));
const FlowersAnim = defineAsyncComponent(() => import('../flowers-anim/FlowersAnim.vue'));

const props = defineProps<{
    title?: string;
    subTitle?: string;
}>();

const emit = defineEmits<{
    (e: 'close', p: { type: string }): void;
    (e: 'after-leave'): void;
    (e: 'after-enter'): void;
    (e: 'on-sub-title-click'): void;
}>();

const { effectShowStatus } = useDowngradeLevel();

const show = defineModel('show', { type: Boolean, default: false });

const slots = defineSlots<{
    default?: ((props: {}) => any) | undefined;
    top?: ((props: {}) => any) | undefined;
    header?: ((props: {}) => any) | undefined;
    title?: ((props: {}) => any) | undefined;
    subTitle?: ((props: {}) => any) | undefined;
}>();

const close = (type = 'close') => {
    emit('close', { type });
    show.value = false;
};

const decodeTitle = computed(() => {
    return decodeText(props.title ?? '');
});

const decodeSubTitle = computed(() => {
    return decodeText(props.subTitle ?? '');
});
const isDefault = computed(() => {
    return slots.title;
});

onMounted(() => {
    show.value = true;
});
</script>
<template>
    <Sheet
        class="common-sheet"
        :show="show"
        ani-type="drawer-bottom"
        :enter-duration="233"
        @close="close"
        @after-enter="$emit('after-enter')"
        @after-leave="$emit('after-leave')"
        @mask-close="close('mask')"
    >
        <template #top>
            <!-- 弹层的标题slot -->
            <slot name="top"></slot>
        </template>
        <template #header>
            <div class="inner-banner">
                <template v-if="effectShowStatus.L1">
                    <PictureImg class="header-img" :src="headerImg" />
                    <CartoonEffect class="cartoon-effect" />
                    <FlowersAnim class="flower-anim" />
                </template>
                <PictureImg v-else class="header-img" :src="headerDowngradeImg" />
            </div>
            <div class="header">
                <div v-if="title || slots.title" class="sheet-title" :class="{ default: isDefault }">
                    <template v-if="slots.title">
                        <slot name="title"></slot>
                    </template>
                    <template v-else>
                        <span class="normal">{{ decodeTitle.prefix }}</span
                        ><span class="high-light">{{ decodeTitle.highlight }}</span
                        ><span class="remain">{{ decodeTitle.surfix }}</span>
                    </template>
                </div>
                <div v-if="subTitle || slots.subTitle" class="sheet-sub-title" @click="emit('on-sub-title-click')">
                    <template v-if="slots.subTitle">
                        <slot name="subTitle"></slot>
                    </template>
                    <template v-else>
                        {{ decodeSubTitle.prefix }}<span class="high-light">{{ decodeSubTitle.highlight }}</span
                        >{{ decodeSubTitle.surfix }}
                    </template>
                </div>
                <slot name="header"></slot>
            </div>
        </template>

        <div class="sheet-content">
            <div>
                <slot></slot>
            </div>
        </div>
    </Sheet>
</template>
<style lang="scss" scoped>
.common-sheet {
    --adapt-sheet-screen-background: transparent;
    --adapt-sheet-lay-cartoon: '';
    --adapt-sheet-cover-background: '';
    --adapt-sheet-title-transform: matrix(1, 0, 0, 1, 0, 0);

    .inner-banner {
        width: 414px;
        height: 293px;
        position: absolute;
        top: -66px;
        background: url('./assets/sheet-banner-bg.png') no-repeat center/100%;
        z-index: 0;

        .flower-anim,
        .header-img,
        .cartoon-effect {
            position: absolute;
            top: 0;
        }

        .header-img {
            z-index: 2;
        }

        .cartoon-effect {
            left: 1px;
            top: 15px;
            z-index: 1;
        }

        .flower-anim {
            left: 86px;
            top: 74px;
            z-index: 3;
        }
    }

    .header {
        position: relative;
        width: 100%;
        height: 150px;

        .sheet-title {
            position: absolute;
            top: 70px;
            left: 24px;
            font-size: 26px;
            font-family: KuaiYuanHuiTi;
            &.default {
                background: linear-gradient(271deg, #000 0.92%, #4e010a 76.82%, #ab2132 100.26%);
                background-clip: text;
                -webkit-background-clip: text;
                -webkit-text-fill-color: transparent;
            }
            .normal {
                background: linear-gradient(271deg, #000 0.92%, #4e010a 76.82%, #ab2132 100.26%);
                background-clip: text;
                -webkit-background-clip: text;
                -webkit-text-fill-color: transparent;
            }
            .remain {
                background: #000;
                background-clip: text;
                -webkit-background-clip: text;
                -webkit-text-fill-color: transparent;
            }
        }

        .sheet-sub-title {
            position: absolute;
            top: 112px;
            left: 24px;
            font-size: 13px;
            color: #c98888;
            max-width: 366px;
        }

        .high-light {
            background: #ff0101;
            background-clip: text;
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }

        .header-slot {
            position: absolute;
            left: 0;
            top: 0;
        }
    }

    .sheet-content {
        position: relative;
        padding: 0 16px;
        overflow: hidden;
        min-height: 640px;
    }

    :deep(.sheet-close) {
        z-index: 10;
    }

    :deep(.sheet-inner::after) {
        content: '';
        position: absolute;
        bottom: 0;
        left: 0;
        width: 100%;
        height: 80%;
        background-color: #fff6f4;
        z-index: -1; /* 确保不会遮挡内容 */
    }

    :deep(.sheet-close-inside) {
        position: relative;
        z-index: 1;
    }
    :deep(.sheet-titles) {
        position: relative;
        z-index: 1;
    }
}
</style>
