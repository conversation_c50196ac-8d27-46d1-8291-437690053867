<!-- eslint-disable sonarjs/no-duplicate-string -->
<script setup lang="ts">
import { LevelEnum, useDowngradeLevel } from '@pet/25cny.downgrade-level';
import Button from '@pet/adapt.button/index.vue';
import TaskComponentsProvide from '@pet/adapt.task-queue/TaskComponentsProvide.vue';

import CommonModal from '@/components/common-modals/CommonModal.vue';
import FullModal from '@/components/full-modals/FullModal.vue';
import FullTransModal from '@/components/full-modals/FullTransModal.vue';
import BannerPopup from '@/components/popups/banner-popup/BannerPopup.vue';
import BannerText from '@/components/popups/banner-popup/BannerText.vue';
import FriendsTeamPopup from '@/components/popups/friends-team-popup/FriendsTeamPopup.vue';
import { TaskType, usePopupModel } from '@/models/popup.model';
import { useShareModel } from '@/models/shareModel';
import type { SummerWarmupInPushView } from '@/services/open-api-docs/home/<USER>/schemas';
// import { getCityPopupData } from '@/utils/popupTransform/cityFullModalTransform';
import { getFinalAwdPreModalData } from '@/utils/popupTransform/fullModalTransform';
import type { SummerPopup } from '@/utils/popupTransform/types';

import { signedNormalPopup, signedFinalPopup, cityPopup, cityFailPopup } from './testData';

const { openSummerPopup, openInpush, openPopup, createPopupTask, createSummerPopup, addGroupTask } = usePopupModel();

const { switchEffectLevel } = useDowngradeLevel();
switchEffectLevel(LevelEnum.L0);

const { shareSign, shareSignFinish } = useShareModel();

// 测试好友卡片数据
const testFriendCards: any[] = [
    {
        title: '快邀请ta组队打卡吧',

        desc: '组队送直通卡,使用后当天一键打卡',

        userId: 2198683589,

        avatar: 'https://kcdn-w1.staging.kuaishou.com/uhead/AB/2025/01/21/14/BMjAyNTAxMjExNDUyNDZfMjE5ODY4MzU4OV8yX2hkNTc3XzY2Nw==_s.jpg',

        nickName: '倪螟',

        commonFriendCount: 1,

        properties: {
            GENDER: 'F',
            XZ: '水瓶座',
            IP: '广东',
        },
    },
    {
        title: '快邀请ta组队打卡吧',

        desc: '组队送直通卡,使用后当天一键打卡',

        userId: 2198138184,

        avatar: 'https://alimov2.a.yximgs.com/kos/nlav12689/head.jpg',

        nickName: '日常沉淀',

        commonFriendCount: 0,

        properties: {
            GENDER: 'F',
        },
    },
    {
        title: '快邀请ta组队打卡吧',

        desc: '组队送直通卡,使用后当天一键打卡',

        userId: 2198620971,

        avatar: 'https://kcdn.staging.kuaishou.com/uhead/AB/2025/02/20/19/BMjAyNTAyMjAxOTU0NTlfMjE5ODYyMDk3MV8yX2hkMjI5XzM1NQ==_s.jpg',

        nickName: '乐类嗜',

        commonFriendCount: 0,

        properties: {
            GENDER: 'U',
        },
    },
    {
        title: '快邀请ta组队打卡吧',

        desc: '组队送直通卡,使用后当天一键打卡',

        userId: 2198648940,

        avatar: 'https://kcdn-w1.staging.kuaishou.com/uhead/AB/2025/07/22/17/BMjAyNTA3MjIxNzU2MTNfMjE5ODY0ODk0MF8yX2hkOTEzXzIyNQ==_s.jpg',

        nickName: '酆奎',

        commonFriendCount: 0,

        properties: {
            GENDER: 'U',
        },
    },
    {
        title: '快邀请ta组队打卡吧',

        desc: '组队送直通卡,使用后当天一键打卡',

        userId: 2198663061,

        avatar: 'https://kcdn-w1.staging.kuaishou.com/uhead/AB/2024/12/31/15/BMjAyNDEyMzExNTI0MTRfMjE5ODY2MzA2MV8yX2hkOTQyXzkzMA==_s.jpg',

        nickName: '殷刘阵',

        commonFriendCount: 0,

        properties: {
            GENDER: 'U',
        },
    },
    {
        title: '快邀请ta组队打卡吧',

        desc: '组队送直通卡,使用后当天一键打卡',

        userId: 2198662635,

        avatar: 'https://kcdn-w1.staging.kuaishou.com/uhead/AB/2025/06/13/15/BMjAyNTA2MTMxNTA4MDFfMjE5ODY2MjYzNV8yX2hkNzM3XzI2Ng==_s.jpg',

        nickName: '殷眨',

        commonFriendCount: 0,

        properties: {
            GENDER: 'U',
        },
    },
    {
        title: '快邀请ta组队打卡吧',

        desc: '组队送直通卡,使用后当天一键打卡',

        userId: 2198901355,

        avatar: 'https://alimov2.a.yximgs.com/kos/nlav12689/head.jpg',

        nickName: '快手用户1754385809360',

        commonFriendCount: 0,

        properties: {
            GENDER: 'M',

            XZ: '水瓶座',

            IP: '广东',
        },
    },
    {
        title: '快邀请ta组队打卡吧',

        desc: '组队送直通卡,使用后当天一键打卡',

        userId: 2198629232,

        avatar: 'https://kcdn.staging.kuaishou.com/uhead/AB/2025/07/11/16/BMjAyNTA3MTExNjE5MDBfMjE5ODYyOTIzMl8yX2hkMzQ5XzIyNw==_s.jpg',

        nickName: '李珠',

        commonFriendCount: 0,

        properties: {
            GENDER: 'U',
        },
    },
    {
        title: '快邀请ta组队打卡吧',

        desc: '组队送直通卡,使用后当天一键打卡',

        userId: 2198682022,

        avatar: 'https://kcdn.staging.kuaishou.com/uhead/AB/2025/03/21/11/BMjAyNTAzMjExMTA0MTRfMjE5ODY4MjAyMl8yX2hkOTAzXzM2Mw==_s.jpg',

        nickName: '唐烷碗',

        commonFriendCount: 0,

        properties: {
            GENDER: 'U',
        },
    },
    {
        title: '快邀请ta组队打卡吧',

        desc: '组队送直通卡,使用后当天一键打卡',

        userId: 2198493807,

        avatar: 'https://kcdn-w1.staging.kuaishou.com/uhead/AB/2024/11/12/15/BMjAyNDExMTIxNTAyMjdfMjE5ODQ5MzgwN18yX2hkMjIzXzY3Ng==_s.jpg',

        nickName: '子车忠脂',

        commonFriendCount: 0,

        properties: {
            GENDER: 'M',
        },
    },
];

// 测试队友数据
const testTeamMembers = [
    {
        userId: 2001,
        nickName: '小明',
        userAvatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=100&h=100&fit=crop&crop=face',
        todaySign: true,
        currentUser: false,
    },
    {
        userId: 2002,
        nickName: '小红',
        userAvatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b47c?w=100&h=100&fit=crop&crop=face',
        todaySign: false,
        currentUser: true,
    },
];

// 打开组队格子好友组队弹窗
const openFriendsTeamPopup = () => {
    openPopup({
        component: FriendsTeamPopup,
        data: {
            // @ts-expect-error
            popupType: 'TEAM_GRID_RECO_FRIENDS_CARD',
            sponsorLogo: 'https://kcdn-w1.staging.kuaishou.com/kos/nlav10309/feihe.png?x-ks-ptid=0',
            mainButtonText: '喊Ta组队',
            friendCards: testFriendCards,
        },
        options: {
            // 必须填，用来关闭弹窗
            name: TaskType.TEAM_GRID_POPUP,
        },
    });
};

// 现金奖励
const openLS_LLCH_LLREWD = () => {
    openSummerPopup(
        {
            popupType: 'LS_LLCH_LLREWD',
            sponsorLogo:
                'https://kcdn-w1.staging.kuaishou.com/kc/files/a/cny2025-server/warmup/sponsor/kuaishou.png?x-ks-ptid=0',
            sponsorText: '快手',
            title: '恭喜抽中现金大奖',
            titleContext: {
                desc: '',
                userList: null,
                userCount: 0,
            },
            subTitle: '获得现金红包',
            subTitleDesc: '',
            mainButton: {
                linkType: 'CLOSE',
                linkText: '开心收下',
                linkSubText: null,
                linkUrl: null,
                icon: null,
            },
            subButton: null,
            bottomButton: {
                linkType: 'KWAI_LINK',
                linkText: '去查看',
                linkSubText: '已存入「我的钱包」',
                linkUrl:
                    'kwai://kds/react?bundleId=Cny2025RetentionKrn&componentName=wallet&biz=12749&themeStyle=1&entry_src=ks_cny_089',
                icon: null,
            },
            blessing: '',
            icon: null,
            llpeDetail: [
                {
                    llpeType: 'LLCH',
                    amount: 2,
                    displayAmount: '0.02',
                    displayUnit: '元',
                    openSubTitle: null,
                },
                {
                    llpeType: 'AD_VIDEO',
                    amount: 0,
                    displayAmount: '0',
                    displayUnit: '',
                    videoUrl: 'https://kcdn.staging.kuaishou.com/kos/nlav111737/25CNYWarmupRichTree/guanxiaotong.png',
                    videoCdnUrl:
                        'https://kcdn-w1.staging.kuaishou.com/kos/nlav111737/25CNYWarmupRichTree/guanxiaotong.png?x-ks-ptid=0',
                    coverUrl: '',
                },
            ],
            stepLeee: null,
        } as unknown as SummerPopup,
        {
            taskType: TaskType.PASSIVE_POPUP,
        },
    );
};

// 金币奖励
const openLS_LLCN_LLREWD = () => {
    openSummerPopup(
        {
            popupType: 'LS_LLCN_LLREWD',
            sponsorLogo: 'https://kcdn-w1.staging.kuaishou.com/kos/nlav10309/feihe.png?x-ks-ptid=0',
            sponsorText: '飞鹤奶粉',
            title: '恭喜摇出金币奖励',
            titleContext: {
                desc: '',
                userList: null,
                userCount: 0,
            },
            subTitle: '获得金币红包',
            subTitleDesc: '',
            mainButton: {
                linkType: 'LUCK_SHAKE_SUDOKU',
                linkText: '继续摇',
                linkSubText: null,
                linkUrl: null,
                icon: null,
            },
            subButton: {
                linkType: 'CLOSE',
                linkText: '开心收下',
                linkSubText: null,
                linkUrl: null,
                icon: null,
            },
            bottomButton: {
                linkType: 'KWAI_LINK',
                linkText: '去查看',
                linkSubText: '已存入「我的钱包」',
                linkUrl:
                    'kwai://kds/react?bundleId=Cny2025RetentionKrn&componentName=wallet&biz=12749&themeStyle=1&entry_src=ks_cny_089',
                icon: null,
            },
            blessing: '祝你蛇年行大运 财源滚滚',
            icon: null,
            llpeDetail: [
                {
                    llpeType: 'LLCN',
                    amount: 1000,
                    displayAmount: '1000',
                    displayUnit: '金币',
                    bottomDesc: '折合人民币0.1元',
                },
                {
                    llpeType: 'COUPON',
                    amount: 0,
                    displayAmount: '0',
                    displayUnit: '',
                    couponView: {
                        displayType: 'number',
                        branding: false,
                        addressInfo: null,
                        name: '平台券',
                        icon: '',
                        desc: '全平台可用（除少数商品外）',
                        forwardUrl: '',
                        forwardText: '去使用',
                        status: 'unused',
                        value: 100,
                        valueText: '1',
                        valueDesc: '满10元可用',
                        expiredTime: 1734346377567,
                        validTime: 0,
                        recordId: 16949748,
                        prizeId: 42278,
                        prizeType: 'KWAI_SHOP_MARKETING',
                        shopCouponId: '37199144529',
                        count: 1,
                        cdKey: null,
                        extraParam: null,
                    },
                    showUnusedButton: false,
                },
            ],
            stepLeee: null,
        } as unknown as SummerPopup,
        {
            taskType: TaskType.PASSIVE_POPUP,
        },
    );
};

// 金币关注
const openLS_FOLLOW_TASK_LLCN = () => {
    openSummerPopup(
        {
            popupType: 'LS_FOLLOW_TASK_LLCN',
            sponsorLogo: 'https://kcdn.staging.kuaishou.com/kos/nlav10309/feihe.png?x-ks-ptid=0',
            sponsorText: '飞鹤奶粉',
            title: '王大陆Staging',
            titleContext: {
                desc: '',
                userList: null,
                userCount: 0,
            },
            subTitle: '完成后，获得【这个能用】金币 100',
            subTitleDesc: '',
            mainButton: {
                linkType: 'COMMON_TASK',
                linkText: '去关注',
                linkSubText: null,
                linkUrl: null,
                icon: null,
            },
            subButton: null,
            bottomButton: null,
            blessing: '',
            icon: null,
            llpeDetail: [
                {
                    llpeType: 'FOLLOW_LLREWD_LLCN_TASK',
                    amount: 100,
                    displayAmount: '100',
                    displayUnit: '金币',
                    authorId: 2196592495,
                    authorName: '王大陆Staging',
                    authorHeadUrl:
                        'https://kcdn.staging.kuaishou.com/uhead/AB/2022/12/23/22/BMjAyMjEyMjMyMjA3MDVfMjE5NjU5MjQ5NV8xX2hkMTk0XzA=.jpg?x-ks-ptid=0',
                    taskExtra: {
                        taskId: 11629,
                        title: '王大陆Staging',
                        description: '完成后，获得【这个能用】金币 100',
                        iconUrls: [''],
                        completeConditionAmount: 1,
                        completedAmount: 0,
                        completeMaxTimes: 1,
                        completedTimes: 0,
                        taskStatus: 'COMPLETING_TASK',
                        displayText: '去关注',
                        jumpLink:
                            'kwai://profile/2196592495?nspk2=1&taskToken=a9APcc8-iNuSkHv0pL9RjLdjVAxlvkAuFw6bkLbIvRY&taskId=11629&subBizId=12814',
                        jumpType: 'native',
                        prizeName: '【这个能用】金币',
                        prizeCount: 100,
                        extParams: {
                            followRefer: '260',
                            activityId: 'activity-auto-2cc3163ee',
                        },
                        completeToastText: '',
                        timeLimitedStart: 0,
                        timeLimitedEnd: 0,
                        timeLimitExpireTime: 0,
                        timeLimitedType: 0,
                        takeTime: 1734348050122,
                        delayQueryStatus: false,
                        widgetParam: '',
                        taskShowStyle: null,
                        apkName: '',
                        apkAddr: '',
                        iosStoreAddr: '',
                        iosSchema: '',
                        shareSubBiz: '',
                        unsupportedToast: '现App或系统版本低，请升级重试',
                        taskPriority: 1,
                        taskContentId: '2196592495',
                        taskToken: 'a9APcc8-iNuSkHv0pL9RjLdjVAxlvkAuFw6bkLbIvRY',
                        subBizId: 12814,
                        takeType: 'MANUAL_TAKE',
                        takeStatus: 'TAKED',
                        assistUserInfo: null,
                        relationChainInfo: [],
                        relationChainTypes: null,
                        prizeIconUrls: [],
                        reservationCalendarConfig: {
                            remindPeriod: 0,
                            scheduleStartTimeStamp: 0,
                            scheduleEndTimeStamp: 0,
                            calendarTitle: '',
                            calendarDescription: '',
                            kwaiLink: '',
                            nebulaLink: '',
                            dailyTimeStampList: [],
                        },
                        dynamicExtParam: null,
                        titleCornerText: null,
                        descriptionLabel: null,
                        taskPropertyKey: null,
                        backgroundColor: null,
                        backgroundUrl: null,
                        titleCornerUrl: null,
                        bannerUrl: null,
                        cornerBubbleText: null,
                        pendantIconUrl: null,
                    },
                },
            ],
            stepLeee: null,
        } as unknown as SummerPopup,
        {
            taskType: TaskType.PASSIVE_POPUP,
        },
    );
};

// 现金关注
const openLS_FOLLOW_TASK_LLCH = () => {
    openSummerPopup(
        {
            popupType: 'LS_FOLLOW_TASK_LLCH',
            sponsorLogo: 'https://kcdn.staging.kuaishou.com/kos/nlav10309/feihe.png?x-ks-ptid=0',
            sponsorText: '飞鹤奶粉',
            title: '快手用户1726214758285',
            titleContext: {
                desc: '',
                userList: null,
                userCount: 0,
            },
            subTitle: '完成后，获得现金 100',
            subTitleDesc: '',
            mainButton: {
                linkType: 'COMMON_TASK',
                linkText: '去关注',
                linkSubText: null,
                linkUrl: null,
                icon: null,
            },
            subButton: null,
            bottomButton: null,
            blessing: '',
            icon: null,
            llpeDetail: [
                {
                    llpeType: 'FOLLOW_LLREWD_LLCH_TASK',
                    amount: 100,
                    displayAmount: '1',
                    displayUnit: '元',
                    authorId: 2198474502,
                    authorName: '快手用户1726214758285',
                    authorHeadUrl: 'https://kcdn.staging.kuaishou.com/kos/nlav12689/head.jpg?x-ks-ptid=0',
                    taskExtra: {
                        taskId: 11628,
                        title: '快手用户1726214758285',
                        description: '完成后，获得现金 100',
                        iconUrls: [''],
                        completeConditionAmount: 1,
                        completedAmount: 0,
                        completeMaxTimes: 1,
                        completedTimes: 0,
                        taskStatus: 'COMPLETING_TASK',
                        displayText: '去关注',
                        jumpLink:
                            'kwai://profile/2198474502?nspk2=1&taskToken=o61ueV7f6zylXbhQhRIibZRJq9Q5HzZ0shAS-MkDjHI&taskId=11628&subBizId=12814',
                        jumpType: 'native',
                        prizeName: '现金',
                        prizeCount: 100,
                        extParams: {
                            followRefer: '260',
                            activityId: 'activity-auto-2cc3163ee',
                        },
                        completeToastText: '',
                        timeLimitedStart: 0,
                        timeLimitedEnd: 0,
                        timeLimitExpireTime: 0,
                        timeLimitedType: 0,
                        takeTime: 1734409502078,
                        delayQueryStatus: false,
                        widgetParam: '',
                        taskShowStyle: null,
                        apkName: '',
                        apkAddr: '',
                        iosStoreAddr: '',
                        iosSchema: '',
                        shareSubBiz: '',
                        unsupportedToast: '现App或系统版本低，请升级重试',
                        taskPriority: 1,
                        taskContentId: '2198474502',
                        taskToken: 'o61ueV7f6zylXbhQhRIibZRJq9Q5HzZ0shAS-MkDjHI',
                        subBizId: 12814,
                        takeType: 'MANUAL_TAKE',
                        takeStatus: 'TAKED',
                        assistUserInfo: null,
                        relationChainInfo: [],
                        relationChainTypes: null,
                        prizeIconUrls: [],
                        reservationCalendarConfig: {
                            remindPeriod: 0,
                            scheduleStartTimeStamp: 0,
                            scheduleEndTimeStamp: 0,
                            calendarTitle: '',
                            calendarDescription: '',
                            kwaiLink: '',
                            nebulaLink: '',
                            dailyTimeStampList: [],
                        },
                        dynamicExtParam: {},
                        titleCornerText: '',
                        descriptionLabel: '',
                        taskPropertyKey: '',
                        backgroundColor: '',
                        backgroundUrl: '',
                        titleCornerUrl: '',
                        bannerUrl: '',
                        cornerBubbleText: '',
                        pendantIconUrl: '',
                    },
                },
            ],
            stepLeee: null,
        } as unknown as SummerPopup,
        {
            taskType: TaskType.PASSIVE_POPUP,
        },
    );
};

// 挂件
const openLS_PROFILE_PENDANT_LLREWD = () => {
    openSummerPopup(
        {
            popupType: 'LS_PROFILE_PENDANT_LLREWD',
            sponsorLogo: 'https://kcdn.staging.kuaishou.com/kos/nlav10309/feihe.png?x-ks-ptid=0',
            sponsorText: '飞鹤奶粉',
            title: '恭喜抽中头像挂件',
            titleContext: {
                desc: '',
                userList: null,
                userCount: 0,
            },
            subTitle: '获得1207头像挂件头像挂件',
            subTitleDesc: '佩戴成功可在个人页展示',
            mainButton: {
                linkType: 'KWAI_LINK',
                linkText: '去佩戴',
                linkSubText: null,
                linkUrl:
                    'kwai://kds/react?bundleId=Cny2025RetentionKrn&componentName=wallet&biz=12749&themeStyle=1&entry_src=ks_cny_089',
                icon: null,
            },
            subButton: null,
            bottomButton: {
                linkType: 'KWAI_LINK',
                linkText: '去查看',
                linkSubText: '已存入「我的钱包」',
                linkUrl:
                    'kwai://kds/react?bundleId=Cny2025RetentionKrn&componentName=wallet&biz=12749&themeStyle=1&entry_src=ks_cny_089',
                icon: null,
            },
            blessing: '',
            icon: 'https://kcdn-w1.staging.kuaishou.com/bs2/zt-encourage-prize/encourage-1634289502586-RradvX.png?x-ks-ptid=0',
            llpeDetail: [
                {
                    llpeType: 'PROFILE_PENDANT',
                    amount: 0,
                    displayAmount: '0',
                    displayUnit: '',
                    bottomDesc: null,
                    bottomButton: {
                        linkType: 'KWAI_LINK',
                        linkText: '去查看',
                        linkSubText: '已存入「我的钱包」',
                        linkUrl:
                            'kwai://kds/react?bundleId=Cny2025RetentionKrn&componentName=wallet&biz=12749&themeStyle=1&entry_src=ks_cny_089',
                        icon: null,
                    },
                },
            ],
            stepLeee: null,
        } as unknown as SummerPopup,
        {
            taskType: TaskType.PASSIVE_POPUP,
        },
    );
};

// 卡片弹窗
const openLS_HUG_SIGN_LLREWD = () => {
    openSummerPopup(
        {
            popupType: 'LS_HUG_SIGN_LLREWD',
            sponsorLogo: 'https://kcdn.staging.kuaishou.com/kos/nlav10309/feihe.png?x-ks-ptid=0',
            sponsorText: '飞鹤奶粉',
            title: '签到拿豪礼',
            titleContext: {
                desc: '',
                userList: null,
                userCount: 0,
            },
            subTitle: '每日打卡白拿好礼',
            subTitleDesc: 'iphone15、茅台、黄金等好礼通通白拿',
            mainButton: {
                linkType: 'JUMP_H5',
                linkText: '立即使用',
                linkSubText: null,
                linkUrl: 'https://ug-encourage-activity.staging.kuaishou.com/huge-sign-in?layoutType=4',
                icon: null,
            },
            subButton: null,
            bottomButton: null,
            blessing: '',
            icon: 'https://kcdn.staging.kuaishou.com/kos/nlav111479/longsigndoudi.png',
            llpeDetail: [
                {
                    llpeType: 'HUG_SIGN_DIVERSION',
                    amount: 0,
                    displayAmount: '0',
                    displayUnit: '',
                    llrewdDesc: 'iPhone、茅台、大米、鸡蛋等好礼等你来拿！',
                    llrewdName: '',
                },
            ],
            stepLeee: null,
        } as unknown as SummerPopup,
        {
            taskType: TaskType.PASSIVE_POPUP,
        },
    );
};

// 卡片弹窗图片（很遗憾 挑战失败）
const openFail = () => {
    openSummerPopup(
        {
            sponsorLogo: null,
            sponsorText: null,
            popupType: 'HUGE_SIGN_IN_CHALLENGE_FAILURE',
            title: '很遗憾 挑战失败',
            subTitle: '断签天数超过5天',
            userId: 0,
            activityId: null,
            mainButton: {
                linkType: 'NEW_ROUND_SIGN',
                linkText: '我知道了',
                linkSubText: null,
                linkUrl: null,
                icon: null,
            },
            subButton: null,
            taskId: 0,
            sortScore: 0,
            hashKeys: [],
            desc: null,
            icon: null,
        } as unknown as SummerPopup,
        {
            taskType: TaskType.PASSIVE_POPUP,
            config: {
                ext: {
                    noPlayShowSound: true,
                },
            },
        },
    );
};

// 卡片弹窗图片（很遗憾 奖品已过期）
const openExpired = () => {
    openPopup({
        component: CommonModal,
        data: {
            subTitle: '很遗憾 奖品已过期',
            desc: '别灰心，再来一次你还行!',
            icon: 'https://ali.a.yximgs.com/kos/nlav12119/XmJWRDpZ_2025-05-07-14-36-20.png',
            mainButton: '我知道了',
            type: 'dialog',
            btnClick: (popup) => {
                popup?.destroy();
            },
        },
        options: {
            ext: { noPlayShowSound: true },
        },
    });
};

// 卡片弹窗图片（选中奖品二次确认）
const openConfirmPrize = () => {
    openPopup({
        component: CommonModal,
        data: {
            subTitle: '确定选择这个吗?',
            desc: '后期可以再次更换豪礼哦',
            icon: 'https://ali.a.yximgs.com/kos/nlav12119/XmJWRDpZ_2025-05-07-14-36-20.png',
            mainButton: '确定选择',
            subButton: '再想想',
            type: 'dialog',
            btnClick: (popup) => {
                popup?.destroy();
            },
        },
        options: {
            ext: {
                noPlayShowSound: true,
            },
        },
    });
};

// 卡片弹窗纯文案（队伍解散）
const openFailTeam = () => {
    openPopup({
        component: CommonModal,
        data: {
            sponsorLogo: 'https://kcdn.staging.kuaishou.com/kos/nlav10309/feihe.png?x-ks-ptid=0',
            subTitle: '队伍解散',
            message: '你已退队，队伍解散\n别担心，你的个人打卡天数不受影响！',
            mainButton: '我知道了',
            type: 'dialog',
            btnClick: (popup) => {
                popup?.destroy();
            },
        },
    });
};

// 卡片弹窗双按钮（确定离开小队吗？）
const openLeaveTeam = () => {
    openPopup({
        component: CommonModal,
        data: {
            sponsorLogo: 'https://kcdn.staging.kuaishou.com/kos/nlav10309/feihe.png?x-ks-ptid=0',
            subTitle: '恭喜获得7张直通卡',
            desc: '使用直通卡，当日可直接打卡成功使用直通卡，当日可直接打卡成功',
            icon: 'https://bdsk.a.yximgs.com/kos/nlav11066/hcj/test.png',
            mainButton: '开心收下',
            // subButton: '退出',
            type: 'dialog',
            btnClick: (popup) => {
                popup?.destroy();
            },
        },
    });
};
const showInpush = () => {
    openInpush({
        inpushType: 'UNKNOWN',
        title: 'string',
        content: 'string',
        assistedUserInfo: [
            {
                userName: 'string',
                headUrl: 'string',
            },
        ],
        iconUrl: 'https://p2.a.yximgs.com/uhead/AB/2020/09/03/12/BMjAyMDA5MDMxMjUyMzFfOTAwNDFfMl9oZDI1OF83NzE=_s.jpg',
        buttonView: {
            linkType: 'UNKNOWN',
            linkText: 'string',
            linkSubText: 'string',
            linkUrl: 'string',
            icon: 'string',
        },
        needRefreshPopup: true,
    } as unknown as SummerWarmupInPushView);
};

const openGroupPopup = () => {
    // 创建通用弹窗任务
    const commonPopup = createPopupTask({
        component: CommonModal,
        data: {
            subTitle: '确定离开小队吗？',
            desc: '马上就能获得7张直通卡了哦',
            icon: 'https://ali.a.yximgs.com/kos/nlav12119/jTyADRou_2025-05-07-15-43-46.png',
            mainButton: '再想想',
            subButton: '退出',
            type: 'dialog',
            btnClick: (popup) => {
                popup?.destroy();
            },
        },
    });
    // 创建奖励弹窗任务
    const summerPopup = createSummerPopup(
        {
            popupType: 'LS_PROFILE_PENDANT_LLREWD',
            sponsorLogo: 'https://kcdn.staging.kuaishou.com/kos/nlav10309/feihe.png?x-ks-ptid=0',
            sponsorText: '飞鹤奶粉',
            title: '恭喜抽中头像挂件',
            titleContext: {
                desc: '',
                userList: null,
                userCount: 0,
            },
            subTitle: '获得1207头像挂件头像挂件',
            subTitleDesc: '佩戴成功可在个人页展示',
            mainButton: {
                linkType: 'KWAI_LINK',
                linkText: '去佩戴',
                linkSubText: null,
                linkUrl:
                    'kwai://kds/react?bundleId=Cny2025RetentionKrn&componentName=wallet&biz=12749&themeStyle=1&entry_src=ks_cny_089',
                icon: null,
            },
            bottomButton: {
                linkType: 'KWAI_LINK',
                linkText: '去查看',
                linkSubText: '已存入「我的钱包」',
                linkUrl:
                    'kwai://kds/react?bundleId=Cny2025RetentionKrn&componentName=wallet&biz=12749&themeStyle=1&entry_src=ks_cny_089',
                icon: null,
            },
            blessing: '',
            icon: 'https://kcdn-w1.staging.kuaishou.com/bs2/zt-encourage-prize/encourage-1634289502586-RradvX.png?x-ks-ptid=0',
            llpeDetail: [
                {
                    llpeType: 'PROFILE_PENDANT',
                    amount: 0,
                    displayAmount: '0',
                    displayUnit: '',
                    bottomDesc: null,
                    bottomButton: {
                        linkType: 'KWAI_LINK',
                        linkText: '去查看',
                        linkSubText: '已存入「我的钱包」',
                        linkUrl:
                            'kwai://kds/react?bundleId=Cny2025RetentionKrn&componentName=wallet&biz=12749&themeStyle=1&entry_src=ks_cny_089',
                        icon: null,
                    },
                },
            ],
            stepLeee: null,
        } as unknown as SummerPopup,
        {
            taskType: TaskType.PASSIVE_POPUP,
        },
    );

    // 添加组任务到队列
    const group = addGroupTask({
        options: {
            name: 'RED_DRAW',
        },
        // 可以塞很多弹窗，会根据优先级展示（如果有配置的话）
        tasks: [commonPopup /* summerPopup */],
    });

    // 请求的情况可以在commonPopup结束前添加子任务
    commonPopup?.end?.then(() => {
        summerPopup && group?.addSubTask(summerPopup);
    });
};

const openGroupPopupV2 = () => {
    // 出sheet弹窗
    const sheetPopup = openPopup({
        component: CommonModal,
        data: {
            subTitle: 'sheet',
            desc: '马上就能获得7张直通卡了哦',
            icon: 'https://ali.a.yximgs.com/kos/nlav12119/jTyADRou_2025-05-07-15-43-46.png',
            mainButton: '再想想',
            subButton: '退出',
            type: 'dialog',
            btnClick: (popup) => {
                // 创建通用弹窗任务
                const commonPopup = openPopup({
                    component: CommonModal,
                    data: {
                        subTitle: 'popup',
                        desc: '马上就能获得7张直通卡了哦',
                        icon: 'https://ali.a.yximgs.com/kos/nlav12119/jTyADRou_2025-05-07-15-43-46.png',
                        mainButton: '再想想',
                        subButton: '退出',
                        type: 'dialog',
                        btnClick: (popup) => {
                            popup?.destroy();
                        },
                    },
                });

                // commonPopup结束后，sheetPopup会被销毁
                commonPopup.end?.then(() => {
                    sheetPopup.triggerDestroy();
                });
            },
        },
        options: {
            type: 'SHEET',
            // 设置 sheet tag，不是popup就不会互斥
            queueTags: ['sheet'],
        },
    });
};

const openSignedPopupFinal = () => {
    // 最终大奖过渡弹窗
    const data = getFinalAwdPreModalData(signedFinalPopup);
    const commonPopup = createPopupTask({
        component: FullTransModal,
        data,
    });
    // 最终日打卡弹窗
    const summerPopup = createSummerPopup(signedFinalPopup, {
        taskType: TaskType.ACTIVE_POPUP,
    });
    // 添加组任务到队列
    const group = addGroupTask({
        options: {
            name: 'RED_DRAW',
        },
        tasks: [commonPopup],
    });

    // 请求的情况可以在commonPopup结束前添加子任务
    commonPopup?.end?.then(() => {
        summerPopup && group?.addSubTask(summerPopup);
    });
};

const openSignedPopupTrans = () => {
    const data = getFinalAwdPreModalData(signedFinalPopup);
    openPopup({
        component: FullTransModal,
        data,
    });
};

const openCityPopup = () => {
    openSummerPopup(cityPopup, {
        taskType: TaskType.ACTIVE_POPUP,
    });
};

const openCityFailPopup = () => {
    openSummerPopup(cityFailPopup, {
        taskType: TaskType.PASSIVE_POPUP,
    });
};

const openSignedPopupNum = (num: number) => {
    const data = {
        ...(num === 100 ? signedFinalPopup : signedNormalPopup),
        continueSignInDays: num,
    };
    openSummerPopup(data as unknown as SummerPopup, {
        taskType: TaskType.ACTIVE_POPUP,
    });
};

const openBannerPopup = () => {
    openPopup({
        component: BannerPopup,
        data: {
            rightText: '太棒了\n今日直达当日站点！',
        },
    });
};

const openBannerTextPopup = () => {
    openPopup({
        component: BannerText,
        data: {
            text: '太棒了\n今日直达当日站点！',
        },
    });
};

// 飞入钱包的奖励弹窗
const openPrizePopup = () => {
    // openSummerPopup(
    //     {
    //         // popupType: 'LS_LLCN_LLREWD',
    //         sponsorLogo:
    //             'https://p2-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/sponsor/kuaishouLogo.png?x-kcdn-pid=112543',
    //         sponsorText: '快手',
    //         title: '恭喜获得站点奖励',
    //         // titleContext: {
    //         //     desc: '',
    //         //     userCount: 0,
    //         // },
    //         subTitle: '开红包啦',
    //         subTitleDesc: '',
    //         mainButton: {
    //             linkType: 'CLOSE',
    //             linkText: '开心收下',
    //             linkSubText: null,
    //             linkUrl: null,
    //             icon: null,
    //         },
    //         subButton: null,
    //         bottomButton: {
    //             linkType: 'KWAI_LINK',
    //             linkText: '去查看',
    //             linkSubText: '已存入「我的钱包」',
    //             linkUrl:
    //                 'kwai://kds/react?bundleId=Cny2025RetentionKrn&componentName=wallet&biz=8147&themeStyle=1&entry_src=ks_2025sum_090',
    //             icon: null,
    //         },
    //         blessing: '红包一开，笑口常开',
    //         icon: null,
    //         showInTaskList: false,
    //         llpeDetail: [
    //             // {
    //             //     amount: 666,
    //             //     displayAmount: '666',
    //             //     displayUnit: '金币',
    //             //     bottomDesc: null,
    //             // },
    //         ],
    //     },
    //     {},
    // );
};

const openRecruitPopup = () => {
    openSummerPopup(
        {
            popupType: 'LS_RECRUIT_QUICK_CARD_TRIAL',
            sponsorLogo: '快手快聘',
            sponsorText: '快手快聘',
            title: '恭喜获得快招卡试用',
            titleContext: {
                desc: '',
                userList: null,
                userCount: 0,
            },
            subTitle: '限时免费试用7天',
            subTitleDesc: '帮你获得更多求职者简历',
            mainButton: {
                linkType: 'JUMP_H5',
                linkText: '立即使用',
                linkSubText: null,
                linkUrl:
                    'kwai://kds/react?bundleId=RecruitPayment&componentName=RecruitPayment&screen=VipPage&themeStyle=1&minBundleVersion=1163&sceneSource=CNY_2025&entrySource=MAIN_PLACE_CARD',
                icon: null,
            },
            subButton: null,
            bottomButton: {
                linkType: 'KWAI_LINK',
                linkText: '去查看',
                linkSubText: '已存入「我的钱包」',
                linkUrl:
                    'kwai://kds/react?bundleId=Cny2025RetentionKrn&componentName=wallet&biz=12749&themeStyle=1&entry_src=ks_cny_089',
                icon: null,
            },
            blessing: '',
            icon: 'https://p5-pro.kskwai.com/kcdn/cdn-kcdn112307/recruit/cny25_recruit_member_free_trail.png',
            llpeDetail: [
                {
                    llpeType: 'RECRUIT_DIVERSION_CAMPUS_TRIAL_CARD',
                    amount: 0,
                    displayAmount: '0',
                    displayUnit: '',
                    bottomDesc: null,
                },
            ],
            stepLeee: null,
        } as unknown as SummerPopup,
        {
            taskType: TaskType.PASSIVE_POPUP,
        },
    );
};
</script>
<template>
    <TaskComponentsProvide>
        <div class="wrapper">
            <div>红包全皮</div>
            <Button @click="openLS_LLCH_LLREWD">现金奖励</Button>
            <Button @click="openLS_LLCN_LLREWD">金币奖励</Button>
            <div>红包无封面</div>
            <Button @click="openLS_PROFILE_PENDANT_LLREWD">挂件奖励</Button>
            <div>红包封面</div>
            <Button @click="openLS_FOLLOW_TASK_LLCN">现金奖励</Button>
            <Button @click="openLS_FOLLOW_TASK_LLCH">金币奖励</Button>
            <div>普通卡片弹窗</div>
            <Button @click="openLS_HUG_SIGN_LLREWD">长签拿好礼</Button>
            <Button @click="openFail">很遗憾，挑战失败</Button>
            <Button @click="openExpired">很遗憾，奖品已过期</Button>
            <Button @click="openConfirmPrize">选中奖品二次确认</Button>
            <Button @click="openFailTeam">队伍解散</Button>
            <Button @click="openLeaveTeam">确定离开小队吗？</Button>
            <Button @click="openRecruitPopup">快招卡试用</Button>
            <div>inpush</div>
            <Button @click="showInpush">inpush</Button>

            <div>组合弹窗</div>
            <Button @click="openGroupPopup">先弹通用再弹奖励</Button>
            <Button @click="openGroupPopupV2">先弹sheet再弹奖励盖在上面</Button>
            <Button @click="openSignedPopupFinal">最终大奖_先弹过渡弹窗再弹打卡弹窗</Button>

            <div>打卡成功全屏弹窗</div>
            <Button @click="openSignedPopupTrans">最终大奖_过渡弹窗</Button>
            <Button @click="() => openSignedPopupNum(2)">2</Button>
            <Button @click="() => openSignedPopupNum(9)">9</Button>
            <Button @click="() => openSignedPopupNum(10)">10</Button>
            <Button @click="() => openSignedPopupNum(13)">13</Button>
            <Button @click="() => openSignedPopupNum(99)">99</Button>
            <Button @click="() => openSignedPopupNum(100)">100</Button>

            <div>横幅弹窗</div>
            <Button @click="openBannerPopup">横幅</Button>
            <Button @click="openBannerTextPopup">花字</Button>

            <div>分享</div>
            <Button @click="() => shareSign(12)">分享打卡天数</Button>
            <Button
                @click="
                    () =>
                        shareSignFinish(
                            'https://kcdn.staging.kuaishou.com/kos/nlav111737/25CNYWarmupRichTree/guanxiaotong.png',
                            'Switch OLED',
                        )
                "
                >分享奖品图</Button
            >
            <div>奖励弹窗飞入</div>
            <Button @click="openPrizePopup">分享打卡天数</Button>
            <div>好友组队弹窗</div>
            <Button @click="openFriendsTeamPopup">组队格子弹窗</Button>
            <div>城市二级页弹窗</div>
            <Button @click="openCityPopup">城市二级页弹窗</Button>
            <Button @click="openCityFailPopup">城市二级页失败弹窗</Button>
        </div>
    </TaskComponentsProvide>
</template>
<style lang="scss" scoped>
.wrapper {
    height: 100vh;
    overflow: scroll;

    // 写 css 减少冲突
    :deep(.btn-wrapper) {
        min-height: auto;
        min-width: 80px;
        height: 38px;
        font-size: 13px;
        margin: 0 5px 5px 0;
    }
}
</style>
