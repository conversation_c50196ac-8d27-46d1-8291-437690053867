import type {
    HugeSignInLLawdFollowPopupView,
    LuckRushSudokuView,
} from '@/services/open-api-docs/home/<USER>/schemas';

const url =
    'kwai://kds/react?bundleId=Cny2025RetentionKrn&componentName=wallet&biz=12749&themeStyle=1&entry_src=ks_cny_089';
const sponsorLogo = 'https://ali.a.yximgs.com/kos/nlav12119/iZwJmwnX_2025-05-24-12-02-43.png';
const sponsorText = '';
const video = 'https://kcdn.staging.kuaishou.com/kos/nlav111737/25CNYWarmupRichTree/guanxiaotong.png';
const finalAward =
    'https://p5-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/product/online_v1/1018-changlong.png?x-kcdn-pid=112543';
const signInStationList = [
    {
        stationName: '北京',
        stationIcon:
            'https://p4-plat.wskwai.com/kcdn/cdn-kcdn112543/wishTravel/sign-in/citylight/beijing_light.png?x-kcdn-pid=112543',
        stationBlackIcon:
            'https://p4-plat.wskwai.com/kcdn/cdn-kcdn112543/wishTravel/sign-in/citydark/beijing_dark.png?x-kcdn-pid=112543',
    },
    {
        stationName: '天津',
        stationIcon:
            'https://p4-plat.wskwai.com/kcdn/cdn-kcdn112543/wishTravel/sign-in/citylight/tianjin_light.png?x-kcdn-pid=112543',
        stationBlackIcon:
            'https://p4-plat.wskwai.com/kcdn/cdn-kcdn112543/wishTravel/sign-in/citydark/tianjin_dark.png?x-kcdn-pid=112543',
    },
    {
        stationName: '石家庄',
        stationIcon:
            'https://p4-plat.wskwai.com/kcdn/cdn-kcdn112543/wishTravel/sign-in/citylight/shijiazhuang_light.png?x-kcdn-pid=112543',
        stationBlackIcon:
            'https://p4-plat.wskwai.com/kcdn/cdn-kcdn112543/wishTravel/sign-in/citydark/shijiazhuang_dark.png?x-kcdn-pid=112543',
    },
    {
        stationName: '保定',
        stationIcon:
            'https://p4-plat.wskwai.com/kcdn/cdn-kcdn112543/wishTravel/sign-in/citylight/baoding_light.png?x-kcdn-pid=112543',
        stationBlackIcon:
            'https://p4-plat.wskwai.com/kcdn/cdn-kcdn112543/wishTravel/sign-in/citydark/baoding_dark.png?x-kcdn-pid=112543',
    },
    {
        stationName: '邯郸',
        stationIcon:
            'https://p4-plat.wskwai.com/kcdn/cdn-kcdn112543/wishTravel/sign-in/citylight/handan_light.png?x-kcdn-pid=112543',
        stationBlackIcon:
            'https://p4-plat.wskwai.com/kcdn/cdn-kcdn112543/wishTravel/sign-in/citydark/handan_dark.png?x-kcdn-pid=112543',
    },
    {
        stationName: '唐山',
        stationIcon:
            'https://p4-plat.wskwai.com/kcdn/cdn-kcdn112543/wishTravel/sign-in/citylight/tangshan_light.png?x-kcdn-pid=112543',
        stationBlackIcon:
            'https://p4-plat.wskwai.com/kcdn/cdn-kcdn112543/wishTravel/sign-in/citydark/tangshan_dark.png?x-kcdn-pid=112543',
    },
    {
        stationName: '沧州',
        stationIcon:
            'https://p4-plat.wskwai.com/kcdn/cdn-kcdn112543/wishTravel/sign-in/citylight/cangzhou_light.png?x-kcdn-pid=112543',
        stationBlackIcon:
            'https://p4-plat.wskwai.com/kcdn/cdn-kcdn112543/wishTravel/sign-in/citydark/cangzhou_dark.png?x-kcdn-pid=112543',
    },
];

export const signedFinalPopup = {
    popupType: 'HUGE_SIGN_IN_LLAWD',
    sponsorLogo,
    sponsorText,
    title: '登顶百日打卡成就',
    subTitle: '你就是王者！',
    continueSignInDays: 10,
    signInStationList,
    todayIndex: 6,
    llawdInfo: {
        amount: 0,
        llawdText: '恭喜你拿到豪礼！',
        llawdSubText: '这么强必须得炫耀一下！',
        llawdDesc: null,
        llawdIcon: finalAward,
        llawdName: '长隆欢乐世界标准票',
        productId: 100,
    },
    canGetLLawdDay: 0,
    hasStationLLawd: false,
    mainButton: {
        linkType: 'FINAL_LLRWD',
        linkText: '立即领取',
        linkSubText: '10天后失效',
        linkUrl: null,
        icon: null,
    },
    subButton: {
        linkType: 'SHARE',
        linkText: '炫耀一下',
        linkSubText: null,
        linkUrl: null,
        icon: null,
    },
} as unknown as HugeSignInLLawdFollowPopupView;

export const signedNormalPopup = {
    popupType: 'HUGE_SIGN_IN_LLAWD',
    sponsorLogo,
    sponsorText,
    title: '今日打卡成功',
    subTitle: '已连续打卡',
    continueSignInDays: 2,
    signInStationList,
    todayIndex: 1,
    llawdInfo: {
        amount: 100,
        llawdText: '今日额外白拿<span>168</span>金币奖励',
        llawdSubText: '距离豪礼还要8天！',
        llawdDesc: '已放入钱包',
        llawdIcon: 'https://kcdn.staging.kuaishou.com/bs2/zt-encourage-prize/encourage-1634289502586-RradvX.png',
        llawdName: '现金',
        productId: 171,
        couponId: 10,
    },
    canGetLLawdDay: 8,
    hasStationLLawd: true,
    mainButton: {
        linkType: 'CLOSE',
        linkText: '我知道了',
        linkSubText: null,
        linkUrl: null,
        icon: null,
    },
    subButton: {
        linkType: 'SHARE',
        linkText: '炫耀一下',
        linkSubText: null,
        linkUrl: null,
        icon: null,
    },
} as unknown as HugeSignInLLawdFollowPopupView;

export const cityPopup = {
    popupType: 'LS_ATTRACTION_TICKETS',
    sponsorLogo,
    sponsorText,
    title: '太幸运了!!\n恭喜抽到新加坡免费游',
    subTitle: '',
    // icon: 'https://p4-plat.wskwai.com/kos/nlav111422/25-summer-share-officer/city_ticket.png',
    icon: 'https://p5-plat.wskwai.com/kos/nlav111422/25-summer-share-officer/city_popup.png',
    mainButton: {
        linkType: 'CLOSE',
        linkText: '开心收下',
        linkSubText: null,
        linkUrl: null,
        icon: null,
    },
    llpeDetail: [
        {
            bottomDesc: '奈雪20-18优惠券',
            llpeType: 'UNKNOWN',
            amount: 0,
            displayAmount: 'string',
            displayUnit: 'string',
        },
    ],
} as unknown as LuckRushSudokuView;

export const cityFailPopup = {
    popupType: 'LS_CITY_PAGE_BLESS',
    sponsorLogo,
    sponsorText,
    title: '哎呀 与大奖失之交臂',
    subTitle: '别灰心 下次一定中奖',
    mainButton: {
        linkType: 'CLOSE',
        linkText: '我知道了',
        linkSubText: null,
        linkUrl: null,
        icon: null,
    },
    llpeDetail: [
        {
            llpeDetail: [
                {
                    llpeType: 'CITY_PAGE_BLESS',
                    amount: 0,
                    displayAmount: '0',
                    displayUnit: '',
                    bottomDesc: '',
                },
            ],
        },
    ],
} as unknown as LuckRushSudokuView;
