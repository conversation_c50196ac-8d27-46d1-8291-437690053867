// :::[gundam router-top]:::
import { createRouter, createWebHistory, type RouteRecordRaw } from 'vue-router';

import { loggerUtils, inLiveHalf } from '@/init/logger';
import CityView from '@/modules/city/CityView.vue';
import HomeView from '@/modules/home/<USER>';

import { ROUTE_NAME } from './name';

const routes: RouteRecordRaw[] = [
    {
        path: '/',
        name: ROUTE_NAME.HOME,
        component: HomeView,
    },
    {
        path: '/home',
        name: ROUTE_NAME.SSG,
        component: HomeView,
    },
    {
        path: '/city',
        name: ROUTE_NAME.CITY,
        component: CityView,
    },
    {
        path: '/error',
        name: ROUTE_NAME.ERROR,
        component: () => import('@pet/adapt.error-handler/error-page.vue'),
    },
];

if (import.meta.env.DEV) {
    const devRoutes: RouteRecordRaw[] = [
        // 弹窗队列测试页
        {
            path: '/test-queue',
            name: 'test-queue',
            component: () => import('@/modules/test/test-queue.vue'),
        },
        // 弹窗测试页
        {
            path: '/test-popup',
            name: 'test-popup',
            component: () => import('@/modules/test/test-popup.vue'),
        },
        // 引导测试页
        {
            path: '/test-guide',
            component: () => import('@/modules/test/test-guide.vue'),
        },
        // 弹窗走查页（todo：集成所有弹窗）
        {
            path: '/test-popup-list',
            name: 'test-popup-list',
            component: () => import('@/modules/test/test-popup-list.vue'),
        },
        // 组队测试页
        {
            path: '/test-team',
            name: 'test-team',
            component: () => import('@/modules/test/test-team.vue'),
        },
        {
            path: '/test-lsq',
            component: () => import('@/modules/test/TestLsq/TestTask.vue'),
        },
        {
            path: '/work',
            component: () => import('@/modules/test/test-work.vue'),
        },
        {
            path: '/test-tv',
            component: () => import('@/modules/test/test-tv.vue'),
        },
        {
            path: '/test-cloud',
            component: () => import('@/modules/test/test-cloud.vue'),
        },
    ];
    routes.push(...devRoutes);
}

const router = createRouter({
    // 业务如果有Path前缀，需要在这里配置。修改 / 为你的kfx中的path
    history: createWebHistory('/'),
    routes,
});
// 由于home页首次上报PV时机在main.ts, 在路由跳转做上报兼容处理
router.beforeEach((to, from, next) => {
    if (from.name === ROUTE_NAME.CITY && to.name === ROUTE_NAME.SSG) {
        loggerUtils.weblog.sendImmediately('PV', {
            type: 'enter',
            page: 'OP_ACTIVITY_SUM2025_MAIN',
            params: {
                url: window.location.href,
                entry_src: to.query?.entry_src ?? '',
                activity_name: 'SUMMER2025',
            },
            coPage: inLiveHalf,
        });
    }
    next();
});

router.afterEach((to, from) => {
    if (from.name === ROUTE_NAME.SSG && to.name === ROUTE_NAME.CITY) {
        loggerUtils.weblog.sendImmediately('PV', {
            type: 'enter',
            page: 'OP_ACTIVITY_SUM2025_MAIN_COUNTRY',
            params: {
                url: window.location.href,
                entry_src: 'main_page',
                activity_name: 'SUMMER2025',
                day_index: to.query?.dayIndex ?? 0,
            },
            coPage: inLiveHalf,
        });
    }
});

// :::[gundam router-bottom]:::

export default router;
