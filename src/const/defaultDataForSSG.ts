import { ref } from 'vue';

import type { KconfFEConfig } from '@/models/configModel';
import { type HomeView } from '@/services/open-api-docs/home/<USER>/schemas';

export const homeFEConstantsConfig: { frameConfig: KconfFEConfig['frameConfig'] } = {
    // 页面设置
    frameConfig: {
        themeMap: {
            // 兜底主题，应对key没对应上的情况
            default: {
                // 主题色，状态栏颜色
                themeColor: '#f85c3e',
                // 头部背景遮罩主颜色，第一个颜色需要和主题色一致，不然会有断层
                headerBackMaskBackground: 'linear-gradient(180deg, #f85c3e 0%, #ff7840 20%, #fba169 62%, #fff0 100%)',
                // 底部背景遮罩颜色
                footerBackMaskBackground: 'linear-gradient(180deg, #fff0 0%, #ffdcbfe6 42%, #ffefd1 100%)',
                backgroundColor: '#FFDCBFE5',
            },
            jinghua: {
                themeColor: '#f85c3e',
                headerBackMaskBackground: 'linear-gradient(180deg, #f85c3e 0%, #ff7840 20%, #fba169 62%, #fff0 100%)',
                footerBackMaskBackground: 'linear-gradient(180deg, #fff0 0%, #ffdcbfe6 42%, #ffefd1 100%)',
                backgroundColor: '#FFDCBFE5',
            },
            xile: {
                themeColor: '#188BFF',
                headerBackMaskBackground: 'linear-gradient(180deg, #188BFF 0%, #57A7F7 20%, #98CBFF 62%, #fff0 100%)',
                footerBackMaskBackground: 'linear-gradient(180deg, #fff0 0%, #d4eaffe6 42%, #C7E4FF 100%)',
                backgroundColor: '#D4EAFFE5',
            },
            furao: {
                themeColor: '#BCC619',
                headerBackMaskBackground:
                    'linear-gradient(179.33deg, #BCC619 0%, #CDD823 35.56%, #E2EA5E 67.82%, rgba(245, 255, 177, 0) 99.42%)',
                footerBackMaskBackground:
                    'linear-gradient(180deg, rgba(221, 216, 144, 0) 0.78%, rgba(253, 249, 200, 0.9) 42.63%, #EAE497 100%)',
                backgroundColor: '#EAE497',
            },
            fengshou: {
                themeColor: '#FFAA12',
                headerBackMaskBackground:
                    'linear-gradient(179.33deg, #FFAA12 0%, #FFBF4E 35.56%, #FFC257 67.82%, rgba(255, 207, 122, 0) 99.42%)',
                footerBackMaskBackground:
                    'linear-gradient(180deg, rgba(255, 223, 166, 0) 0.78%, rgba(255, 223, 166, 0.9) 42.63%, #FFDFA6 100%)',
                backgroundColor: '#FFDFA6',
            },
            jiangnan: {
                backgroundColor: '#BBFFF4',
                footerBackMaskBackground:
                    'linear-gradient(180deg, rgba(187, 255, 244, 0) 0.78%, rgba(187, 255, 244, 0.9) 42.63%, #BBFFF4 100%)',
                headerBackMaskBackground:
                    'linear-gradient(179.33deg, #0AD3C4 -7.01%, #08D5C4 19.83%, #08D5C4 67.82%, rgba(8, 213, 196, 0) 99.42%)',
                themeColor: '#0AD3C4',
            },
            yangfan: {
                backgroundColor: '#8EF1FF',
                footerBackMaskBackground:
                    'linear-gradient(180deg, rgba(142, 241, 255, 0) 0.78%, rgba(142, 241, 255, 0.9) 42.63%, #8EF1FF 100%)',
                headerBackMaskBackground:
                    'linear-gradient(179.33deg, #00CBFC -7.01%, #00CBFC 19.83%, #00CBFC 67.82%, rgba(0, 203, 252, 0) 99.42%)',
                themeColor: '#00CBFC',
            },
            duocai: {
                backgroundColor: '#CCF1A9',
                footerBackMaskBackground:
                    'linear-gradient(180deg, rgba(204, 241, 169, 0) 0.78%, rgba(204, 241, 169, 0.9) 42.63%, #CCF1A9 100%)',
                headerBackMaskBackground:
                    'linear-gradient(179.33deg, #55DA56 0%, #42D443 19.83%, #85E686 67.82%, rgba(130, 220, 131, 0) 99.42%)',
                themeColor: '#55DA56',
            },
            sichou: {
                themeColor: '#FFA765',
                headerBackMaskBackground:
                    'linear-gradient(179.33deg, #FFA765 0%, #FFA765 19.83%, #FFA868 67.82%, rgba(247, 179, 129, 0) 99.42%)',
                footerBackMaskBackground:
                    'linear-gradient(180deg, rgba(255, 211, 176, 0) 0.78%, rgba(255, 211, 176, 0.9) 42.63%, #FFD3B0 100%)',
                backgroundColor: '#FF8C36',
            },
            jingchu: {
                themeColor: '#FF849D',
                headerBackMaskBackground:
                    'linear-gradient(179.33deg, #FF849D 0%, #FF849D 19.83%, #FF9AAE 67.82%, rgba(255, 154, 174, 0) 99.42%)',
                footerBackMaskBackground:
                    'linear-gradient(180deg, rgba(255, 215, 220, 0) 0.78%, rgba(255, 215, 220, 0.9) 42.63%, #FFD7DC 100%)',
                backgroundColor: '#FF5C7C',
            },
            rela: {
                themeColor: '#FF995E',
                headerBackMaskBackground:
                    'linear-gradient(179.33deg, #FF995E 0%, #FF995E 19.83%, #FFBF9A 67.82%, rgba(255, 191, 154, 0) 99.42%)',
                footerBackMaskBackground:
                    'linear-gradient(180deg, rgba(255, 211, 186, 0) 0.78%, rgba(255, 211, 186, 0.9) 42.63%, #FFD3BA 100%)',
                backgroundColor: '#FF7F34',
            },
            'theme-shenyang': {
                themeColor: '#0093FC',
                headerBackMaskBackground:
                    'linear-gradient(179.33deg, #0093FC 0%, #0093FC 19.83%, #0093FC 67.82%, rgba(0, 147, 252, 0) 99.42%)',
                footerBackMaskBackground:
                    'linear-gradient(180deg, rgba(131, 203, 255, 0) 0.78%, rgba(131, 203, 255, 0.9) 42.63%, #83CBFF 100%)',
                backgroundColor: '#0093FC',
            },
        },
    },
};

export const RawDataDefault = ref<HomeView>({
    todaySigned: false,
    chessboard: {
        progress: {
            currentStep: 0,
            expectTotalStep: 1,
            signed: false,
        },
        initStationCount: 5,
    },
    mainButton: {},
    titleInfo: {},
    accountModel: {},
    popList: [],
    needSig3Path: [],
});
