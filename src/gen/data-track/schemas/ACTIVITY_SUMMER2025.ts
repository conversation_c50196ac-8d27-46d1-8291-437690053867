
export type CLICK_EVENT__OP_ACTIVITY_SUM2025_MAIN__OP_ACTIVITY_MAKE_TEAM_CARD = {
  /**
   * 社交组队卡片
   */
  action: "OP_ACTIVITY_MAKE_TEAM_CARD",
  params: {
    /**
     * 好友id；
     */
    friend_id?: string,
    /**
     * 区分点击的区域
     */
    click_area?: "change" | "invite" | "close"
  }
}

export type PAGE_SHOW_EVENT__OP_ACTIVITY_SUM2025_MAIN = {
  /**
   * 2025暑期活动主会场页面
   */
  page: "OP_ACTIVITY_SUM2025_MAIN",
  params: {
    /**
     * 活动名称，本次为SUMMER2025
     */
    activity_name?: "SUMMER2025",
    /**
     * 页面链接
     */
    url?: string,
    /**
     * 区分活动页来源
     */
    entry_src?: "ks_2025sum_001" | "ks_2025sum_002" | "ks_2025sum_003" | "ks_2025sum_004" | "ks_2025sum_005" | "ks_2025sum_006" | "ks_2025sum_007" | "ks_2025sum_008" | "ks_2025sum_009" | "ks_2025sum_010" | "ks_2025sum_011" | "ks_2025sum_012" | "ks_2025sum_013" | "ks_2025sum_014" | "ks_2025sum_015" | "ks_2025sum_016" | "ks_2025sum_017" | "ks_2025sum_018" | "ks_2025sum_019" | "ks_2025sum_020" | "ks_2025sum_021" | "ks_2025sum_022" | "ks_2025sum_023" | "ks_2025sum_024" | "ks_2025sum_025" | "ks_2025sum_026" | "ks_2025sum_027" | "ks_2025sum_028" | "ks_2025sum_029" | "ks_2025sum_030" | "ks_2025sum_031" | "ks_2025sum_032" | "ks_2025sum_033" | "ks_2025sum_034" | "ks_2025sum_035" | "ks_2025sum_036" | "ks_2025sum_037" | "ks_2025sum_038" | "ks_2025sum_039" | "ks_2025sum_040" | "ks_2025sum_041" | "ks_2025sum_042" | "ks_2025sum_043" | "ks_2025sum_044" | "ks_2025sum_045" | "ks_2025sum_046" | "ks_2025sum_047" | "ks_2025sum_048" | "ks_2025sum_049" | "ks_2025sum_050" | "ks_2025sum_051" | "ks_2025sum_052" | "ks_2025sum_053" | "ks_2025sum_054",
    /**
     * 状态
     */
    status?: "1" | "0" | "-1"
  }
}

export type ELEMENT_SHOW_EVENT__OP_ACTIVITY_SUM2025_MAIN__OP_ACTIVITY_MAKE_TEAM_CARD = {
  /**
   * 社交组队卡片
   */
  action: "OP_ACTIVITY_MAKE_TEAM_CARD",
  params: {
    /**
     * 好友id
     */
    friend_id?: string
  }
}

export type ELEMENT_SHOW_EVENT__OP_ACTIVITY_SUM2025_MAIN__OP_ACTIVITY_MAKE_TEAM_ICON = {
  /**
   * 组队格子icon
   */
  action: "OP_ACTIVITY_MAKE_TEAM_ICON",
  params: {}
}

export type CLICK_EVENT__OP_ACTIVITY_SUM2025_MAIN__OP_ACTIVITY_MAKE_TEAM_GRID_POP = {
  /**
   * 格子上的组队任务弹窗
   */
  action: "OP_ACTIVITY_MAKE_TEAM_GRID_POP",
  params: {
    /**
     * 点击的区域
     */
    click_area?: "invite" | "close"
  }
}

export type ELEMENT_SHOW_EVENT__OP_ACTIVITY_SUM2025_MAIN__OP_ACTIVITY_MAKE_TEAM_GRID_POP = {
  /**
   * 格子上的组队任务弹窗
   */
  action: "OP_ACTIVITY_MAKE_TEAM_GRID_POP",
  params: {}
}

export type CLICK_EVENT__OP_ACTIVITY_SUM2025_MAIN__OP_ACTIVITY_MAKE_TEAM_POP = {
  /**
   * 邀请组队弹窗
   */
  action: "OP_ACTIVITY_MAKE_TEAM_POP",
  params: {
    /**
     * 目前组队状态
     */
    team_status?: "1" | "2" | "3",
    /**
     * 区分图中点击的位置，主button上报文案，其他位置上报如下枚举值，如立即邀请
     */
    click_area?: "invite+" | "head" | "change" | "exit"
  }
}

export type ELEMENT_SHOW_EVENT__OP_ACTIVITY_SUM2025_MAIN__OP_ACTIVITY_MAKE_TEAM_POP = {
  /**
   * 邀请组队弹窗
   */
  action: "OP_ACTIVITY_MAKE_TEAM_POP",
  params: {
    /**
     * 目前组队状态
     */
    team_status?: "1" | "2" | "3"
  }
}

export type CLICK_EVENT__OP_ACTIVITY_SUM2025_MAIN__OP_ACTIVITY_MAKE_TEAM_GUID = {
  /**
   * 组队引导蒙层
   */
  action: "OP_ACTIVITY_MAKE_TEAM_GUID",
  params: {}
}

export type ELEMENT_SHOW_EVENT__OP_ACTIVITY_SUM2025_MAIN__OP_ACTIVITY_MAKE_TEAM_GUID = {
  /**
   * 组队引导蒙层
   */
  action: "OP_ACTIVITY_MAKE_TEAM_GUID",
  params: {}
}

export type CLICK_EVENT__OP_ACTIVITY_SUM2025_MAIN__OP_ACTIVITY_REWARD_POP = {
  /**
   * 主会场奖励弹窗
   */
  action: "OP_ACTIVITY_REWARD_POP",
  params: {
    /**
     * 奖励类型
     */
    encourage_type?: "UNKNOWN" | "DEFAULT_BLESS" | "DEFAULT_LLCN" | "LUCK_RUSH_CHANCE" | "LLCN" | "LLCH" | "COUPON" | "LLWDW_CARD" | "LLCH_TRANSFER_CARD" | "PROFILE_PENDANT" | "COMMON_ZT_TASK_LLCN" | "COMMON_ZT_TASK_LLCH" | "FOLLOW_LLREWD_LLCN_TASK" | "FOLLOW_LLREWD_LLCH_TASK" | "FOLLOW_LLREWD_BLESS_TASK" | "FOLLOW_LLREWD_RUSH_TASK" | "WATCH_LIVE_LLREWD_LLCH_TASK" | "WATCH_LIVE_LLREWD_LLCN_TASK" | "WATCH_LIVE_LLREWD_RUSH_TASK" | "TIME_LIMITED_ASSIST_DOUBLE_LLCN_TASK" | "TIME_LIMITED_ASSIST_DOUBLE_LLCH_TASK" | "TIME_LIMITED_COUNT_LLCN_TASK" | "TIME_LIMITED_COUNT_LLCH_TASK" | "TIME_LIMITED_INVITE_LLCN_TASK" | "TIME_LIMITED_INVITE_LLCH_TASK" | "TIME_LIMITED_INVITE_REFLUX_LLCN_TASK" | "TIME_LIMITED_INVITE_REFLUX_LLCH_TASK" | "TIME_LIMITED_PK_TASK" | "TEAM_TASK" | "AD_VIDEO" | "AD_PHOTO" | "FISSION_GOAT_DIVERSATION" | "PHYSICAL_PRODUCT" | "AD_STATIC_DIVERSION_POPUP" | "ALWAYS_PULL_TASK" | "INVOKE_APP_LLREWD_LLCN" | "INVOKE_APP_LLREWD_LLCH" | "INVOKE_APP_LLREWD_SHAKE",
    /**
     * 弹窗类型
     */
    popup_type?: "TIME_ASSIST_TASK_SUCCESS" | "TIME_ASSIST_RETURN_TASK_SUCCESS" | "TIME_LIMIT_TASK_SUCCESS" | "ASSIST_DOUBLE_LLCH_TASK_SUCCESS" | "ASSIST_DOUBLE_LLCN_TASK_SUCCESS" | " LS_LLCH_LLREWD" | "LS_LLCN_LLREWD" | "LS_COUPON_LLREWD" | " LS_WITH_DRAW_CARD_LLREWD" | "LS_LLCH_TRANSFER_CARD_LLREWD" | "LS_PROFILE_PENDANT_LLREWD" | " LS_DEFAULT_BLESS" | " LS_DEFAULT_LLCN" | "LS_AD_PHOTO" | " LS_AD_VIDEO",
    /**
     * 品牌名称
     */
    brand_name?: string,
    /**
     * 弹窗标题
     */
    title?: string,
    /**
     * 按钮文案
     */
    button_name?: string,
    /**
     * 包含所有券的实例id
     */
    coupon_id?: string,
    /**
     * 商业化id，包含图片、视频等，实际上报为url区分
     */
    photo_id?: string,
    /**
     * task_id:区分inpush和日历
     */
    task_id?: string
  }
}

export type ELEMENT_SHOW_EVENT__OP_ACTIVITY_SUM2025_MAIN__OP_ACTIVITY_REWARD_POP = {
  /**
   * 主会场奖励弹窗
   */
  action: "OP_ACTIVITY_REWARD_POP",
  params: {
    /**
     * 奖励类型
     */
    encourage_type?: "UNKNOWN" | "DEFAULT_BLESS" | "DEFAULT_LLCN" | "LUCK_RUSH_CHANCE" | "LLCN" | "LLCH" | "COUPON" | "LLWDW_CARD" | "LLCH_TRANSFER_CARD" | "PROFILE_PENDANT" | "COMMON_ZT_TASK_LLCN" | "COMMON_ZT_TASK_LLCH" | "FOLLOW_LLREWD_LLCN_TASK" | "FOLLOW_LLREWD_LLCH_TASK" | "FOLLOW_LLREWD_BLESS_TASK" | "FOLLOW_LLREWD_RUSH_TASK" | "WATCH_LIVE_LLREWD_LLCH_TASK" | "WATCH_LIVE_LLREWD_LLCN_TASK" | "WATCH_LIVE_LLREWD_RUSH_TASK" | "TIME_LIMITED_ASSIST_DOUBLE_LLCN_TASK" | "TIME_LIMITED_ASSIST_DOUBLE_LLCH_TASK" | "TIME_LIMITED_COUNT_LLCN_TASK" | "TIME_LIMITED_COUNT_LLCH_TASK" | "TIME_LIMITED_INVITE_LLCN_TASK" | "TIME_LIMITED_INVITE_LLCH_TASK" | "TIME_LIMITED_INVITE_REFLUX_LLCN_TASK" | "TIME_LIMITED_INVITE_REFLUX_LLCH_TASK" | "TIME_LIMITED_PK_TASK" | "TEAM_TASK" | "AD_VIDEO" | "AD_PHOTO" | "FISSION_GOAT_DIVERSATION" | "PHYSICAL_PRODUCT" | "AD_STATIC_DIVERSION_POPUP" | "ALWAYS_PULL_TASK" | "INVOKE_APP_LLREWD_LLCN" | "INVOKE_APP_LLREWD_LLCH" | "INVOKE_APP_LLREWD_SHAKE",
    /**
     * 弹窗类型
     */
    popup_type?: "TIME_ASSIST_TASK_SUCCESS" | "TIME_ASSIST_RETURN_TASK_SUCCESS" | "TIME_LIMIT_TASK_SUCCESS" | "ASSIST_DOUBLE_LLCH_TASK_SUCCESS" | "ASSIST_DOUBLE_LLCN_TASK_SUCCESS" | " LS_LLCH_LLREWD" | "LS_LLCN_LLREWD" | "LS_COUPON_LLREWD" | " LS_WITH_DRAW_CARD_LLREWD" | "LS_LLCH_TRANSFER_CARD_LLREWD" | "LS_PROFILE_PENDANT_LLREWD" | " LS_DEFAULT_BLESS" | " LS_DEFAULT_LLCN" | "LS_AD_PHOTO" | " LS_AD_VIDEO",
    /**
     * 品牌名称
     */
    brand_name?: string,
    /**
     * 弹窗标题
     */
    title?: string,
    /**
     * 主按钮文案（默认高亮的button）
     */
    button_name?: string,
    /**
     * 包含所有券的实例id
     */
    coupon_id?: string,
    /**
     * 商业化id，包含图片、视频等，实际上报为url区分
     */
    photo_id?: string
  }
}

export type CLICK_EVENT__OP_ACTIVITY_SUM2025_MAIN__OP_ACTIVITY_JION_BUTTON = {
  /**
   * 主页面参与button
   */
  action: "OP_ACTIVITY_JION_BUTTON",
  params: {
    /**
     * 按钮文案，上报按钮的文案，如"向前冲"等
     */
    title?: string,
    /**
     * 剩余步数，上报进入页面按钮曝光时的剩余步数，0、1、2·····
     */
    steps?: string
  }
}

export type ELEMENT_SHOW_EVENT__OP_ACTIVITY_SUM2025_MAIN__OP_ACTIVITY_JION_BUTTON = {
  /**
   * 主页面参与button
   */
  action: "OP_ACTIVITY_JION_BUTTON",
  params: {
    /**
     * 按钮文案，上报按钮的文案，如"向前冲"等
     */
    title?: string,
    /**
     * 剩余步数，上报进入页面按钮曝光时的剩余步数，0、1、2·····
     */
    steps?: string
  }
}

export type CLICK_EVENT__OP_ACTIVITY_SUM2025_MAIN__OP_ACTIVITY_REWARD_CHOOSE_POP = {
  /**
   * 主会场礼物选择弹窗
   */
  action: "OP_ACTIVITY_REWARD_CHOOSE_POP",
  params: {
    /**
     * 弹窗来源，区分是打卡完成掉落还是用户主动点击
     */
    source?: "choose" | "sign" | "newer_guid",
    /**
     * 上报点击的按钮名称，如"换这个“”就选这个“
     */
    button_name?: string,
    /**
     * 点击的按钮对应的商品id
     */
    good_id?: string
  }
}

export type ELEMENT_SHOW_EVENT__OP_ACTIVITY_SUM2025_MAIN__OP_ACTIVITY_REWARD_CHOOSE_POP = {
  /**
   * 主会场礼物选择弹窗
   */
  action: "OP_ACTIVITY_REWARD_CHOOSE_POP",
  params: {
    /**
     * 弹窗来源，区分是打卡完成掉落还是用户主动点击
     */
    source?: "choose" | "sign" | "calender_pop" | "newer_guid"
  }
}

export type ELEMENT_SHOW_EVENT__OP_ACTIVITY_SUM2025_MAIN__OP_ACTIVITY_TASK_RESULT_POP__FORWARD = {
  /**
   * 主会场返回结果弹窗封皮
   */
  action: "OP_ACTIVITY_TASK_RESULT_POP__FORWARD",
  params: {
    /**
     * 弹窗类型
     */
    popup_type?: "TIME_ASSIST_TASK_SUCCESS" | "TIME_ASSIST_RETURN_TASK_SUCCESS" | "TIME_LIMIT_TASK_SUCCESS" | "ASSIST_DOUBLE_LLCH_TASK_SUCCESS" | "ASSIST_DOUBLE_LLCN_TASK_SUCCESS" | " LS_LLCH_LLREWD" | "LS_LLCN_LLREWD" | "LS_COUPON_LLREWD" | " LS_WITH_DRAW_CARD_LLREWD" | "LS_LLCH_TRANSFER_CARD_LLREWD" | "LS_PROFILE_PENDANT_LLREWD" | " LS_DEFAULT_BLESS" | " LS_DEFAULT_LLCN" | "LS_AD_PHOTO" | " LS_AD_VIDEO",
    /**
     * 奖励类型
     */
    encourage_type?: "UNKNOWN" | "DEFAULT_BLESS" | "DEFAULT_LLCN" | "LUCK_RUSH_CHANCE" | "LLCN" | "LLCH" | "COUPON" | "LLWDW_CARD" | "LLCH_TRANSFER_CARD" | "PROFILE_PENDANT" | "COMMON_ZT_TASK_LLCN" | "COMMON_ZT_TASK_LLCH" | "FOLLOW_LLREWD_LLCN_TASK" | "FOLLOW_LLREWD_LLCH_TASK" | "FOLLOW_LLREWD_BLESS_TASK" | "FOLLOW_LLREWD_RUSH_TASK" | "WATCH_LIVE_LLREWD_LLCH_TASK" | "WATCH_LIVE_LLREWD_LLCN_TASK" | "WATCH_LIVE_LLREWD_RUSH_TASK" | "TIME_LIMITED_ASSIST_DOUBLE_LLCN_TASK" | "TIME_LIMITED_ASSIST_DOUBLE_LLCH_TASK" | "TIME_LIMITED_COUNT_LLCN_TASK" | "TIME_LIMITED_COUNT_LLCH_TASK" | "TIME_LIMITED_INVITE_LLCN_TASK" | "TIME_LIMITED_INVITE_LLCH_TASK" | "TIME_LIMITED_INVITE_REFLUX_LLCN_TASK" | "TIME_LIMITED_INVITE_REFLUX_LLCH_TASK" | "TIME_LIMITED_PK_TASK" | "TEAM_TASK" | "AD_VIDEO" | "AD_PHOTO" | "FISSION_GOAT_DIVERSATION" | "PHYSICAL_PRODUCT" | "AD_STATIC_DIVERSION_POPUP" | "ALWAYS_PULL_TASK" | "INVOKE_APP_LLREWD_LLCN" | "INVOKE_APP_LLREWD_LLCH" | "INVOKE_APP_LLREWD_SHAKE",
    /**
     * 任务类型
     */
    task_type?: string,
    /**
     * 任务id
     */
    task_id?: string,
    /**
     * 任务名称
     */
    task_name?: string,
    /**
     * 冠名商名称
     */
    brand_name?: string,
    /**
     * 包含所有券的实例id
     */
    coupon_id?: string,
    /**
     * 商业化id，包含图片、视频等，实际上报为url区分
     */
    photo_id?: string,
    /**
     * 点击的按钮名称，上报按钮文案，如“去开心手下”
     */
    button_name?: string
  }
}

export type ELEMENT_SHOW_EVENT__OP_ACTIVITY_SUM2025_MAIN__OP_ACTIVITY_TASK_ASSIGN_POP_FORWARD = {
  /**
   * 主会场任务下发弹窗封皮
   */
  action: "OP_ACTIVITY_TASK_ASSIGN_POP_FORWARD",
  params: {
    /**
     * 弹窗类型
     */
    popup_type?: "TIME_ASSIST_TASK_SUCCESS" | "TIME_ASSIST_RETURN_TASK_SUCCESS" | "TIME_LIMIT_TASK_SUCCESS" | "ASSIST_DOUBLE_LLCH_TASK_SUCCESS" | "ASSIST_DOUBLE_LLCN_TASK_SUCCESS" | " LS_LLCH_LLREWD" | "LS_LLCN_LLREWD" | "LS_COUPON_LLREWD" | " LS_WITH_DRAW_CARD_LLREWD" | "LS_LLCH_TRANSFER_CARD_LLREWD" | "LS_PROFILE_PENDANT_LLREWD" | " LS_DEFAULT_BLESS" | " LS_DEFAULT_LLCN" | "LS_AD_PHOTO" | " LS_AD_VIDEO",
    /**
     * 任务类型
     */
    task_type?: string,
    /**
     * 任务id
     */
    task_id?: string,
    /**
     * 任务名称
     */
    task_name?: string,
    /**
     * 冠名商名称
     */
    brand_name?: string,
    /**
     * 弹窗主标题
     */
    title?: string
  }
}

export type ELEMENT_SHOW_EVENT__OP_ACTIVITY_SUM2025_MAIN__OP_ACTIVITY_REWARD_POP_FORWARD = {
  /**
   * 主会场奖励弹窗封皮
   */
  action: "OP_ACTIVITY_REWARD_POP_FORWARD",
  params: {
    /**
     * 奖励类型
     */
    encourage_type?: "UNKNOWN" | "DEFAULT_BLESS" | "DEFAULT_LLCN" | "LUCK_RUSH_CHANCE" | "LLCN" | "LLCH" | "COUPON" | "LLWDW_CARD" | "LLCH_TRANSFER_CARD" | "PROFILE_PENDANT" | "COMMON_ZT_TASK_LLCN" | "COMMON_ZT_TASK_LLCH" | "FOLLOW_LLREWD_LLCN_TASK" | "FOLLOW_LLREWD_LLCH_TASK" | "FOLLOW_LLREWD_BLESS_TASK" | "FOLLOW_LLREWD_RUSH_TASK" | "WATCH_LIVE_LLREWD_LLCH_TASK" | "WATCH_LIVE_LLREWD_LLCN_TASK" | "WATCH_LIVE_LLREWD_RUSH_TASK" | "TIME_LIMITED_ASSIST_DOUBLE_LLCN_TASK" | "TIME_LIMITED_ASSIST_DOUBLE_LLCH_TASK" | "TIME_LIMITED_COUNT_LLCN_TASK" | "TIME_LIMITED_COUNT_LLCH_TASK" | "TIME_LIMITED_INVITE_LLCN_TASK" | "TIME_LIMITED_INVITE_LLCH_TASK" | "TIME_LIMITED_INVITE_REFLUX_LLCN_TASK" | "TIME_LIMITED_INVITE_REFLUX_LLCH_TASK" | "TIME_LIMITED_PK_TASK" | "TEAM_TASK" | "AD_VIDEO" | "AD_PHOTO" | "FISSION_GOAT_DIVERSATION" | "PHYSICAL_PRODUCT" | "AD_STATIC_DIVERSION_POPUP" | "ALWAYS_PULL_TASK" | "INVOKE_APP_LLREWD_LLCN" | "INVOKE_APP_LLREWD_LLCH" | "INVOKE_APP_LLREWD_SHAKE",
    /**
     * 弹窗类型
     */
    popup_type?: "TIME_ASSIST_TASK_SUCCESS" | "TIME_ASSIST_RETURN_TASK_SUCCESS" | "TIME_LIMIT_TASK_SUCCESS" | "ASSIST_DOUBLE_LLCH_TASK_SUCCESS" | "ASSIST_DOUBLE_LLCN_TASK_SUCCESS" | " LS_LLCH_LLREWD" | "LS_LLCN_LLREWD" | "LS_COUPON_LLREWD" | " LS_WITH_DRAW_CARD_LLREWD" | "LS_LLCH_TRANSFER_CARD_LLREWD" | "LS_PROFILE_PENDANT_LLREWD" | " LS_DEFAULT_BLESS" | " LS_DEFAULT_LLCN" | "LS_AD_PHOTO" | " LS_AD_VIDEO",
    /**
     * 品牌名称
     */
    brand_name?: string,
    /**
     * 弹窗标题
     */
    title?: string,
    /**
     * 主按钮文案（默认高亮的button）
     */
    button_name?: string,
    /**
     * 包含所有券的实例id
     */
    coupon_id?: string,
    /**
     * 商业化id，包含图片、视频等，实际上报为url区分
     */
    photo_id?: string
  }
}

export type ELEMENT_SHOW_EVENT__OP_ACTIVITY_SUM2025_MAIN__OP_ACTIVITY_BUSINESS_GRID_LOGO = {
  /**
   * 格子上的商业化logo
   */
  action: "OP_ACTIVITY_BUSINESS_GRID_LOGO",
  params: {
    /**
     * 商业化图片url
     */
    icon_url?: string,
    /**
     * 品牌名
     */
    brand_name?: string
  }
}

export type ELEMENT_SHOW_EVENT__OP_ACTIVITY_SUM2025_MAIN__OP_ACTIVITY_BUSINESS_BUILD_LOGO = {
  /**
   * 建筑上的商业化logo
   */
  action: "OP_ACTIVITY_BUSINESS_BUILD_LOGO",
  params: {
    /**
     * 建筑上的logo类型，区分商业化和建筑装饰
     */
    type?: "guide" | "decoration",
    /**
     * 商业化图片url
     */
    icon_url?: string,
    /**
     * 跳转url，包含导流
     */
    link_url?: string,
    /**
     * 品牌名
     */
    brand_name?: string,
    /**
     * 上报建筑名
     */
    name?: string
  }
}

export type ELEMENT_SHOW_EVENT__OP_ACTIVITY_SUM2025_MAIN__OP_ACTIVITY_BUSINESS_LOGO = {
  /**
   * 主会场首页商业化logo
   */
  action: "OP_ACTIVITY_BUSINESS_LOGO",
  params: {
    /**
     * 商业化点位的url
     */
    url?: string
  }
}

export type CLICK_EVENT__OP_ACTIVITY_SUM2025_MAIN__OP_ACTIVITY_MAKE_TEAM_SUCCESS = {
  /**
   * 组队成功事件
   */
  action: "OP_ACTIVITY_MAKE_TEAM_SUCCESS",
  params: {
    /**
     * 队伍id
     */
    team_id?: string,
    /**
     * 按钮名称，如“开启组队打卡"和"开心手下“
     */
    button_name?: string,
    /**
     * 标题
     */
    title?: string,
    /**
     * 品牌名称
     */
    brand_name?: string
  }
}

export type CLICK_EVENT__OP_ACTIVITY_SUM2025_MAIN__OP_ACTIVITY_MAKE_TEAM_SIGN_SUCCESS = {
  /**
   * 组队且打卡7天成功事件
   */
  action: "OP_ACTIVITY_MAKE_TEAM_SIGN_SUCCESS",
  params: {
    /**
     * 队伍id
     */
    team_id?: string,
    /**
     * 按钮名称，如“查看我的直通卡"
     */
    button_name?: string,
    /**
     * 品牌名称
     */
    brand_name?: string,
    /**
     * 标题
     */
    title?: string
  }
}

export type CLICK_EVENT__OP_ACTIVITY_SUM2025_MAIN__OP_ACTIVITY_TEAM_BREAK = {
  /**
   * 队伍解散完成
   */
  action: "OP_ACTIVITY_TEAM_BREAK",
  params: {
    /**
     * 解散原因
     */
    break_reason?: "EXIT_TEAM" | "BREAK_SIGN" | "MATCH_EXPIRE",
    /**
     * 是否打卡超过7天
     */
    type?: "1" | "2",
    /**
     * 点击的按钮名称
     */
    button_name?: string,
    /**
     * 品牌名称
     */
    brand_name?: string,
    /**
     * 标题
     */
    title?: string
  }
}

export type ELEMENT_SHOW_EVENT__OP_ACTIVITY_SUM2025_MAIN__OP_ACTIVITY_TEAM_BREAK = {
  /**
   * 队伍解散完成
   */
  action: "OP_ACTIVITY_TEAM_BREAK",
  params: {
    /**
     * 解散原因
     */
    break_reason?: "EXIT_TEAM" | "BREAK_SIGN" | "MATCH_EXPIRE",
    /**
     * 是否打卡超过7天
     */
    type?: "1" | "2",
    /**
     * 品牌名称
     */
    brand_name?: string,
    /**
     * 标题
     */
    title?: string
  }
}

export type ELEMENT_SHOW_EVENT__OP_ACTIVITY_SUM2025_MAIN__OP_ACTIVITY_MAKE_TEAM_SIGN_SUCCESS = {
  /**
   * 组队且打卡7天成功事件
   */
  action: "OP_ACTIVITY_MAKE_TEAM_SIGN_SUCCESS",
  params: {
    /**
     * 队伍id
     */
    team_id?: string,
    /**
     * 品牌名称
     */
    brand_name?: string,
    /**
     * 标题
     */
    title?: string
  }
}

export type ELEMENT_SHOW_EVENT__OP_ACTIVITY_SUM2025_MAIN__OP_ACTIVITY_MAKE_TEAM_SUCCESS = {
  /**
   * 组队成功事件
   */
  action: "OP_ACTIVITY_MAKE_TEAM_SUCCESS",
  params: {
    /**
     * 队伍id
     */
    team_id?: string,
    /**
     * 弹窗标题
     */
    title?: string,
    /**
     * 品牌名称
     */
    brand_name?: string
  }
}

export type CLICK_EVENT__OP_ACTIVITY_SUM2025_MAIN__OP_ACTIVITY_REWARD_CHOOSE_RESULT = {
  /**
   * 礼物选择结果
   */
  action: "OP_ACTIVITY_REWARD_CHOOSE_RESULT",
  params: {
    /**
     * 弹窗来源，区分是打卡完成掉落还是用户主动点击
     */
    source?: string,
    /**
     * 选择成功的商品id
     */
    good_id?: string,
    /**
     * 区分时成功还是失败
     */
    result_type?: "success" | "fail" | "sucess",
    /**
     * 点击的按钮名称，上报按钮文档
     */
    button_name?: string
  }
}

export type CLICK_EVENT__OP_ACTIVITY_SUM2025_MAIN__OP_ACTIVITY_PUSH_OPEN_GUIDE_IS_OPEN_RESULT = {
  /**
   * 开关开启结果
   */
  action: "OP_ACTIVITY_PUSH_OPEN_GUIDE_IS_OPEN_RESULT",
  params: {
    is_open?: "true" | "false"
  }
}

export type CLICK_EVENT__OP_ACTIVITY_SUM2025_MAIN__OP_ACTIVITY_PUSH_OPEN_GUIDE_CNY_POPUP = {
  /**
   * 打开推送消息通知弹窗
   */
  action: "OP_ACTIVITY_PUSH_OPEN_GUIDE_CNY_POPUP",
  params: {
    /**
     * 点击类型
     */
    click_type?: "open" | "ignore" | "close"
  }
}

export type ELEMENT_SHOW_EVENT__OP_ACTIVITY_SUM2025_MAIN__OP_ACTIVITY_PUSH_OPEN_GUIDE_CNY_POPUP = {
  /**
   * 打开推送消息通知弹窗
   */
  action: "OP_ACTIVITY_PUSH_OPEN_GUIDE_CNY_POPUP",
  params: {}
}

export type CLICK_EVENT__OP_ACTIVITY_SUM2025_MAIN__OP_ACTIVITY_CORE_POP = {
  /**
   * 主会场其他核心弹窗
   */
  action: "OP_ACTIVITY_CORE_POP",
  params: {
    /**
     * 弹窗类型，需要给枚举值
     */
    popup_type?: string,
    /**
     * 弹窗标题
     */
    title?: string,
    /**
     * 品牌商名称
     */
    brand_name?: string,
    /**
     * 包含所有券的实例id，没有上报“”
     */
    coupon_id?: string,
    /**
     * 包含商品的上报商品id，没有上报""
     */
    good_id?: string,
    /**
     * 上报点击的按钮名称，关闭不上报
     */
    button_name?: string
  }
}

export type ELEMENT_SHOW_EVENT__OP_ACTIVITY_SUM2025_MAIN__OP_ACTIVITY_CORE_POP = {
  /**
   * 主会场其他核心弹窗
   */
  action: "OP_ACTIVITY_CORE_POP",
  params: {
    /**
     * 弹窗类型，需要给枚举值
     */
    popup_type?: string,
    /**
     * 弹窗标题
     */
    title?: string,
    /**
     * 品牌商名称
     */
    brand_name?: string,
    /**
     * 包含所有券的实例id
     */
    coupon_id?: string,
    /**
     * 包含商品的上报商品id
     */
    good_id?: string,
    /**
     * 主button按钮
     */
    button_name?: string
  }
}

export type CLICK_EVENT__OP_ACTIVITY_SUM2025_MAIN__OP_ACTIVITY_TASK_RESULT_POP = {
  /**
   * 主会场返回结果弹窗
   */
  action: "OP_ACTIVITY_TASK_RESULT_POP",
  params: {
    /**
     * 任务完成状态
     */
    task_finish_status?: "success" | "fail",
    /**
     * 任务类型
     */
    task_type?: string,
    /**
     * 任务id
     */
    task_id?: string,
    /**
     * 任务名称
     */
    task_name?: string,
    /**
     * 点击的按钮名称，上报按钮文案，如“去开心手下”
     */
    button_name?: string,
    /**
     * 冠名商名称
     */
    brand_name?: string,
    /**
     * 奖励类型
     */
    encourage_type?: "UNKNOWN" | "DEFAULT_BLESS" | "DEFAULT_LLCN" | "LUCK_RUSH_CHANCE" | "LLCN" | "LLCH" | "COUPON" | "LLWDW_CARD" | "LLCH_TRANSFER_CARD" | "PROFILE_PENDANT" | "COMMON_ZT_TASK_LLCN" | "COMMON_ZT_TASK_LLCH" | "FOLLOW_LLREWD_LLCN_TASK" | "FOLLOW_LLREWD_LLCH_TASK" | "FOLLOW_LLREWD_BLESS_TASK" | "FOLLOW_LLREWD_RUSH_TASK" | "WATCH_LIVE_LLREWD_LLCH_TASK" | "WATCH_LIVE_LLREWD_LLCN_TASK" | "WATCH_LIVE_LLREWD_RUSH_TASK" | "TIME_LIMITED_ASSIST_DOUBLE_LLCN_TASK" | "TIME_LIMITED_ASSIST_DOUBLE_LLCH_TASK" | "TIME_LIMITED_COUNT_LLCN_TASK" | "TIME_LIMITED_COUNT_LLCH_TASK" | "TIME_LIMITED_INVITE_LLCN_TASK" | "TIME_LIMITED_INVITE_LLCH_TASK" | "TIME_LIMITED_INVITE_REFLUX_LLCN_TASK" | "TIME_LIMITED_INVITE_REFLUX_LLCH_TASK" | "TIME_LIMITED_PK_TASK" | "TEAM_TASK" | "AD_VIDEO" | "AD_PHOTO" | "FISSION_GOAT_DIVERSATION" | "PHYSICAL_PRODUCT" | "AD_STATIC_DIVERSION_POPUP" | "ALWAYS_PULL_TASK" | "INVOKE_APP_LLREWD_LLCN" | "INVOKE_APP_LLREWD_LLCH" | "INVOKE_APP_LLREWD_SHAKE",
    /**
     * 弹窗类型
     */
    popup_type?: "TIME_ASSIST_TASK_SUCCESS" | "TIME_ASSIST_RETURN_TASK_SUCCESS" | "TIME_LIMIT_TASK_SUCCESS" | "ASSIST_DOUBLE_LLCH_TASK_SUCCESS" | "ASSIST_DOUBLE_LLCN_TASK_SUCCESS" | " LS_LLCH_LLREWD" | "LS_LLCN_LLREWD" | "LS_COUPON_LLREWD" | " LS_WITH_DRAW_CARD_LLREWD" | "LS_LLCH_TRANSFER_CARD_LLREWD" | "LS_PROFILE_PENDANT_LLREWD" | " LS_DEFAULT_BLESS" | " LS_DEFAULT_LLCN" | "LS_AD_PHOTO" | " LS_AD_VIDEO",
    /**
     * 商业化id，包含图片、视频等，实际上报为url区分
     */
    photo_id?: string,
    /**
     * 包含所有券的实例id
     */
    coupon_id?: string
  }
}

export type ELEMENT_SHOW_EVENT__OP_ACTIVITY_SUM2025_MAIN__OP_ACTIVITY_TASK_RESULT_POP = {
  /**
   * 主会场返回结果弹窗
   */
  action: "OP_ACTIVITY_TASK_RESULT_POP",
  params: {
    /**
     * 任务完成状态
     */
    task_finish_status?: "success" | "fail",
    /**
     * 任务类型
     */
    task_type?: string,
    /**
     * 任务id
     */
    task_id?: string,
    /**
     * 任务名称
     */
    task_name?: string,
    /**
     * 冠名商名称
     */
    brand_name?: string,
    /**
     * 弹窗类型
     */
    popup_type?: "TIME_ASSIST_TASK_SUCCESS" | "TIME_ASSIST_RETURN_TASK_SUCCESS" | "TIME_LIMIT_TASK_SUCCESS" | "ASSIST_DOUBLE_LLCH_TASK_SUCCESS" | "ASSIST_DOUBLE_LLCN_TASK_SUCCESS" | " LS_LLCH_LLREWD" | "LS_LLCN_LLREWD" | "LS_COUPON_LLREWD" | " LS_WITH_DRAW_CARD_LLREWD" | "LS_LLCH_TRANSFER_CARD_LLREWD" | "LS_PROFILE_PENDANT_LLREWD" | " LS_DEFAULT_BLESS" | " LS_DEFAULT_LLCN" | "LS_AD_PHOTO" | " LS_AD_VIDEO",
    /**
     * 包含所有券的实例id
     */
    coupon_id?: string,
    /**
     * 奖励类型
     */
    encourage_type?: "UNKNOWN" | "DEFAULT_BLESS" | "DEFAULT_LLCN" | "LUCK_RUSH_CHANCE" | "LLCN" | "LLCH" | "COUPON" | "LLWDW_CARD" | "LLCH_TRANSFER_CARD" | "PROFILE_PENDANT" | "COMMON_ZT_TASK_LLCN" | "COMMON_ZT_TASK_LLCH" | "FOLLOW_LLREWD_LLCN_TASK" | "FOLLOW_LLREWD_LLCH_TASK" | "FOLLOW_LLREWD_BLESS_TASK" | "FOLLOW_LLREWD_RUSH_TASK" | "WATCH_LIVE_LLREWD_LLCH_TASK" | "WATCH_LIVE_LLREWD_LLCN_TASK" | "WATCH_LIVE_LLREWD_RUSH_TASK" | "TIME_LIMITED_ASSIST_DOUBLE_LLCN_TASK" | "TIME_LIMITED_ASSIST_DOUBLE_LLCH_TASK" | "TIME_LIMITED_COUNT_LLCN_TASK" | "TIME_LIMITED_COUNT_LLCH_TASK" | "TIME_LIMITED_INVITE_LLCN_TASK" | "TIME_LIMITED_INVITE_LLCH_TASK" | "TIME_LIMITED_INVITE_REFLUX_LLCN_TASK" | "TIME_LIMITED_INVITE_REFLUX_LLCH_TASK" | "TIME_LIMITED_PK_TASK" | "TEAM_TASK" | "AD_VIDEO" | "AD_PHOTO" | "FISSION_GOAT_DIVERSATION" | "PHYSICAL_PRODUCT" | "AD_STATIC_DIVERSION_POPUP" | "ALWAYS_PULL_TASK" | "INVOKE_APP_LLREWD_LLCN" | "INVOKE_APP_LLREWD_LLCH" | "INVOKE_APP_LLREWD_SHAKE",
    /**
     * 商业化id，包含图片、视频等，实际上报为url区分
     */
    photo_id?: string
  }
}

export type CLICK_EVENT__OP_ACTIVITY_SUM2025_MAIN__OP_ACTIVITY_TASK_ASSIGN_POP = {
  /**
   * 主会场任务下发弹窗
   */
  action: "OP_ACTIVITY_TASK_ASSIGN_POP",
  params: {
    /**
     * 任务类型
     */
    task_type?: string,
    /**
     * 任务id
     */
    task_id?: string,
    /**
     * 任务名称
     */
    task_name?: string,
    /**
     * 冠名商名称
     */
    brand_name?: string,
    /**
     * 弹窗主标题
     */
    title?: string,
    /**
     * 点击的按钮名称，上报按钮文案，如“去邀请好友”
     */
    button_name?: "+",
    /**
     * 弹窗类型
     */
    popup_type?: "TIME_ASSIST_TASK_SUCCESS" | "TIME_ASSIST_RETURN_TASK_SUCCESS" | "TIME_LIMIT_TASK_SUCCESS" | "ASSIST_DOUBLE_LLCH_TASK_SUCCESS" | "ASSIST_DOUBLE_LLCN_TASK_SUCCESS" | "LS_LLCH_LLREWD" | "LS_LLCN_LLREWD" | "LS_COUPON_LLREWD" | "LS_WITH_DRAW_CARD_LLREWD" | "LS_LLCH_TRANSFER_CARD_LLREWD" | "LS_PROFILE_PENDANT_LLREWD" | "LS_DEFAULT_BLESS" | "LS_DEFAULT_LLCN" | "LS_AD_PHOTO" | "LS_AD_VIDEO" | "GRID_TASK_SHEET" | "HUGE_SIGN_IN_SUBSCRIBE_POPUP"
  }
}

export type ELEMENT_SHOW_EVENT__OP_ACTIVITY_SUM2025_MAIN__OP_ACTIVITY_TASK_ASSIGN_POP = {
  /**
   * 主会场任务下发弹窗
   */
  action: "OP_ACTIVITY_TASK_ASSIGN_POP",
  params: {
    /**
     * 任务类型
     */
    task_type?: string,
    /**
     * 任务id
     */
    task_id?: string,
    /**
     * 任务名称
     */
    task_name?: string,
    /**
     * 冠名商名称
     */
    brand_name?: string,
    /**
     * 弹窗主标题
     */
    title?: string,
    /**
     * 弹窗类型
     */
    popup_type?: "TIME_ASSIST_TASK_SUCCESS" | "TIME_ASSIST_RETURN_TASK_SUCCESS" | "TIME_LIMIT_TASK_SUCCESS" | "ASSIST_DOUBLE_LLCH_TASK_SUCCESS" | "ASSIST_DOUBLE_LLCN_TASK_SUCCESS" | "LS_LLCH_LLREWD" | "LS_LLCN_LLREWD" | "LS_COUPON_LLREWD" | "LS_WITH_DRAW_CARD_LLREWD" | "LS_LLCH_TRANSFER_CARD_LLREWD" | "LS_PROFILE_PENDANT_LLREWD" | "LS_DEFAULT_BLESS" | "LS_DEFAULT_LLCN" | "LS_AD_PHOTO" | "LS_AD_VIDEO" | "GRID_TASK_SHEET" | "HUGE_SIGN_IN_SUBSCRIBE_POPUP"
  }
}

export type CLICK_EVENT__OP_ACTIVITY_SUM2025_MAIN__OP_ACTIVITY_DRECT_CARD = {
  /**
   * 直通卡
   */
  action: "OP_ACTIVITY_DRECT_CARD",
  params: {
    /**
     * 卡片顺序，从上到下从1开始
     */
    index?: string,
    /**
     * 目前每个卡片针对的按钮文案，用来区分是否可用"去使用"
     */
    button_type?: "1" | "2" | "3" | "4" | "5" | "去使用" | "明日可用" | "暂不使用（置灰的不可点击" | "已使用（置灰的不可点击" | "已过期（置灰的不可点击"
  }
}

export type ELEMENT_SHOW_EVENT__OP_ACTIVITY_SUM2025_MAIN__OP_ACTIVITY_DRECT_CARD = {
  /**
   * 直通卡
   */
  action: "OP_ACTIVITY_DRECT_CARD",
  params: {
    /**
     * 卡片顺序，从上到下从1开始
     */
    index?: string,
    /**
     * 目前每个卡片针对的按钮文案，用来区分是否可用"去使用"
     */
    button_type?: "1" | "2" | "3" | "4" | "5" | "去使用" | "明日可用" | "暂不使用（置灰的不可点击" | "已使用（置灰的不可点击" | "已过期（置灰的不可点击"
  }
}

export type CLICK_EVENT__OP_ACTIVITY_SUM2025_MAIN__OP_ACTIVITY_MAKE_TEAM_BUTTON = {
  /**
   * 主会场页面去组队按钮
   */
  action: "OP_ACTIVITY_MAKE_TEAM_BUTTON",
  params: {
    /**
     * 目前组队状态
     */
    entry_status?: "1" | "2" | "3" | "4",
    /**
     * 目前组队人数
     */
    number?: "1" | "2" | "3"
  }
}

export type ELEMENT_SHOW_EVENT__OP_ACTIVITY_SUM2025_MAIN__OP_ACTIVITY_MAKE_TEAM_BUTTON = {
  /**
   * 主会场页面去组队按钮
   */
  action: "OP_ACTIVITY_MAKE_TEAM_BUTTON",
  params: {
    /**
     * 目前组队状态
     */
    entry_status?: "1" | "2" | "3" | "4",
    /**
     * 目前组队人数
     */
    number?: "1" | "2" | "3"
  }
}

export type CLICK_EVENT__OP_ACTIVITY_SUM2025_MAIN__OP_ACTIVITY_TASK_ITEM = {
  /**
   * 任务卡片
   */
  action: "OP_ACTIVITY_TASK_ITEM",
  params: {
    /**
     * 上报任务弹窗顶部的title文案，如“邀好友，得现金“”做任务，赚步数“
     */
    title?: string,
    /**
     * 任务id；
     */
    task_id?: string,
    /**
     * 任务名称
     */
    task_name?: string,
    /**
     * 任务类型
     */
    task_type?: string,
    /**
     * 任务状态
     */
    task_status?: string,
    /**
     * 任务页面链接
     */
    url?: string,
    /**
     * 卡片顺序，从上到下从1开始
     */
    index?: string,
    /**
     * 按钮名称
     */
    button_name?: string,
    /**
     * 是否为悬浮弹窗类型
     */
    is_popup?: string
  }
}

export type ELEMENT_SHOW_EVENT__OP_ACTIVITY_SUM2025_MAIN__OP_ACTIVITY_TASK_ITEM = {
  /**
   * 任务卡片
   */
  action: "OP_ACTIVITY_TASK_ITEM",
  params: {
    /**
     * 上报任务弹窗顶部的title文案，如“邀好友，得现金“”做任务，赚步数“
     */
    title?: string,
    /**
     * 任务id
     */
    task_id?: string,
    /**
     * 任务名称
     */
    task_name?: string,
    /**
     * 任务类型
     */
    task_type?: string,
    /**
     * 任务状态
     */
    task_status?: string,
    /**
     * 任务页面链接
     */
    url?: string,
    /**
     * 卡片顺序，从上到下从1开始
     */
    index?: string,
    /**
     * 按钮名称
     */
    button_name?: string,
    /**
     * 是否为悬浮弹窗类型
     */
    is_popup?: string
  }
}

export type ELEMENT_SHOW_EVENT__OP_ACTIVITY_SUM2025_MAIN__OP_ACTIVITY_TASK_LIST_POP = {
  /**
   * 任务列表弹窗
   */
  action: "OP_ACTIVITY_TASK_LIST_POP",
  params: {
    /**
     * 上报任务弹窗顶部的title文案，如“邀好友，得现金“”做任务，赚步数“
     */
    title?: string
  }
}

export type ELEMENT_SHOW_EVENT__OP_ACTIVITY_SUM2025_MAIN__OP_ACTIVITY_MAKE_TEAM_BUBBLE_REMIND_POP = {
  /**
   * 组队气泡提醒弹窗
   */
  action: "OP_ACTIVITY_MAKE_TEAM_BUBBLE_REMIND_POP",
  params: {
    /**
     * 组队气泡提醒弹窗文案
     */
    title?: string
  }
}

export type CLICK_EVENT__OP_ACTIVITY_SUM2025_MAIN__OP_ACTIVITY_BUBBLE_REMIND_POP = {
  /**
   * 气泡提醒弹窗
   */
  action: "OP_ACTIVITY_BUBBLE_REMIND_POP",
  params: {
    /**
     * 气泡提醒弹窗文案
     */
    title?: string,
    /**
     * 人数，上报具体数值
     */
    remind_num?: string,
    /**
     * 点击按钮名称，如“去提醒”
     */
    click_positon?: string,
    /**
     * 点击按钮名称，如“去提醒”
     */
    click_position?: string
  }
}

export type ELEMENT_SHOW_EVENT__OP_ACTIVITY_SUM2025_MAIN__OP_ACTIVITY_BUBBLE_REMIND_POP = {
  /**
   * 气泡提醒弹窗
   */
  action: "OP_ACTIVITY_BUBBLE_REMIND_POP",
  params: {
    /**
     * 气泡提醒弹窗文案
     */
    title?: string,
    /**
     * 人数，上报具体数值
     */
    remind_num?: string
  }
}

export type CLICK_EVENT__OP_ACTIVITY_SUM2025_MAIN__OP_ACTIVITY_TASK_BUTTON = {
  /**
   * 主会场任务入口button
   */
  action: "OP_ACTIVITY_TASK_BUTTON",
  params: {
    /**
     * 按钮文案
     */
    title?: string
  }
}

export type ELEMENT_SHOW_EVENT__OP_ACTIVITY_SUM2025_MAIN__OP_ACTIVITY_SIGN_INFO_POP = {
  /**
   * 打卡进度说明弹窗
   */
  action: "OP_ACTIVITY_SIGN_INFO_POP",
  params: {
    /**
     * 上面弹窗文案
     */
    title?: string,
    /**
     * 主按钮文案，如“向前冲”
     */
    button_name?: string
  }
}

export type CLICK_EVENT__OP_ACTIVITY_SUM2025_MAIN__OP_ACTIVITY_RE_SIGN_POP = {
  /**
   * 补签弹窗
   */
  action: "OP_ACTIVITY_RE_SIGN_POP",
  params: {
    /**
     * 点击的按钮名称，上报按钮文案，如“补签“、”立即邀请“
     */
    button_name?: string,
    /**
     * 用户看到的任务类型title，多个通过arry上传
     */
    title?: string,
    /**
     * 用户看到的任务类型title，多个通过arry上传，枚举用server已有
     */
    type?: string,
    /**
     * 区分不同的补签弹窗类型
     */
    popup_type?: "1" | "2"
  }
}

export type ELEMENT_SHOW_EVENT__OP_ACTIVITY_SUM2025_MAIN__OP_ACTIVITY_RE_SIGN_POP = {
  /**
   * 补签弹窗
   */
  action: "OP_ACTIVITY_RE_SIGN_POP",
  params: {
    /**
     * 用户看到的任务类型title，多个通过arry上传
     */
    title?: string,
    /**
     * 用户看到的任务类型title，多个通过arry上传，枚举用server已有
     */
    type?: string,
    /**
     * 区分不同的补签弹窗类型
     */
    popup_type?: "1" | "2"
  }
}

export type CLICK_EVENT__OP_ACTIVITY_SUM2025_MAIN__OP_ACTIVITY_CALENDER_POP = {
  /**
   * 日历弹窗
   */
  action: "OP_ACTIVITY_CALENDER_POP",
  params: {
    /**
     * 日历弹窗签到状态
     */
    status?: "1" | "0",
    /**
     * 弹窗标题，上传弹窗标题文案，如“已连续签到14天“”已经断签2天“了
     */
    title?: string,
    /**
     * 点击的按钮名称
     */
    button_name?: "chang_good" | "re_sign" | "reminder" | "close"
  }
}

export type ELEMENT_SHOW_EVENT__OP_ACTIVITY_SUM2025_MAIN__OP_ACTIVITY_CALENDER_POP = {
  /**
   * 日历弹窗
   */
  action: "OP_ACTIVITY_CALENDER_POP",
  params: {
    /**
     * 日历弹窗签到状态
     */
    status?: "1" | "0",
    /**
     * 弹窗标题，上传弹窗标题文案，如“已连续签到14天“”已经断签2天“了
     */
    title?: string
  }
}

export type CLICK_EVENT__OP_ACTIVITY_SUM2025_MAIN__OP_ACTIVITY_TIME_BAR = {
  /**
   * 主会场打卡记录按钮（日历入口）
   */
  action: "OP_ACTIVITY_TIME_BAR",
  params: {
    /**
     * 按钮处文案标题
     */
    title?: string
  }
}

export type ELEMENT_SHOW_EVENT__OP_ACTIVITY_SUM2025_MAIN__OP_ACTIVITY_TIME_BAR = {
  /**
   * 主会场打卡记录按钮（日历入口）
   */
  action: "OP_ACTIVITY_TIME_BAR",
  params: {
    /**
     * 按钮处文案标题
     */
    title?: string
  }
}

export type CLICK_EVENT__OP_ACTIVITY_SUM2025_MAIN__OP_ACTIVITY_CHANGE_GOOD_ENTR = {
  /**
   * 主会场首页换商品入口
   */
  action: "OP_ACTIVITY_CHANGE_GOOD_ENTR",
  params: {
    /**
     * 曝光时的商品id
     */
    good_id?: string
  }
}

export type ELEMENT_SHOW_EVENT__OP_ACTIVITY_SUM2025_MAIN__OP_ACTIVITY_CHANGE_GOOD_ENTR = {
  /**
   * 主会场首页换商品入口
   */
  action: "OP_ACTIVITY_CHANGE_GOOD_ENTR",
  params: {
    /**
     * 曝光时的商品id
     */
    good_id?: string
  }
}

export type CLICK_EVENT__OP_ACTIVITY_SUM2025_MAIN__OP_ACTIVITY_GUID_BUTTON = {
  /**
   * 主页面导流button
   */
  action: "OP_ACTIVITY_GUID_BUTTON",
  params: {
    /**
     * 按钮文案
     */
    title?: string,
    /**
     * 无文案时上报url链接
     */
    url?: string
  }
}

export type ELEMENT_SHOW_EVENT__OP_ACTIVITY_SUM2025_MAIN__OP_ACTIVITY_GUID_BUTTON = {
  /**
   * 主页面导流button
   */
  action: "OP_ACTIVITY_GUID_BUTTON",
  params: {
    /**
     * 按钮文案
     */
    title?: string,
    /**
     * 无文案时上报url链接
     */
    url?: string
  }
}

export type CLICK_EVENT__OP_ACTIVITY_SUM2025_MAIN__OP_ACTIVITY_ICON_BUTTON_MORE = {
  /**
   * 更多里面的按钮点击
   */
  action: "OP_ACTIVITY_ICON_BUTTON_MORE",
  params: {
    /**
     * 按钮文案
     */
    title?: string
  }
}

export type CLICK_EVENT__OP_ACTIVITY_SUM2025_MAIN__OP_ACTIVITY_ICON_BUTTON = {
  /**
   * 顶部功能葫芦串
   */
  action: "OP_ACTIVITY_ICON_BUTTON",
  params: {
    /**
     * 按钮文案
     */
    title?: string
  }
}
export type ACTIVITY_SUMMER2025 = {
  application_info: {
    appName?: "ACTIVITY_SUMMER2025",
    groupIdList?: [43370,48276]
  },
  track_info: {
    event_type: "CLICK_EVENT",
    page_info: PAGE_SHOW_EVENT__OP_ACTIVITY_SUM2025_MAIN,
    element_info: CLICK_EVENT__OP_ACTIVITY_SUM2025_MAIN__OP_ACTIVITY_MAKE_TEAM_CARD
  } | {
    event_type: "ELEMENT_SHOW_EVENT",
    page_info: PAGE_SHOW_EVENT__OP_ACTIVITY_SUM2025_MAIN,
    element_info: ELEMENT_SHOW_EVENT__OP_ACTIVITY_SUM2025_MAIN__OP_ACTIVITY_MAKE_TEAM_CARD
  } | {
    event_type: "ELEMENT_SHOW_EVENT",
    page_info: PAGE_SHOW_EVENT__OP_ACTIVITY_SUM2025_MAIN,
    element_info: ELEMENT_SHOW_EVENT__OP_ACTIVITY_SUM2025_MAIN__OP_ACTIVITY_MAKE_TEAM_ICON
  } | {
    event_type: "CLICK_EVENT",
    page_info: PAGE_SHOW_EVENT__OP_ACTIVITY_SUM2025_MAIN,
    element_info: CLICK_EVENT__OP_ACTIVITY_SUM2025_MAIN__OP_ACTIVITY_MAKE_TEAM_GRID_POP
  } | {
    event_type: "ELEMENT_SHOW_EVENT",
    page_info: PAGE_SHOW_EVENT__OP_ACTIVITY_SUM2025_MAIN,
    element_info: ELEMENT_SHOW_EVENT__OP_ACTIVITY_SUM2025_MAIN__OP_ACTIVITY_MAKE_TEAM_GRID_POP
  } | {
    event_type: "CLICK_EVENT",
    page_info: PAGE_SHOW_EVENT__OP_ACTIVITY_SUM2025_MAIN,
    element_info: CLICK_EVENT__OP_ACTIVITY_SUM2025_MAIN__OP_ACTIVITY_MAKE_TEAM_POP
  } | {
    event_type: "ELEMENT_SHOW_EVENT",
    page_info: PAGE_SHOW_EVENT__OP_ACTIVITY_SUM2025_MAIN,
    element_info: ELEMENT_SHOW_EVENT__OP_ACTIVITY_SUM2025_MAIN__OP_ACTIVITY_MAKE_TEAM_POP
  } | {
    event_type: "CLICK_EVENT",
    page_info: PAGE_SHOW_EVENT__OP_ACTIVITY_SUM2025_MAIN,
    element_info: CLICK_EVENT__OP_ACTIVITY_SUM2025_MAIN__OP_ACTIVITY_MAKE_TEAM_GUID
  } | {
    event_type: "ELEMENT_SHOW_EVENT",
    page_info: PAGE_SHOW_EVENT__OP_ACTIVITY_SUM2025_MAIN,
    element_info: ELEMENT_SHOW_EVENT__OP_ACTIVITY_SUM2025_MAIN__OP_ACTIVITY_MAKE_TEAM_GUID
  } | {
    event_type: "CLICK_EVENT",
    page_info: PAGE_SHOW_EVENT__OP_ACTIVITY_SUM2025_MAIN,
    element_info: CLICK_EVENT__OP_ACTIVITY_SUM2025_MAIN__OP_ACTIVITY_REWARD_POP
  } | {
    event_type: "ELEMENT_SHOW_EVENT",
    page_info: PAGE_SHOW_EVENT__OP_ACTIVITY_SUM2025_MAIN,
    element_info: ELEMENT_SHOW_EVENT__OP_ACTIVITY_SUM2025_MAIN__OP_ACTIVITY_REWARD_POP
  } | {
    event_type: "CLICK_EVENT",
    page_info: PAGE_SHOW_EVENT__OP_ACTIVITY_SUM2025_MAIN,
    element_info: CLICK_EVENT__OP_ACTIVITY_SUM2025_MAIN__OP_ACTIVITY_JION_BUTTON
  } | {
    event_type: "ELEMENT_SHOW_EVENT",
    page_info: PAGE_SHOW_EVENT__OP_ACTIVITY_SUM2025_MAIN,
    element_info: ELEMENT_SHOW_EVENT__OP_ACTIVITY_SUM2025_MAIN__OP_ACTIVITY_JION_BUTTON
  } | {
    event_type: "CLICK_EVENT",
    page_info: PAGE_SHOW_EVENT__OP_ACTIVITY_SUM2025_MAIN,
    element_info: CLICK_EVENT__OP_ACTIVITY_SUM2025_MAIN__OP_ACTIVITY_REWARD_CHOOSE_POP
  } | {
    event_type: "ELEMENT_SHOW_EVENT",
    page_info: PAGE_SHOW_EVENT__OP_ACTIVITY_SUM2025_MAIN,
    element_info: ELEMENT_SHOW_EVENT__OP_ACTIVITY_SUM2025_MAIN__OP_ACTIVITY_REWARD_CHOOSE_POP
  } | {
    event_type: "PAGE_SHOW_EVENT",
    page_info: PAGE_SHOW_EVENT__OP_ACTIVITY_SUM2025_MAIN
  } | {
    event_type: "ELEMENT_SHOW_EVENT",
    page_info: PAGE_SHOW_EVENT__OP_ACTIVITY_SUM2025_MAIN,
    element_info: ELEMENT_SHOW_EVENT__OP_ACTIVITY_SUM2025_MAIN__OP_ACTIVITY_TASK_RESULT_POP__FORWARD
  } | {
    event_type: "ELEMENT_SHOW_EVENT",
    page_info: PAGE_SHOW_EVENT__OP_ACTIVITY_SUM2025_MAIN,
    element_info: ELEMENT_SHOW_EVENT__OP_ACTIVITY_SUM2025_MAIN__OP_ACTIVITY_TASK_ASSIGN_POP_FORWARD
  } | {
    event_type: "ELEMENT_SHOW_EVENT",
    page_info: PAGE_SHOW_EVENT__OP_ACTIVITY_SUM2025_MAIN,
    element_info: ELEMENT_SHOW_EVENT__OP_ACTIVITY_SUM2025_MAIN__OP_ACTIVITY_REWARD_POP_FORWARD
  } | {
    event_type: "ELEMENT_SHOW_EVENT",
    page_info: PAGE_SHOW_EVENT__OP_ACTIVITY_SUM2025_MAIN,
    element_info: ELEMENT_SHOW_EVENT__OP_ACTIVITY_SUM2025_MAIN__OP_ACTIVITY_BUSINESS_GRID_LOGO
  } | {
    event_type: "ELEMENT_SHOW_EVENT",
    page_info: PAGE_SHOW_EVENT__OP_ACTIVITY_SUM2025_MAIN,
    element_info: ELEMENT_SHOW_EVENT__OP_ACTIVITY_SUM2025_MAIN__OP_ACTIVITY_BUSINESS_BUILD_LOGO
  } | {
    event_type: "ELEMENT_SHOW_EVENT",
    page_info: PAGE_SHOW_EVENT__OP_ACTIVITY_SUM2025_MAIN,
    element_info: ELEMENT_SHOW_EVENT__OP_ACTIVITY_SUM2025_MAIN__OP_ACTIVITY_BUSINESS_LOGO
  } | {
    event_type: "CLICK_EVENT",
    page_info: PAGE_SHOW_EVENT__OP_ACTIVITY_SUM2025_MAIN,
    element_info: CLICK_EVENT__OP_ACTIVITY_SUM2025_MAIN__OP_ACTIVITY_MAKE_TEAM_SUCCESS
  } | {
    event_type: "CLICK_EVENT",
    page_info: PAGE_SHOW_EVENT__OP_ACTIVITY_SUM2025_MAIN,
    element_info: CLICK_EVENT__OP_ACTIVITY_SUM2025_MAIN__OP_ACTIVITY_MAKE_TEAM_SIGN_SUCCESS
  } | {
    event_type: "CLICK_EVENT",
    page_info: PAGE_SHOW_EVENT__OP_ACTIVITY_SUM2025_MAIN,
    element_info: CLICK_EVENT__OP_ACTIVITY_SUM2025_MAIN__OP_ACTIVITY_TEAM_BREAK
  } | {
    event_type: "ELEMENT_SHOW_EVENT",
    page_info: PAGE_SHOW_EVENT__OP_ACTIVITY_SUM2025_MAIN,
    element_info: ELEMENT_SHOW_EVENT__OP_ACTIVITY_SUM2025_MAIN__OP_ACTIVITY_TEAM_BREAK
  } | {
    event_type: "ELEMENT_SHOW_EVENT",
    page_info: PAGE_SHOW_EVENT__OP_ACTIVITY_SUM2025_MAIN,
    element_info: ELEMENT_SHOW_EVENT__OP_ACTIVITY_SUM2025_MAIN__OP_ACTIVITY_MAKE_TEAM_SIGN_SUCCESS
  } | {
    event_type: "ELEMENT_SHOW_EVENT",
    page_info: PAGE_SHOW_EVENT__OP_ACTIVITY_SUM2025_MAIN,
    element_info: ELEMENT_SHOW_EVENT__OP_ACTIVITY_SUM2025_MAIN__OP_ACTIVITY_MAKE_TEAM_SUCCESS
  } | {
    event_type: "CLICK_EVENT",
    page_info: PAGE_SHOW_EVENT__OP_ACTIVITY_SUM2025_MAIN,
    element_info: CLICK_EVENT__OP_ACTIVITY_SUM2025_MAIN__OP_ACTIVITY_REWARD_CHOOSE_RESULT
  } | {
    event_type: "CLICK_EVENT",
    page_info: PAGE_SHOW_EVENT__OP_ACTIVITY_SUM2025_MAIN,
    element_info: CLICK_EVENT__OP_ACTIVITY_SUM2025_MAIN__OP_ACTIVITY_PUSH_OPEN_GUIDE_IS_OPEN_RESULT
  } | {
    event_type: "CLICK_EVENT",
    page_info: PAGE_SHOW_EVENT__OP_ACTIVITY_SUM2025_MAIN,
    element_info: CLICK_EVENT__OP_ACTIVITY_SUM2025_MAIN__OP_ACTIVITY_PUSH_OPEN_GUIDE_CNY_POPUP
  } | {
    event_type: "ELEMENT_SHOW_EVENT",
    page_info: PAGE_SHOW_EVENT__OP_ACTIVITY_SUM2025_MAIN,
    element_info: ELEMENT_SHOW_EVENT__OP_ACTIVITY_SUM2025_MAIN__OP_ACTIVITY_PUSH_OPEN_GUIDE_CNY_POPUP
  } | {
    event_type: "CLICK_EVENT",
    page_info: PAGE_SHOW_EVENT__OP_ACTIVITY_SUM2025_MAIN,
    element_info: CLICK_EVENT__OP_ACTIVITY_SUM2025_MAIN__OP_ACTIVITY_CORE_POP
  } | {
    event_type: "ELEMENT_SHOW_EVENT",
    page_info: PAGE_SHOW_EVENT__OP_ACTIVITY_SUM2025_MAIN,
    element_info: ELEMENT_SHOW_EVENT__OP_ACTIVITY_SUM2025_MAIN__OP_ACTIVITY_CORE_POP
  } | {
    event_type: "CLICK_EVENT",
    page_info: PAGE_SHOW_EVENT__OP_ACTIVITY_SUM2025_MAIN,
    element_info: CLICK_EVENT__OP_ACTIVITY_SUM2025_MAIN__OP_ACTIVITY_TASK_RESULT_POP
  } | {
    event_type: "ELEMENT_SHOW_EVENT",
    page_info: PAGE_SHOW_EVENT__OP_ACTIVITY_SUM2025_MAIN,
    element_info: ELEMENT_SHOW_EVENT__OP_ACTIVITY_SUM2025_MAIN__OP_ACTIVITY_TASK_RESULT_POP
  } | {
    event_type: "CLICK_EVENT",
    page_info: PAGE_SHOW_EVENT__OP_ACTIVITY_SUM2025_MAIN,
    element_info: CLICK_EVENT__OP_ACTIVITY_SUM2025_MAIN__OP_ACTIVITY_TASK_ASSIGN_POP
  } | {
    event_type: "ELEMENT_SHOW_EVENT",
    page_info: PAGE_SHOW_EVENT__OP_ACTIVITY_SUM2025_MAIN,
    element_info: ELEMENT_SHOW_EVENT__OP_ACTIVITY_SUM2025_MAIN__OP_ACTIVITY_TASK_ASSIGN_POP
  } | {
    event_type: "CLICK_EVENT",
    page_info: PAGE_SHOW_EVENT__OP_ACTIVITY_SUM2025_MAIN,
    element_info: CLICK_EVENT__OP_ACTIVITY_SUM2025_MAIN__OP_ACTIVITY_DRECT_CARD
  } | {
    event_type: "ELEMENT_SHOW_EVENT",
    page_info: PAGE_SHOW_EVENT__OP_ACTIVITY_SUM2025_MAIN,
    element_info: ELEMENT_SHOW_EVENT__OP_ACTIVITY_SUM2025_MAIN__OP_ACTIVITY_DRECT_CARD
  } | {
    event_type: "CLICK_EVENT",
    page_info: PAGE_SHOW_EVENT__OP_ACTIVITY_SUM2025_MAIN,
    element_info: CLICK_EVENT__OP_ACTIVITY_SUM2025_MAIN__OP_ACTIVITY_MAKE_TEAM_BUTTON
  } | {
    event_type: "ELEMENT_SHOW_EVENT",
    page_info: PAGE_SHOW_EVENT__OP_ACTIVITY_SUM2025_MAIN,
    element_info: ELEMENT_SHOW_EVENT__OP_ACTIVITY_SUM2025_MAIN__OP_ACTIVITY_MAKE_TEAM_BUTTON
  } | {
    event_type: "CLICK_EVENT",
    page_info: PAGE_SHOW_EVENT__OP_ACTIVITY_SUM2025_MAIN,
    element_info: CLICK_EVENT__OP_ACTIVITY_SUM2025_MAIN__OP_ACTIVITY_TASK_ITEM
  } | {
    event_type: "ELEMENT_SHOW_EVENT",
    page_info: PAGE_SHOW_EVENT__OP_ACTIVITY_SUM2025_MAIN,
    element_info: ELEMENT_SHOW_EVENT__OP_ACTIVITY_SUM2025_MAIN__OP_ACTIVITY_TASK_ITEM
  } | {
    event_type: "ELEMENT_SHOW_EVENT",
    page_info: PAGE_SHOW_EVENT__OP_ACTIVITY_SUM2025_MAIN,
    element_info: ELEMENT_SHOW_EVENT__OP_ACTIVITY_SUM2025_MAIN__OP_ACTIVITY_TASK_LIST_POP
  } | {
    event_type: "ELEMENT_SHOW_EVENT",
    page_info: PAGE_SHOW_EVENT__OP_ACTIVITY_SUM2025_MAIN,
    element_info: ELEMENT_SHOW_EVENT__OP_ACTIVITY_SUM2025_MAIN__OP_ACTIVITY_MAKE_TEAM_BUBBLE_REMIND_POP
  } | {
    event_type: "CLICK_EVENT",
    page_info: PAGE_SHOW_EVENT__OP_ACTIVITY_SUM2025_MAIN,
    element_info: CLICK_EVENT__OP_ACTIVITY_SUM2025_MAIN__OP_ACTIVITY_BUBBLE_REMIND_POP
  } | {
    event_type: "ELEMENT_SHOW_EVENT",
    page_info: PAGE_SHOW_EVENT__OP_ACTIVITY_SUM2025_MAIN,
    element_info: ELEMENT_SHOW_EVENT__OP_ACTIVITY_SUM2025_MAIN__OP_ACTIVITY_BUBBLE_REMIND_POP
  } | {
    event_type: "CLICK_EVENT",
    page_info: PAGE_SHOW_EVENT__OP_ACTIVITY_SUM2025_MAIN,
    element_info: CLICK_EVENT__OP_ACTIVITY_SUM2025_MAIN__OP_ACTIVITY_TASK_BUTTON
  } | {
    event_type: "ELEMENT_SHOW_EVENT",
    page_info: PAGE_SHOW_EVENT__OP_ACTIVITY_SUM2025_MAIN,
    element_info: ELEMENT_SHOW_EVENT__OP_ACTIVITY_SUM2025_MAIN__OP_ACTIVITY_SIGN_INFO_POP
  } | {
    event_type: "CLICK_EVENT",
    page_info: PAGE_SHOW_EVENT__OP_ACTIVITY_SUM2025_MAIN,
    element_info: CLICK_EVENT__OP_ACTIVITY_SUM2025_MAIN__OP_ACTIVITY_RE_SIGN_POP
  } | {
    event_type: "ELEMENT_SHOW_EVENT",
    page_info: PAGE_SHOW_EVENT__OP_ACTIVITY_SUM2025_MAIN,
    element_info: ELEMENT_SHOW_EVENT__OP_ACTIVITY_SUM2025_MAIN__OP_ACTIVITY_RE_SIGN_POP
  } | {
    event_type: "CLICK_EVENT",
    page_info: PAGE_SHOW_EVENT__OP_ACTIVITY_SUM2025_MAIN,
    element_info: CLICK_EVENT__OP_ACTIVITY_SUM2025_MAIN__OP_ACTIVITY_CALENDER_POP
  } | {
    event_type: "ELEMENT_SHOW_EVENT",
    page_info: PAGE_SHOW_EVENT__OP_ACTIVITY_SUM2025_MAIN,
    element_info: ELEMENT_SHOW_EVENT__OP_ACTIVITY_SUM2025_MAIN__OP_ACTIVITY_CALENDER_POP
  } | {
    event_type: "CLICK_EVENT",
    page_info: PAGE_SHOW_EVENT__OP_ACTIVITY_SUM2025_MAIN,
    element_info: CLICK_EVENT__OP_ACTIVITY_SUM2025_MAIN__OP_ACTIVITY_TIME_BAR
  } | {
    event_type: "ELEMENT_SHOW_EVENT",
    page_info: PAGE_SHOW_EVENT__OP_ACTIVITY_SUM2025_MAIN,
    element_info: ELEMENT_SHOW_EVENT__OP_ACTIVITY_SUM2025_MAIN__OP_ACTIVITY_TIME_BAR
  } | {
    event_type: "CLICK_EVENT",
    page_info: PAGE_SHOW_EVENT__OP_ACTIVITY_SUM2025_MAIN,
    element_info: CLICK_EVENT__OP_ACTIVITY_SUM2025_MAIN__OP_ACTIVITY_CHANGE_GOOD_ENTR
  } | {
    event_type: "ELEMENT_SHOW_EVENT",
    page_info: PAGE_SHOW_EVENT__OP_ACTIVITY_SUM2025_MAIN,
    element_info: ELEMENT_SHOW_EVENT__OP_ACTIVITY_SUM2025_MAIN__OP_ACTIVITY_CHANGE_GOOD_ENTR
  } | {
    event_type: "CLICK_EVENT",
    page_info: PAGE_SHOW_EVENT__OP_ACTIVITY_SUM2025_MAIN,
    element_info: CLICK_EVENT__OP_ACTIVITY_SUM2025_MAIN__OP_ACTIVITY_GUID_BUTTON
  } | {
    event_type: "ELEMENT_SHOW_EVENT",
    page_info: PAGE_SHOW_EVENT__OP_ACTIVITY_SUM2025_MAIN,
    element_info: ELEMENT_SHOW_EVENT__OP_ACTIVITY_SUM2025_MAIN__OP_ACTIVITY_GUID_BUTTON
  } | {
    event_type: "CLICK_EVENT",
    page_info: PAGE_SHOW_EVENT__OP_ACTIVITY_SUM2025_MAIN,
    element_info: CLICK_EVENT__OP_ACTIVITY_SUM2025_MAIN__OP_ACTIVITY_ICON_BUTTON_MORE
  } | {
    event_type: "CLICK_EVENT",
    page_info: PAGE_SHOW_EVENT__OP_ACTIVITY_SUM2025_MAIN,
    element_info: CLICK_EVENT__OP_ACTIVITY_SUM2025_MAIN__OP_ACTIVITY_ICON_BUTTON
  }
}
