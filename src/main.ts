// :::[gundam main-top]:::
import { initStore } from '@gundam/model';
import { vGuide } from '@pet/25cny.guide-directive';
import { provideViewInfo } from '@pet/core.mobile/screenDetect';
import { getUrlSearchParams } from '@pet/yau.core/url';
import { initLogger } from '@pet/yau.logger/init';
import { sendFP, sendTTI } from '@pet/yau.radar';
import { invoke } from '@yoda/bridge';
import { createApp } from 'vue';

import '@pet/adapt.reset/reset.css'; // 全局 css reset
import '@pet/adapt.fonts/style/kuaiyuanhui.css'; // 全局特殊字体
// import '@pet/adapt.fonts/style/puhui105-num.css';
// import '@pet/adapt.fonts/style/Alte-DIN.css';
import '@pet/adapt.fonts/style/font-weight.css'; // 全局字重控制
import '@pet/25cny.theme/index.scss'; // 全局统一样式
import './assets/font/font.scss'; // 字体

import { vLog, loggerUtils, vShowLog, vClickLog, inLiveHalf } from '@/init/logger';

import App from './App.vue';
import './init/model';
import addVconsole from './common/vconsole';
import router from './router';
import { initLocalStore } from './utils/localStore';
import { inHeadless } from './utils/ssg';
invoke('ui.hideLoadingPage').catch(() => {});

const app = createApp({
    router,
    setup() {
        // 全局屏幕信息
        provideViewInfo();
    },
    render: () => h(App),
});
const entrySrc = getUrlSearchParams().entry_src ?? '';
initStore(app, {});
// todo 初始化本地存储（activityId下发调用）
initLocalStore('default');
initLogger(app, import.meta.env.DEV);

app.use(router);
app.directive('guide', vGuide);
app.directive('log', vLog);
app.directive('showLog', vShowLog);
app.directive('clickLog', vClickLog);
// provide logger
app.provide('logger', loggerUtils);

loggerUtils.weblog.sendImmediately('PV', {
    type: 'enter',
    page: 'OP_ACTIVITY_SUM2025_MAIN',
    params: {
        url: window.location.href,
        entry_src: entrySrc,
        activity_name: 'SUMMER2025',
    },
    coPage: inLiveHalf,
});

loggerUtils.queue.unlock();
app.mount(inHeadless() ? '#app-ssg' : '#app');
sendTTI();
sendFP();
addVconsole();

// :::[gundam main-bottom]:::
