import { defineConfig } from '@gundam/vite';
import legacy from '@vitejs/plugin-legacy';
import vue from '@vitejs/plugin-vue';
import type { UserConfig } from 'vite';
import Inspect from 'vite-plugin-inspect';
import { fileURLToPath, URL } from 'node:url';
import autoprefixer from 'autoprefixer';
// @ts-ignore
import px2rem from 'postcss-pxtorem';
// @ts-ignore
import fontFallBack from '@gundam/postcss-font-fallback';
import { flexibleRem } from '@pex/vite-plugin-flexible-rem';
import unpluginImageTools from '@pex/unplugin-image-tools/vite';
import { chunkSplitPlugin } from 'vite-plugin-chunk-split';
import AutoImport from 'unplugin-auto-import/vite';
import { PrefetchPlugin } from '@gundam/gundam-plugin-prefetch-api';

// https://vitejs.dev/config/
export const defaultConfig: UserConfig = {
    base: process.env.GUNDAM_PIPELINE_ENV ? 'https://p4-plat.wskwai.com/kos/nlav111449/activity/' : '/',
    plugins: [
        vue(),
        // 业务方可以按照实际情况修改legacy配置。
        legacy({
            renderLegacyChunks: false,
            targets: 'supports es6',
            modernTargets: ['iOS >= 12', 'chrome >= 70'],
            modernPolyfills: true,
        }),
        Inspect(),
        unpluginImageTools({
            enableViteImagetools: true, // 默认为false，是否内置vite-imagetools 插件
            useBuiltInMixins: {
                scss: false, // 默认为true，可不传，如果为 false 可以通过 vite.config css.preprocessorOptions.scss 设置
            },
        }),
        flexibleRem(414, 100, 500, true, 430),
        chunkSplitPlugin({
            customSplitting: {
                'summer-modules': [
                    /[\\/]node_modules[\\/](vue|vue-router|@vue|@vueuse|vue-demi)[\\/]/,
                    /[\\/]node_modules[\\/](@ks-share|@yoda|@gundam|@kwai-explore|@explore|axios|@ks-radar)[\\/]/,
                    /[\\/]node_modules(?!.*(@ks-video|apng|@ks-kplayer|vconsole|@vision)).*[\\/]/,
                ],
                'summer-pet-vendors': [
                    /[\\/]@pet(?!.*(@effect))(?!.*(vision|popup|sheet|dialog|layer|adapt\.heading|error-handler).*?).*[\\/]/,
                ],
            },
        }),
        AutoImport({
            imports: ['vue', 'vue-router'],
            dts: './typings/auto-imports.d.ts',
            include: [/\.ts$/, /\.vue$/, /\.vue\?vue/],
        }),
        PrefetchPlugin({
            // minify: false, // 预请求代码是否混淆压缩, 默认开启
            // api: 'fetch', // 可以选择xhr、fetch模式，默认xhr
            // checkProps: ['header'], // 缓存是否检查header参数，默认不开启
            list: [
                {
                    url: '/rest/wd/summer25/wishTravel/homePage/mainInfo',
                    method: 'get',
                    adapter(data) {
                        return {
                            query: {
                                entry_src: data.query.entry_src || '',
                                scene: data.query.entry_src === 'ks_2025sum_070' ? 'SOCIAL_TEAM_TASK_ACCESS' : '',
                                resumeVersion: 'v2',
                                externalTouchParam: data.query.externalTouchParam || '',
                            },
                        };
                    },
                    trigger: function (data) {
                        return data.path === '/home';
                    },
                    rejected: function () {
                        console.log('预请求失败了===');
                    },
                    fulfilled: function (data: any) {
                        if (data && data.status === 200) {
                            const yodaBridge = (window as any).__yodaBridge__;
                            if (yodaBridge && yodaBridge.invoke) {
                                yodaBridge.invoke('ui', 'hideLoadingPage', JSON.stringify({}), '___uihideLoading__');
                            }
                        }
                    },
                },
            ],
        }),
    ],
    css: {
        preprocessorOptions: {
            scss: {
                api: 'modern-compiler',
                additionalData: '@use "@/styles/mixins.scss" as *;',
            },
        },
        // 为兼容modifyCssUrlPlugin插件使用，postcss配置从postcss.config.js移到vite中配置
        postcss: {
            plugins: [
                autoprefixer(),
                px2rem({
                    rootValue: 100,
                    propList: ['*'],
                    replace: true,
                    mediaQuery: false,
                    minPixelValue: 0,
                    exclude: /node_modules/,
                }),
                fontFallBack({
                    fallback: ['"PingFang SC"', 'system-ui', 'sans-serif'],
                    exclude: /node_modules/,
                }),
            ],
        },
    },

    build: {
        sourcemap: true,
        minify: 'terser',
        target: ['chrome70', 'safari12'],
        terserOptions: {
            mangle: {
                // eval 对动效有些 bug，先关掉
                // eval: true,
            },
            compress: {
                if_return: false,
            },
        },
    },
    resolve: {
        // dedupe: Object.keys(petPackageJson.dependencies),
        alias: {
            '@': fileURLToPath(new URL('./src', import.meta.url)),
            '@pet': fileURLToPath(new URL('./@pet', import.meta.url)),
            '@effect': fileURLToPath(new URL('./src/@effect', import.meta.url)),
        },
    },
    server: {
        allowedHosts: true,
        proxy: {
            // KDEV平台Mock&代理配置教程 https://docs.corp.kuaishou.com/k/home/<USER>/fcABu6yA6iF-P7U-QAREw-7-1
            // KDEV平台Mock&代理模板 https://kdev.corp.kuaishou.com/web/api-mock/proxy?id=2599
            // Start: Demo中用到的一些代理
            '/response-headers': {
                target: 'https://koasproxy.corp.kuaishou.com/proxy/d4c4168',
                changeOrigin: true,
            },
            '/get': {
                target: 'https://koasproxy.corp.kuaishou.com/proxy/d4c4168',
                changeOrigin: true,
            },
            '/swagger-demo': {
                target: 'https://gundam-server.corp.kuaishou.com',
                changeOrigin: true,
            },
            /** 任务列表 */
            '^/rest/wd/zt/task': {
                target: 'https://kipa.staging.kuaishou.com',
                changeOrigin: true,
            },
            '/rest/wd/summer25/': {
                // target: 'https://summer25.prt.kuaishou.com', // prt
                target: 'https://summer25.staging.kuaishou.com/', // staging
                changeOrigin: true,
            },
            '/rest/brand/activity/cny/download/apk/info': {
                // target: 'https://summer25.prt.kuaishou.com', // prt
                target: 'https://summer25.staging.kuaishou.com/', //staging
                changeOrigin: true,
            },
            // End
        },
    },
    define: {
        'process.env.COMMIT_ID': JSON.stringify(process.env.COMMIT_ID),
    },
};

export default defineConfig(defaultConfig);
