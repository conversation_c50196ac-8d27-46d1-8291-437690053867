/**
 * 对 @ks-share/share 2.0.2 的二次封装
 * @see https://docs.corp.kuaishou.com/d/home/<USER>
 */
import * as _ from '@ks-share/share';
import type { ShareChannel, ShareOptions } from '@ks-share/share-utils';
import { toast } from '@pet/adapt.toast';
import Cookie from 'js-cookie';
import { imHeadsInsertNormalShare, type ExtendShareParam, type InsertUserInfo } from './extendNormalShare';

/**
 * 对三个面板分享方法进行二次封装
 * 1. 通过 type 来区分分享类型
 * 2. Cookie.get('ud') 作为默认 shareObjectId
 * 3. 默认使用沉浸式面板
 */
export const share = async (
    params: {
        /** 分享对应业务，一个分享流程对应的唯一标识，分享后台配置 */
        subBiz: string;
        /** 分享类型，immersive 沉浸式面板(默认), normal 普通面板, simple 最简单的分享场景 */
        type?: 'immersive' | 'normal' | 'simple';
        /** 标识一个分享内容唯一值，可以取user_id、shop_id 或者 photo_id 等 */
        shareObjectId?: string;
        /** 分享上报数据 */
        logExt?: Record<string, any>;
        /** 分享占位符 */
        placeholder?: Record<string, string>;
        /** 插入私信头像列表 */
        insertUserInfos?: InsertUserInfo[];
    } & ShareOptions,
) => {
    console.info('share: ', params);
    const { subBiz, shareObjectId, logExt, placeholder, insertUserInfos = [], ...options } = params;

    switch (params.type) {
        case 'simple':
            /** simpleShare 和 share 不同的是不能传入分享占位符 placeholder，面板都是一模一样的
             * 考虑能不能只调 share，就可以少定义一个类型 */
            return await _.simpleShare(
                {
                    subBiz,
                    shareObjectId: shareObjectId ?? Cookie.get('ud') ?? '',
                    logExt,
                },
                options,
            );
        case 'normal':
            const commonParams = {
                subBiz,
                shareObjectId: shareObjectId ?? Cookie.get('ud') ?? '',
                logExt,
                placeholder,
            }
            if (params.insertUserInfos && params.insertUserInfos.length > 0) {
                return await imHeadsInsertNormalShare({
                    ...commonParams,
                    insertUserInfos,
                }, options);
            } else {
                return await _.share(
                    commonParams,
                    options,
                );
            }
            
        default: // 默认沉浸式面板
            return await _.immersiveShare(
                {
                    subBiz,
                    shareObjectId: shareObjectId ?? Cookie.get('ud') ?? '',
                    logExt,
                    placeholder,
                },
                options,
            );
    }
};

/**
 * 对 directShare 的二次封装
 * 1. 直接分享api，要处理未安装错误
 * 2. Cookie.get('ud') 作为默认 shareObjectId
 * 3. 默认的错误处理：toast
 */
export const directShare = async (params: {
    /** 分享对应业务，一个分享流程对应的唯一标识，分享后台配置 */
    subBiz: string;
    /** 分享渠道 */
    channel: ShareChannel;
    /** 标识一个分享内容唯一值，可以取user_id、shop_id 或者 photo_id 等 */
    shareObjectId?: string;
    /** 分享上报数据 */
    logExt?: Record<string, string>;
    /** 分享占位符 */
    placeholder?: Record<string, string>;

    /** 降级开关 */
    downgrade?: boolean;

    /** 自行处理未安装错误，默认是 toast */
    throwError?: boolean;
}) => {
    console.info('directShare: ', params);
    const { subBiz, channel, shareObjectId, logExt, placeholder, throwError, ...options } = params;

    try {
        return await _.directShare(
            {
                subBiz,
                channel,
                shareObjectId: shareObjectId ?? Cookie.get('ud') ?? '',
                logExt,
                placeholder,
            },
            options,
        );
    } catch (error) {
        if (throwError) throw error;
        if (_.isShareError(error)) {
            switch (error.code) {
                // 未安装错误是预期内的，可以 toast 处理
                case _.ShareErrorCode.WECHAT_NOT_INSTALLED:
                    toast('分享失败，未安装微信');
                    return { msg: '分享失败，未安装微信', error };
                case _.ShareErrorCode.QQ_NOT_INSTALLED:
                    toast('分享失败，未安装QQ');
                    return { msg: '分享失败，未安装QQ', error };
                default:
                    // 都不是的话，就直接抛出错误
                    throw error;
            }
        }
    }
};

/**
 * 按照 channels 顺序依次尝试拉起 微信、QQ、复制链接
 * 如果遇到未安装错误就跳过，拉下一个
 * 如果遇到其他错误就直接抛出
 */
export const shareChain = async (
    params: {
        /** 分享对应业务，一个分享流程对应的唯一标识，分享后台配置 */
        subBiz: string;
        /** 标识一个分享内容唯一值，可以取user_id、shop_id 或者 photo_id 等 */
        shareObjectId?: string;
        /** 分享上报数据 */
        logExt?: Record<string, any>;
        /** 分享占位符 */
        placeholder?: Record<string, string>;

        /** 降级开关 */
        downgrade?: boolean;
    },
    channels: ShareChannel[] = ['WECHAT', 'QQ', 'COPY_LINK'],
) => {
    console.info('shareChain: ', params);

    for (const channel of channels) {
        try {
            return await directShare({ channel, throwError: true, ...params });
        } catch (error) {
            console.log(`shareChain.${channel} err: `, error);
            if (
                _.isShareError(error) &&
                [_.ShareErrorCode.WECHAT_NOT_INSTALLED, _.ShareErrorCode.QQ_NOT_INSTALLED].includes(error.code)
            ) {
                continue; // 未安装错误，跳过
            }
            throw error;
        }
    }
};
