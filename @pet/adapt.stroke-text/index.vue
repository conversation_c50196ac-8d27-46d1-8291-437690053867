<script lang="ts">
export default {
    name: 'AdaptStrokeText',
};
</script>
<script lang="ts" setup>
interface Props {
    /**
     * 文字内容
     */
    text: string | number;
}
defineProps<Props>();
</script>

<template>
    <span class="stroke-text" :data-content="text">{{ text }}</span>
</template>

<style>
:root {
    /* 文字字体 */
    --adapt-stroke-text-font-family: initial;
    /* 文字字重 */
    --adapt-stroke-text-font-weight: 400;
    /* 文字字号 */
    --adapt-stroke-text-font-size: 17px;
    /* 文字行高 */
    --adapt-stroke-text-line-height: normal;
    /* 文字描边宽度 */
    --adapt-stroke-text-stroke-width: 3px;
    /* 文字颜色 */
    --adapt-stroke-text-color: #fe3666;
    /* 文字描边色 */
    --adapt-stroke-text-stroke-color: #fff;
    /* 文字倾斜角度 */
    --adapt-stroke-text-skew: matrix(1, 0, -0.21, 0.98, 0, 0);
    /* 文字间距 */
    --adapt-stroke-text-letter-spacing: 0;
}
</style>

<style lang="scss" scoped>
.stroke-text {
    font-family: var(--adapt-stroke-text-font-family), sans-serif;
    display: inline-flex;
    font-size: var(--adapt-stroke-text-font-size);
    line-height: var(--adapt-stroke-text-line-height);
    transform: var(--adapt-stroke-text-skew);
    letter-spacing: var(--adapt-stroke-text-letter-spacing);
    font-weight: var(--adapt-stroke-text-font-weight);
    margin-top: auto;
    position: relative;
    white-space: pre-wrap;

    &::before {
        content: attr(data-content);
        position: absolute;
        left: 0;
        top: 0;
        -webkit-text-stroke: var(--adapt-stroke-text-stroke-width) var(--adapt-stroke-text-stroke-color);
        color: var(--adapt-stroke-text-stroke-color);
        /* 这个作用是为了堵住一些文字描边的空隙如8这类的 */
        text-shadow:
            0.5px 0 currentColor,
            0 0.5px currentColor,
            -0.5px 0 currentColor,
            0 -0.5px currentColor;
    }

    &::after {
        content: attr(data-content);
        position: absolute;
        left: 0;
        top: 0;
        background: var(--adapt-stroke-text-color);
        background-clip: text;
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
    }
}
</style>
