import mockjs from 'mockjs';

const homeFEConstantsConfig = {
    // 页面设置
    frameConfig: {
        themeMap: {
            // 兜底主题，应对key没对应上的情况
            default: {
                // 主题色，状态栏颜色
                themeColor: '#f44f3c',
                // 头部背景遮罩主颜色，第一个颜色需要和主题色一致，不然会有断层
                headerBackMaskBackground: 'linear-gradient(180deg, #f44f3c 0%, #ff7840 20%, #fba169 62%, #0000 100%)',
                // 底部背景遮罩颜色
                footerBackMaskBackground: 'linear-gradient(180deg, #0000 0%, #ffdcbfe6 42%, #ffefd1 100%)',
                backgroundColor: '#FFDCBFE5',
            },
            jinghua: {
                themeColor: '#f44f3c',
                headerBackMaskBackground: 'linear-gradient(180deg, #f44f3c 0%, #ff7840 20%, #fba169 62%, #0000 100%)',
                footerBackMaskBackground: 'linear-gradient(180deg, #0000 0%, #ffdcbfe6 42%, #ffefd1 100%)',
                backgroundColor: '#FFDCBFE5',
            },
            xile: {
                themeColor: '#188BFF',
                headerBackMaskBackground: 'linear-gradient(180deg, #188BFF 0%, #57A7F7 20%, #98CBFF 62%, #0000 100%)',
                footerBackMaskBackground: 'linear-gradient(180deg, #0000 0%, #d4eaffe6 42%, #C7E4FF 100%)',
                backgroundColor: '#D4EAFFE5',
            },
            furao: {
                themeColor: '#BBC527',
                headerBackMaskBackground: 'linear-gradient(180deg, #BBC527 0%, #CFD83E 20%, #E2E796 67%, #0000 100%)',
                footerBackMaskBackground: 'linear-gradient(180deg, #0000 0%, #fdf9c8e6 42%, #EAE497 100%)',
                backgroundColor: '#EAE497',
            },
        },
    },
    // 花字提示
    flowerTip: {
        signed: '打卡成功',
        day2: '送你${0}步 冲!',
        day100: '最后一步 冲!',
    },
    // 主按钮文案
    mainButtonConfig: {
        leftSideButtonText: '赚现金',
        rightSideButtonText: '赚步数',
        noStepToast: '没有步数可以向前冲啦，明天再来吧',
        // 断签
        SIGN_INTERCEPTED: {
            buttonText: '立即补签',
            buttonSubText: '后不可补签',
            bubbleText: '你已断签啦',
            bubbleSubText: '快来拯救进度',
        },
        // 已签到
        SIGNED: {
            buttonText: '今日已打卡',
            newButtonText: '向前冲',
            buttonSubText: '剩余步数: ',
            duration: 3000, // 气泡展示的时长
            bubbleText: '今日打卡过了',
            bubbleSubText: '明天再来哦',
        },
        // 免签
        SIGN_FREE: {
            buttonText: '使用免任务卡',
            buttonSubText: '剩余步数: ',
            bubbleText: '一键使用!',
            bubbleSubText: '直接完成今日打卡',
        },
        // 未签到-有次数
        UN_SIGN: {
            buttonText: '向前冲',
            buttonSubText: '剩余步数: ',
            bubbleText: '再冲{0}完成打卡!',
        },
        OTHER: {
            buttonText: '重新挑战',
        },
    },
    // 建筑循环配置
    buildingInterval: 8,
    stationTagView: {
        noSigned: {
            first: '今日待打卡',
            second: '第{0}天',
            another: '第{0}天',
        },
        signed: {
            first: '第{0}天已打卡',
            second: '明日待打卡',
            another: '第{0}天',
        },
    },
    // 桌面图标配置
    shortcutConfig: {
        name: '百日心愿',
        id: 'summer25',
        bgColor: '#F2FCFF',
        btText: '打开活动页',
    },
    weakGuideConfig: {
        /** 站点奖励气泡 */
        GUIDE_REWARD: {},
        /** 引导赚步数 */
        GUIDE_RIGHT_BUTTON: {
            // did 纬度频控
            totalLimitCount: 30,
            // 消失时间
            // duration: 5000,
        },
        /** 引导组队 */
        GUIDE_TEAM: {
            // 每日频控
            everydayLimitCount: 100,
            // 消失时间
            duration: 10000,
        },
        /** 引导组队打卡 */
        GUIDE_CARD: {
            // 每日频控
            everydayLimitCount: 5,
            // 消失时间
            duration: 10000,
        },
    },
    // 挽留弹窗频控
    retainFrequency: {
        // 打卡挽留弹窗
        signRetainPopup: {
            dayTime: 3,
            allTime: 9999, // 无全周期频控
        },
        // 快捷方式挽留弹窗
        shortcutRetainPopup: {
            dayTime: 3,
            allTime: 99,
        },
        // 搜索挽留弹窗
        searchRetainPopup: {
            dayTime: 3,
            allTime: 99,
        },
    },
    // 新手引导配置
    beginnerConfig: {
        beginnerSelectProductPopup: {
            title: '打卡100天 白拿188元!',
            subTitle: '打卡100天必得现金',
            autoCloseTime: 0,
        },
        enhanceRewardPopup: {
            title: ['已坚持打卡${stationDay}天', '打卡100天白拿大奖'],
            autoCloseTime: 4000,
        },
    },
    inpushConfig: {
        // 兜底轮询间隔
        interval: 60000,
    },
    replaceProductConfirmText: {
        FIRST: '连续打卡100天必得',
        NORMAL: '更换后挑战进度将会清零哦测试',
        TEAM: '你还在队伍中，先退队吧！',
    },
    broadcastTag: {
        INTERCEPT: '打卡中断！',
        UN_SIGN: '今日待打卡',
        SIGNED: '今日已打卡',
    },
    broadcastText: {
        INTERCEPT: '最后通牒，即将痛失好礼！',
        DEFAULT: '查看我的打卡成就',
    },
    progressPointText: {
        INTERCEPT: '中断',
        DEFAULT: '第{0}天',
        TODAY: '今天',
        TARGET: '{0}天必得',
    },
    rewardTransPopupText: {
        title: '挑战成功',
        subTitle: '百天成就达成，速速发朋友圈收割膝盖',
    },
    renderGroup: 2,
    progressViewConf: {
        topText: {
            default: '今日待打卡 快向前冲吧',
            signed: '已打卡{0}天 明日继续',
            intercept: '打卡已中断！快去补打卡吧',
        },
    },
};

const homeUEConstantsConfig = {
    activityUrl: 'https://summer25.staging.kuaishou.com/home?layoutType=4',
    // searchContentIcon:
    //     'https://kcdn-w1.staging.kuaishou.com/kc/files/a/cny2025-server/warmup/home/<USER>/searchContentIcon.png',
    // mainTitleImg:
    //     'https://kcdn-w1.staging.kuaishou.com/kc/files/a/cny2025-server/warmup/home/<USER>/main-title.0b522789a6414813.png',

    // 活动规则页链接
    activityRuleUrl: 'https://baidu.com',
    // 钱包页链接
    walletUrl:
        'kwai://kds/react?bundleId=Cny2025RetentionKrn&componentName=wallet&biz=12749&themeStyle=1&entry_src=ks_cny_089',
    // 客服页链接
    customerServiceUrl:
        'https://csc-center.staging.kuaishou.com/help/index.html?enableWK=1&layoutType=4#/?entranceId=5579&environment=staging',

    // 桌面图标图片
    shortcutIcon: 'https://p4-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/retain-popup/icon.png',
    // 活动链接，桌面图标跳转需要
    shortcutUrl: 'https://summer25.staging.kuaishou.com/home?layoutType=4',
    // 添加桌面图标弹窗里面展示的图片
    shortcutContentIcon: 'https://p4-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/retain-popup/contentIcon.png',
    // ios快捷方式中间页背景
    shortcutBgImg:
        'https://p4-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/retain-popup/ios-shortcut-proxy-back.png',

    // 分享打卡天数
    shareNumber0: 'https://p4-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/share/num0.png',
    shareNumber1: 'https://p4-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/share/num1.png',
    shareNumber2: 'https://p4-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/share/num2.png',
    shareNumber3: 'https://p4-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/share/num3.png',
    shareNumber4: 'https://p4-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/share/num4.png',
    shareNumber5: 'https://p4-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/share/num5.png',
    shareNumber6: 'https://p4-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/share/num6.png',
    shareNumber7: 'https://p4-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/share/num7.png',
    shareNumber8: 'https://p4-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/share/num8.png',
    shareNumber9: 'https://p4-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/share/num9.png',

    // 弹窗快手logo
    sponsorLogo: 'https://p2-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/sponsor/kuaishouLogo.png',
};

const calendarEventViewList = [
    {
        title: '心愿大巴签到提醒',
        note: '打开快手进入活动页面http://ksurl.cn/J2-T1gw1',
        url: null,
        startDay: 1754879400000,
        endDay: 1758249000000,
        type: 1,
    },
    {
        title: '快去确认今日心愿打卡已完成，错过将与大奖失之交臂',
        note: '打开快手进入活动页面http://ksurl.cn/J2-T1gw1',
        url: null,
        startDay: 1754906400000,
        endDay: 1758276000000,
        type: 1,
    },
];

const progressAreaStationInfos = [
    {
        uniqueKey: 'beijing',
        stationName: '北京',
        stationIcon:
            'https://p2-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/sign-in/citylight/beijing_light.png?x-kcdn-pid=112543',
        stationBlackIcon:
            'https://p2-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/sign-in/citydark/beijing_dark.png?x-kcdn-pid=112543',
        chessStationIcon:
            'https://p2-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/sign-in/chess/beijing_chess.png?x-kcdn-pid=112543',
        llrewdIcon: null,
        stationThemeKey: 'jinghua',
        signed: false,
        stationDayIndex: 1,
        stationTotalStep: 7,
        stationBubbleIcon: null,
        normalBubbleText: '',
        stationBubbleText: '',
        tomorrowBubbleText: '',
        bubbleShowSeconds: 5000,
        gridSkinUrl: null,
        gridSkinSponsor: null,
        gridSkinLocation: [0],
        stationIconRewardText: '',
        stationIconRewardTextColor: '',
        newLlrewdIcon: null,
    },
    {
        uniqueKey: 'tianjin',
        stationName: '天津',
        stationIcon:
            'https://p2-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/sign-in/citylight/tianjin_light.png?x-kcdn-pid=112543',
        stationBlackIcon:
            'https://p2-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/sign-in/citydark/tianjin_dark.png?x-kcdn-pid=112543',
        chessStationIcon:
            'https://p2-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/sign-in/chess/tianjin_chess.png?x-kcdn-pid=112543',
        llrewdIcon:
            'https://p2-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/sign-in/reward/blackbox.png?x-kcdn-pid=112543',
        stationThemeKey: 'jinghua',
        signed: false,
        stationDayIndex: 2,
        stationTotalStep: 7,
        stationBubbleIcon:
            'https://p2-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/sign-in/reward/blackbox.png?x-kcdn-pid=112543',
        normalBubbleText: '最高{8888金币}',
        stationBubbleText: '今日打卡/n最高{8888金币}',
        tomorrowBubbleText: '明日打卡/n最高{8888金币}',
        bubbleShowSeconds: 5000,
        gridSkinUrl: null,
        gridSkinSponsor: null,
        gridSkinLocation: [2],
        stationIconRewardText: '8888',
        stationIconRewardTextColor: '#B97B00',
        newLlrewdIcon:
            'https://p2-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/sign-in/reward/coinBlack.png?x-kcdn-pid=112543',
    },
    {
        uniqueKey: 'shijiazhuang',
        stationName: '石家庄',
        stationIcon:
            'https://p2-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/sign-in/citylight/shijiazhuang_light.png?x-kcdn-pid=112543',
        stationBlackIcon:
            'https://p2-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/sign-in/citydark/shijiazhuang_dark.png?x-kcdn-pid=112543',
        chessStationIcon:
            'https://p2-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/sign-in/chess/shijiazhuang_chess.png?x-kcdn-pid=112543',
        llrewdIcon: null,
        stationThemeKey: 'jinghua',
        signed: false,
        stationDayIndex: 3,
        stationTotalStep: 7,
        stationBubbleIcon: null,
        normalBubbleText: '',
        stationBubbleText: '',
        tomorrowBubbleText: '',
        bubbleShowSeconds: 5000,
        gridSkinUrl: null,
        gridSkinSponsor: null,
        gridSkinLocation: [2],
        stationIconRewardText: '',
        stationIconRewardTextColor: '',
        newLlrewdIcon: null,
    },
    {
        uniqueKey: 'baoding',
        stationName: '保定',
        stationIcon:
            'https://p2-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/sign-in/citylight/baoding_light.png?x-kcdn-pid=112543',
        stationBlackIcon:
            'https://p2-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/sign-in/citydark/baoding_dark_new.png?x-kcdn-pid=112543',
        chessStationIcon:
            'https://p2-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/sign-in/chess/baoding_chess.png?x-kcdn-pid=112543',
        llrewdIcon:
            'https://p2-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/sign-in/reward/blackbox.png?x-kcdn-pid=112543',
        stationThemeKey: 'jinghua',
        signed: false,
        stationDayIndex: 4,
        stationTotalStep: 7,
        stationBubbleIcon:
            'https://p2-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/sign-in/reward/blackbox.png?x-kcdn-pid=112543',
        normalBubbleText: '最高{8888元}',
        stationBubbleText: '今日打卡/n最高{8888元}',
        tomorrowBubbleText: '明日打卡/n最高{8888元}',
        bubbleShowSeconds: 5000,
        gridSkinUrl: null,
        gridSkinSponsor: null,
        gridSkinLocation: [3],
        stationIconRewardText: '8888',
        stationIconRewardTextColor: '#00D164',
        newLlrewdIcon:
            'https://p2-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/sign-in/reward/cashBlack.png?x-kcdn-pid=112543',
    },
];

const chessboardStationList = [
    {
        stationInfo: {
            uniqueKey: 'beijing',
            stationName: '北京',
            stationIcon:
                'https://p2-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/sign-in/citylight/beijing_light.png?x-kcdn-pid=112543',
            stationBlackIcon:
                'https://p2-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/sign-in/citydark/beijing_dark.png?x-kcdn-pid=112543',
            chessStationIcon:
                'https://p2-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/sign-in/chess/beijing_chess.png?x-kcdn-pid=112543',
            llrewdIcon: null,
            stationThemeKey: 'jinghua',
            signed: false,
            stationDayIndex: 1,
            stationTotalStep: 7,
            stationBubbleIcon: null,
            normalBubbleText: '',
            stationBubbleText: '',
            tomorrowBubbleText: '',
            bubbleShowSeconds: 5000,
            gridSkinUrl: null,
            gridSkinSponsor: null,
            gridSkinLocation: [0],
            stationIconRewardText: '',
            stationIconRewardTextColor: '',
            newLlrewdIcon: null,
        },
        llrewdGridLayout: {
            LLCH_GRID: {
                gridIcon:
                    'https://p2-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/chessboard/llwd/LLCH.png?x-kcdn-pid=112543',
                gridLocation: [],
            },
            LLCN_GRID: {
                gridIcon:
                    'https://p2-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/chessboard/llwd/LLCN.png?x-kcdn-pid=112543',
                gridLocation: [1, 3, 5],
            },
        },
    },
    {
        stationInfo: {
            uniqueKey: 'tianjin',
            stationName: '天津',
            stationIcon:
                'https://p2-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/sign-in/citylight/tianjin_light.png?x-kcdn-pid=112543',
            stationBlackIcon:
                'https://p2-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/sign-in/citydark/tianjin_dark.png?x-kcdn-pid=112543',
            chessStationIcon:
                'https://p2-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/sign-in/chess/tianjin_chess.png?x-kcdn-pid=112543',
            llrewdIcon:
                'https://p2-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/sign-in/reward/blackbox.png?x-kcdn-pid=112543',
            stationThemeKey: 'jinghua',
            signed: false,
            stationDayIndex: 2,
            stationTotalStep: 7,
            stationBubbleIcon:
                'https://p2-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/sign-in/reward/blackbox.png?x-kcdn-pid=112543',
            normalBubbleText: '最高{8888金币}',
            stationBubbleText: '今日打卡/n最高{8888金币}',
            tomorrowBubbleText: '明日打卡/n最高{8888金币}',
            bubbleShowSeconds: 5000,
            gridSkinUrl: null,
            gridSkinSponsor: null,
            gridSkinLocation: [2],
            stationIconRewardText: '8888',
            stationIconRewardTextColor: '#B97B00',
            newLlrewdIcon:
                'https://p2-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/sign-in/reward/coinBlack.png?x-kcdn-pid=112543',
        },
        llrewdGridLayout: {
            LLCH_GRID: {
                gridIcon:
                    'https://p2-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/chessboard/llwd/LLCH.png?x-kcdn-pid=112543',
                gridLocation: [],
            },
            LLCN_GRID: {
                gridIcon:
                    'https://p2-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/chessboard/llwd/LLCN.png?x-kcdn-pid=112543',
                gridLocation: [1, 3, 5],
            },
        },
    },
    {
        stationInfo: {
            uniqueKey: 'shijiazhuang',
            stationName: '石家庄',
            stationIcon:
                'https://p2-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/sign-in/citylight/shijiazhuang_light.png?x-kcdn-pid=112543',
            stationBlackIcon:
                'https://p2-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/sign-in/citydark/shijiazhuang_dark.png?x-kcdn-pid=112543',
            chessStationIcon:
                'https://p2-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/sign-in/chess/shijiazhuang_chess.png?x-kcdn-pid=112543',
            llrewdIcon: null,
            stationThemeKey: 'jinghua',
            signed: false,
            stationDayIndex: 3,
            stationTotalStep: 7,
            stationBubbleIcon: null,
            normalBubbleText: '',
            stationBubbleText: '',
            tomorrowBubbleText: '',
            bubbleShowSeconds: 5000,
            gridSkinUrl: null,
            gridSkinSponsor: null,
            gridSkinLocation: [2],
            stationIconRewardText: '',
            stationIconRewardTextColor: '',
            newLlrewdIcon: null,
        },
        llrewdGridLayout: {
            LLCH_GRID: {
                gridIcon:
                    'https://p2-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/chessboard/llwd/LLCH.png?x-kcdn-pid=112543',
                gridLocation: [],
            },
            LLCN_GRID: {
                gridIcon:
                    'https://p2-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/chessboard/llwd/LLCN.png?x-kcdn-pid=112543',
                gridLocation: [1, 4, 6],
            },
        },
    },
    {
        stationInfo: {
            uniqueKey: 'baoding',
            stationName: '保定',
            stationIcon:
                'https://p2-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/sign-in/citylight/baoding_light.png?x-kcdn-pid=112543',
            stationBlackIcon:
                'https://p2-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/sign-in/citydark/baoding_dark_new.png?x-kcdn-pid=112543',
            chessStationIcon:
                'https://p2-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/sign-in/chess/baoding_chess.png?x-kcdn-pid=112543',
            llrewdIcon:
                'https://p2-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/sign-in/reward/blackbox.png?x-kcdn-pid=112543',
            stationThemeKey: 'jinghua',
            signed: false,
            stationDayIndex: 4,
            stationTotalStep: 7,
            stationBubbleIcon:
                'https://p2-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/sign-in/reward/blackbox.png?x-kcdn-pid=112543',
            normalBubbleText: '最高{8888元}',
            stationBubbleText: '今日打卡/n最高{8888元}',
            tomorrowBubbleText: '明日打卡/n最高{8888元}',
            bubbleShowSeconds: 5000,
            gridSkinUrl: null,
            gridSkinSponsor: null,
            gridSkinLocation: [3],
            stationIconRewardText: '8888',
            stationIconRewardTextColor: '#00D164',
            newLlrewdIcon:
                'https://p2-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/sign-in/reward/cashBlack.png?x-kcdn-pid=112543',
        },
        llrewdGridLayout: {
            LLCH_GRID: {
                gridIcon:
                    'https://p2-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/chessboard/llwd/LLCH.png?x-kcdn-pid=112543',
                gridLocation: [],
            },
            LLCN_GRID: {
                gridIcon:
                    'https://p2-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/chessboard/llwd/LLCN.png?x-kcdn-pid=112543',
                gridLocation: [1, 3, 6],
            },
        },
    },
    {
        stationInfo: {
            uniqueKey: 'handan',
            stationName: '邯郸',
            stationIcon:
                'https://p2-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/sign-in/citylight/handan_light.png?x-kcdn-pid=112543',
            stationBlackIcon:
                'https://p2-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/sign-in/citydark/handan_dark.png?x-kcdn-pid=112543',
            chessStationIcon:
                'https://p2-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/sign-in/chess/handan_chess.png?x-kcdn-pid=112543',
            llrewdIcon: null,
            stationThemeKey: 'jinghua',
            signed: false,
            stationDayIndex: 5,
            stationTotalStep: 7,
            stationBubbleIcon: null,
            normalBubbleText: '',
            stationBubbleText: '',
            tomorrowBubbleText: '',
            bubbleShowSeconds: 5000,
            gridSkinUrl: null,
            gridSkinSponsor: null,
            gridSkinLocation: [2],
            stationIconRewardText: '',
            stationIconRewardTextColor: '',
            newLlrewdIcon: null,
        },
        llrewdGridLayout: {
            LLCH_GRID: {
                gridIcon:
                    'https://p2-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/chessboard/llwd/LLCH.png?x-kcdn-pid=112543',
                gridLocation: [],
            },
            LLCN_GRID: {
                gridIcon:
                    'https://p2-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/chessboard/llwd/LLCN.png?x-kcdn-pid=112543',
                gridLocation: [1, 3, 5],
            },
        },
    },
];

const mockTeamExitPopup = {
    popupType: 'TEAM_EXIT',
    subTitle: '队伍解散',
    message: '好友断签，队伍解散\n别担心，你的个人打卡天数不受影响！',
    mainButton: '我知道了',
    toast: '队友退队',
    type: 1,
    reasonType: 1,
};

const mockTeamSuccessPopup = {
    popupType: 'TEAM_SUCCESS',
    animationTitle: '组队成功',
    animationDesc: '全员连续打卡7天得直通卡 1人断签即失败',
    icon: 'https://ali.a.yximgs.com/kos/nlav12119/jTyADRou_2025-05-07-15-43-46.png',
    mainButton: {
        linkType: 'GO_TEAM',
        linkText: '开启组队打卡',
    },
};

const mockReceiveTaskFreeCardPopup = {
    popupType: 'TEAM_SIGN_REWARD',
    title: '恭喜获得7张直通卡',
    desc: '每天无需任务，点击即可打卡',
    icon: 'https://p4-plat.wskwai.com/kcdn/cdn-kcdn112543/wishTravel/team/freeCardImg.png?x-kcdn-pid=112543',
    mainButton: {
        linkType: 'CLOSE',
        linkText: '开心收下',
    },
};

const mockResumePopup = {
    popupType: 'HUGE_SIGN_IN_RESUME',
    title: '可任选一方式完成续签',
    subTitle: '04月05日未打卡',
    financeMethod: {
        title: '使用70.19元现金+1.28活动余额',
        subTitle: '现金余额:2.5元 金币余额:158',
        cornerText: '1.使用现金余额及金币续签',
        buttonText: '确认使用',
        hasBadgeIcon: false,
        resumeType: 2,
        continuePay: false,
        cashAmount: 19,
        coinAmount: 100,
    },
    shareAssistMethod: {
        title: '邀请2位好友(0/2)',
        subTitle: '好友每天仅可助力一次',
        cornerText: '2.邀人免费续签',
        buttonText: '去邀请',
        hasBadgeIcon: true,
        subBiz: 'NEBULA_SIGNIN',
        hugeSignInShareToken: 'NnO2iAs02K_mNR8Dzv85hUmvaDw2haqH',
    },
    signInResumeMethod: {
        icon: 'https://h1.static.yximgs.com/kos/nlav10721/chrome-plugin-upload/2025-07-08/1751968698367.8cce14d36a2593cf.png', //图标
        subTitle: '恭喜你抽中幸运补签，点击补签按钮即可完成补签',
        buttonText: '一键补打卡',
        resumeType: 7, //签到类型
    },
};

const mockResumeExpirePopup = {
    sponsorLogo: null,
    sponsorText: null,
    popupType: 'FINAL_RECEIVE_EXPIRE',
    title: '很遗憾 挑战失败',
    subTitle: '断签天数超过5天',
    userId: 0,
    activityId: null,
    mainButton: {
        linkType: 'NEW_ROUND_SIGN',
        linkText: '重新挑战',
        linkSubText: null,
        linkUrl: null,
        icon: null,
    },
    subButton: null,
    taskId: 0,
    sortScore: 0,
    hashKeys: [],
    desc: null,
    icon: null,
};

const mockOldUserTipPopup1 = {
    sponsorLogo: null,
    sponsorText: null,
    popupType: 'OLD_USER_TIP',
    title: null,
    subTitle: '天降福利 玩法升级',
    userId: 2198474502,
    activityId: 'summer25Main_t2',
    mainButton: {
        linkType: 'CLOSE',
        linkText: '我知道了',
        linkSubText: null,
        linkUrl: null,
        icon: null,
    },
    coinPopup: true,
    subButton: null,
    taskId: 0,
    sortScore: 0,
    hashKeys: [],
    desc: '无需步数即可向前冲，快去试试吧～',
    icon: 'https://p4-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/sign-in/olduser/oldUserIcon.png',
};

const mockBeginnerGuidePopup = {
    sponsorLogo: null,
    sponsorText: null,
    popupType: 'BEGINNER_GUIDE',
    title: '',
    subTitle: '',
    userId: 2184733160,
    activityId: 'summer25Main_t2',
    mainButton: null,
    subButton: null,
    taskId: 0,
    sortScore: 0,
    hashKeys: [],
    desc: null,
    icon: null,
};

const mockLLCHTaskPopup = {
    sponsorLogo:
        'https://p2-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/sponsor/kuaishouLogo.png?x-kcdn-pid=112543',
    sponsorText: '快手',
    popupType: 'COMMON_LLCH_TASK',
    title: '任务成功完成',
    subTitle: '开红包啦',
    userId: 2184733160,
    activityId: 'summer25Main_t2',
    mainButton: {
        linkType: 'CLOSE',
        linkText: '开心收下',
        linkSubText: null,
        linkUrl: null,
        icon: null,
    },
    subButton: null,
    taskId: 16858,
    sortScore: *********,
    hashKeys: ['28_*********'],
    desc: null,
    icon: null,
    llrewdId: 45232,
    titleContext: null,
    blessing: '红包一开，笑口常开',
    llpeDetail: [
        {
            llpeType: 'LLCN',
            amount: 88,
            displayAmount: '88',
            displayUnit: '金币',
            openSubTitle: '快手送你金币',
            bottomDesc: null,
        },
    ],
    userIdentity: null,
    bottomInfo: {
        bottomDesc: '已存入「我的钱包」',
        bottomButton: {
            linkType: 'KWAI_LINK',
            linkText: '去查看',
            linkSubText: null,
            linkUrl:
                'kwai://kds/react?bundleId=Cny2025RetentionKrn&componentName=wallet&biz=13627&themeStyle=1&entry_src=entry_src',
            icon: null,
        },
    },
};

const mockFinalReceivePopup = {
    sponsorLogo:
        'https://kcdn-w1.staging.kuaishou.com/kc/files/a/cny2025-server/warmup/sponsor/kuaishou.png?x-ks-ptid=0',
    sponsorText: '快手',
    popupType: 'FINAL_RECEIVE_LLAWD',
    title: null,
    subTitle: '挑战成功',
    userId: 0,
    activityId: null,
    mainButton: {
        linkType: 'FINAL_LLRWD',
        linkText: '立即领取',
        linkSubText: null,
        linkUrl: null,
        icon: null,
    },
    subButton: null,
    taskId: 0,
    sortScore: 0,
    hashKeys: [],
    desc: '太不容易啦，奖品到手！',
    icon: 'https://p2-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/product/iphone16.png?x-kcdn-pid=112543',
    llwrdName: '电商测试商品2',
    productId: 10,
};

const mockFinalReceiveExpirePopup = {
    sponsorLogo: null,
    sponsorText: null,
    popupType: 'FINAL_RECEIVE_EXPIRE',
    title: null,
    subTitle: '很遗憾 奖品已过期',
    userId: 0,
    activityId: null,
    mainButton: {
        linkType: 'NEW_ROUND_SIGN',
        linkText: '重新挑战',
        linkSubText: null,
        linkUrl: null,
        icon: null,
    },
    subButton: null,
    taskId: 0,
    sortScore: 0,
    hashKeys: [],
    desc: '别灰心，再来一次你还行！',
    icon: null,
    llwrdName: null,
    productId: 1001,
};

const mockInpushTaskReceivePopup = {
    sponsorLogo:
        'https://p2-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/sponsor/kuaishouLogo.png?x-kcdn-pid=112543',
    sponsorText: '快手',
    popupType: 'COMMON_LLCH_TASK',
    title: '订阅提醒成功',
    subTitle: '开红包啦',
    userId: 2184733160,
    activityId: 'summer25Main_t2',
    mainButton: {
        linkType: 'CLOSE',
        linkText: '开心收下',
        linkSubText: null,
        linkUrl: null,
        icon: null,
    },
    subButton: null,
    taskId: 17482,
    sortScore: *********,
    hashKeys: ['28_*********'],
    desc: null,
    icon: null,
    llrewdId: 44707,
    titleContext: {
        desc: '明日喊你来打卡！',
    },
    blessing: '红包一开，笑口常开',
    llpeDetail: [
        {
            llpeType: 'LLCN',
            amount: 126,
            displayAmount: '126',
            displayUnit: '金币',
            openSubTitle: '获得金币',
            bottomDesc: null,
        },
    ],
    userIdentity: null,
    bottomInfo: {
        bottomDesc: '已存入「我的钱包」',
        bottomButton: {
            linkType: 'KWAI_LINK',
            linkText: '去查看',
            linkSubText: null,
            linkUrl:
                'kwai://kds/react?bundleId=Cny2025RetentionKrn&componentName=wallet&biz=13627&themeStyle=1&entry_src=entry_src',
            icon: null,
        },
    },
};

const mockCustomLLCHPopup = {
    sponsorLogo:
        'https://p2-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/sponsor/kuaishouLogo.png?x-kcdn-pid=112543',
    sponsorText: '快手',
    popupType: 'COMMON_LLCH_TASK',
    title: '任务成功完成',
    subTitle: '开红包啦',
    userId: 2184733160,
    activityId: 'summer25Main_t2',
    mainButton: {
        linkType: 'CLOSE',
        linkText: '开心收下',
        linkSubText: null,
        linkUrl: null,
        icon: null,
    },
    subButton: null,
    taskId: 14818,
    sortScore: *********,
    hashKeys: ['28_*********'],
    desc: null,
    icon: null,
    llrewdId: 44707,
    titleContext: null,
    blessing: '红包一开，笑口常开',
    llpeDetail: [
        {
            llpeType: 'LLCN',
            amount: 126,
            displayAmount: '126',
            displayUnit: '金币',
            openSubTitle: '快手送你金币',
            bottomDesc: null,
        },
    ],
    userIdentity: null,
    bottomInfo: {
        bottomDesc: '已存入「我的钱包」',
        bottomButton: {
            linkType: 'KWAI_LINK',
            linkText: '去查看',
            linkSubText: null,
            linkUrl:
                'kwai://kds/react?bundleId=Cny2025RetentionKrn&componentName=wallet&biz=13627&themeStyle=1&entry_src=entry_src',
            icon: null,
        },
    },
};

const mockPopList = [
    // 组队：队伍解散
    mockTeamExitPopup,
    // 组队：组队成功
    mockTeamSuccessPopup,
    // 组队：获得面前卡
    mockReceiveTaskFreeCardPopup,
    // 续签
    mockResumePopup,
    // 彻底断签
    mockResumeExpirePopup,
    // 无步数引导
    mockOldUserTipPopup1,
    // 新手引导
    mockBeginnerGuidePopup,
    // 任务成功完成
    mockLLCHTaskPopup,
    // 最终大奖未领取
    mockFinalReceivePopup,
    // 最终大奖过期
    mockFinalReceiveExpirePopup,
    // inpush提醒任务完成奖励
    mockInpushTaskReceivePopup,
    // 常见任务奖励
    mockCustomLLCHPopup,
];

const getRandom = (arr) => arr[Math.floor(Math.random() * arr.length)];

const getPop = () => {
    if (getRandom([false, false])) {
        return getRandom(mockPopList);
    }
    return false;
};

const mockCurrentGridTaskDetail = {
    commonTaskDetail: {
        taskId: 17274,
        title: '观看3个广告（0/3）',
        description: '完成任务，即可通关',
        iconUrls: [''],
        completeConditionAmount: 1,
        completedAmount: 0,
        completeMaxTimes: 3,
        completedTimes: 0,
        taskStatus: 'COMPLETING_TASK',
        displayText: '去完成',
        jumpLink: 'feed.rewardVideoTask',
        jumpType: 'adIncentiveVideo',
        prizeName: '',
        prizeCount: 0,
        prizeId: 0,
        extParams: {},
        completeToastText: '',
        timeLimitedStart: 0,
        timeLimitedEnd: 0,
        timeLimitExpireTime: 0,
        timeLimitedType: 0,
        takeTime: 1751035234206,
        delayQueryStatus: false,
        widgetParam:
            'eyJ3aWRnZXRQcm9ncmVzc0JhY2tncm91bmRDb2xvciI6IiNGRjdENDEiLCJkaXNhcHBlYXJTZWNvbmRzTGF0ZXJDb21wbGV0ZWQiOjAsInJlc3RyaWN0aXZlVXNlcklkcyI6W10sIndpZGdldEljb25VcmwiOiIiLCJkaXNhYmxlU3RvcmVQcm9ncmVzcyI6ZmFsc2UsIndpZGdldERlc2NDb2xvciI6IiNGRkU4Q0EiLCJhZFZpZGVvU3ViUGFnZUlkIjoiMTAwMDI2OTc4Iiwid2lkZ2V0RW5hYmxlU2hvdyI6dHJ1ZSwidGFyZ2V0Q291bnQiOjEsIndpZGdldEluUHJvZ3Jlc3NMaW5rVXJsIjoiIiwid2lkZ2V0Q29tcGxldGVGb3JlZ3JvdW5kSWNvblVybCI6IiIsImFkVmlkZW9UeXBlIjozLCJzZWN0aW9uSW50ZXJ2YWxzIjpbXSwiY3VycmVudENvdW50IjowLCJiaXpJZCI6MjAwMDAwMDAwLCJzaG93UGFnZXNTdHJpbmciOltdLCJ3aWRnZXRDYWxsQmFja1VybCI6IiIsIndpZGdldEFuaW1hdGlvbkZyYW1lUE1zIjozMCwiZWZmZWN0UGFnZXMiOltdLCJ3aWRnZXREZXNjIjoi5YCS6K6h5pe2Iiwid2lkZ2V0RGVzY0ljb25VcmwiOiIiLCJyZXN0b3JlUGFnZXNTdHJpbmciOltdLCJ3aWRnZXRFeHBpcmVUaW1lIjoxNzU4OTAyMzk5MDAwLCJ3aWRnZXRTdHlsZSI6MiwidGFza1Rva2VuIjoid1lFeXBEM1pubk5pQUVNcFdUbVczUW1iSnRYcUh1R0siLCJldmVudElkIjoid2lkZ2V0X2Jyb3dzZV9iZWhpbmRfZGV0YWlsX3BhZ2UiLCJjdXJyZW50UGVyaW9kSW5kZXgiOjEsImV2ZW50Q291bnRUeXBlIjoxLCJhZFZpZGVvVG9hc3REZXNjIjoi5YaN55yLJHswfeenku-8jOWPr-iOt-W-l-WlluWKsSIsIndpZGdldFByb2dyZXNzQmFyQ29sb3IiOiIjRkZGRkZGIiwiZWZmZWN0UGFnZXNTdHJpbmciOltdLCJidWJibGVUb2FzdCI6IuS7u-WKoeWujOaIkCIsImFkVmlkZW9QYWdlSWQiOiIxMDAwMTEyNTEiLCJhZFZpZGVvVG9hc3RJbWFnZVVybCI6Imh0dHBzOi8vcDEtcHJvLmEueXhpbWdzLmNvbS9rb3MvbmxhdjExMDY2L2VuY291cmFnZS0xNjQyMzAzNDU3MDM1LW9hTGxhay5wbmciLCJ3aWRnZXRDaGVja1ZhbHVlIjoxLCJhZExpdmVFbnRyeU1peGVkIjp0cnVlLCJ0b2FzdCI6IiIsInNob3dQYWdlcyI6W10sIndpZGdldEluaXRpYWxQb3NpdGlvbiI6eyJzaWRlIjowLCJ5IjoyNTV9LCJ3aWRnZXRBbmltYXRpb25SZXNvdXJjZVVybCI6IiIsInJlc3RvcmVQYWdlcyI6W10sIndpZGdldENvbXBsZXRlSWNvblVybCI6IiJ9',
        taskShowStyle: null,
        apkName: '',
        apkAddr: '',
        iosStoreAddr: '',
        iosSchema: '',
        shareSubBiz: '',
        unsupportedToast: '',
        taskPriority: 100,
        taskContentId: '完成任务，即可通关',
        taskToken: 'KP2fF4ynEpKEMnnmXdQr3QOOSkiJkgDIVw9eXcDUF3U',
        subBizId: 13958,
        takeType: 'MANUAL_TAKE',
        takeStatus: 'TAKED',
        assistUserInfo: null,
        relationChainInfo: [],
        relationChainTypes: null,
        prizeIconUrls: [],
        reservationCalendarConfig: {
            remindPeriod: 0,
            scheduleStartTimeStamp: 0,
            scheduleEndTimeStamp: 0,
            calendarTitle: '',
            calendarDescription: '',
            kwaiLink: '',
            nebulaLink: '',
            dailyTimeStampList: [],
        },
        dynamicExtParam: {},
        titleCornerText: '',
        descriptionLabel: '',
        taskPropertyKey: '',
        backgroundColor: '',
        backgroundUrl: '',
        titleCornerUrl: '',
        bannerUrl: '',
        cornerBubbleText: '',
        pendantIconUrl: '',
    },
};

const ifShowCurrentGridTaskDetail = (mockData, pop) => {
    const conflictPopupType = [
        'BEGINNER_GUIDE',
        'HUGE_SIGN_IN_RESUME',
        'FINAL_RECEIVE_EXPIRE',
        'FINAL_RECEIVE_LLAWD',
        'FINAL_RECEIVE_EXPIRE',
    ];

    if (conflictPopupType.includes(pop.popupType)) {
        console.log(`派发${pop.popupType} 不下发挑战半屏`);
        return false;
    }
    console.log(`派发${pop?.popupType ?? '无'} 下发挑战半屏`);
    return mockData.hasGridTask;
};

const gen = () => {
    // 从 chess/move/data.json 文件中读取公共信息
    const mockData = require('../../chess/move/data.json');

    // TODO: 需注释，恢复初始位置
    mockData.currentStep = mockData.initCurrentStep;

    const pop = getPop();
    const showCurrentGridTaskDetail = ifShowCurrentGridTaskDetail(mockData, pop);

    const signed = mockData.currentStep === mockData.expectTotalStep;

    return {
        result: 1,
        data: {
            toast: '测试一下这玩意',
            signInHomeView: {
                progressArea: {
                    stationInfos: progressAreaStationInfos,
                    broadCastInfo: {
                        todaySignIn: true,
                        nextLLrewdSignInDays: 1,
                        llrewdName: '神秘奖励',
                    },
                    todayIndex: mockData.todayIndex,
                    todayStationInfo: {
                        uniqueKey: 'beijing',
                        stationName: '北京',
                        stationIcon:
                            'https://p4-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/sign-in/city/beijing.png',
                        chessStationIcon:
                            'https://p4-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/chessboard/station/beijing.png?x-kcdn-pid=112543',
                        llrewdIcon: '',
                        stationThemeKey: 'jinghua',
                        signed,
                        stationDayIndex: 1,
                        stationTotalStep: mockData.stationTotalStep,
                        stationBubbleIcon: null,
                        stationBubbleText: '',
                        tomorrowBubbleText: null,
                        bubbleShowSeconds: 0,
                        gridSkinUrl:
                            'https://p4-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/chessboard/skin/skin.png?x-kcdn-pid=112543',
                    },
                    needSignInDays: mockData.needSignInDays,
                },
                product: {
                    productId: 1001,
                    productName: '电商测试商品2',
                    productIcon: 'https://p4-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/product/iphone16.png',
                    signInDays: 10,
                },
                eventId: '107#108',
                calendarEventViewList,
                signInDays: 0,
            },
            todaySigned: false,
            signInStatus: 'PROCESSING',
            chessboard: {
                progress: {
                    currentGridTaskDetail: showCurrentGridTaskDetail ? mockCurrentGridTaskDetail : null,
                    currentStep: mockData.currentStep,
                    expectTotalStep: mockData.expectTotalStep,
                    signed,
                    lastStation: mockData.stationDayIndex === mockData.needSignInDays,
                },
                buildingInfos: [
                    {
                        type: 'guide',
                        name: 'bussiness',
                        iconUrl:
                            'https://p4-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/chessboard/building/building.png?x-kcdn-pid=112543',
                        title: 'string',
                        desc: 'string',
                        linkUrl: 'https://summer25.staging.kuaishou.com/home?layoutType=4&_vconsole=1',
                        linkType: 'JUMP_H5',
                        pos: [4],
                    },
                ],
                stationHotInfoViews: {
                    stationDesc: '北京新鲜事',
                    hotInfos: [
                        {
                            title: '你好',
                            linkUrl: 'ksnebula://home/<USER>',
                        },
                        {
                            title: '你真好',
                            linkUrl: 'ksnebula://home/<USER>',
                        },
                    ],
                    liveTime: 102000,
                },
                userInfo: {
                    userName: '快手用户1747819956470',
                    headUrl:
                        'https://kcdn-w1.staging.kuaishou.com/BMjAyNTA2MjMxNDU1MTBfMjE5ODg0MTU0Ml8yX2hkODU1XzUw_s.jpg',
                },
                stationList: chessboardStationList,
                currentStationIndex: mockData.currentStationIndex,
                initStationCount: 5,
            },
            mainButton: {
                rushCount: 0,
                reSignInCountdownTime: 0,
                stationDayIndex: mockData.stationDayIndex,
                hasValidSignFreeCard: mockData.hasValidSignFreeCard,
                systemGivingRushChance: 0,
            },
            titleInfo: {
                logo: 'https://p4-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/home/<USER>',
                title: '百天心愿之旅',
                subTitle: '打卡10天必得终点豪礼',
            },
            accountModel: {
                total: '0.00',
                unit: '元',
            },
            homeUEConstantsConfig,
            homeFEConstantsConfig,
            homeResourceMap: {
                RESOURCE_LEFT_BOTTOM: {
                    id: 0,
                    labelDesc: '活动B',
                    title: '活动B',
                    icon: null,
                    iconText:
                        'https://p4-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/home/<USER>',
                    linkType: 'JUMP_H5',
                    linkUrl:
                        'https://sf2025.m.kuaishou.cn/warmup/cards?layoutType=4&bizId=25cny-warmup&entry_src=ks_cny_082&softInputMode=50&pad_h5_promote_disable=true',
                },
                RESOURCE_RIGHT_BOTTOM: {
                    id: 0,
                    labelDesc: '集福卡',
                    title: '集福卡',
                    icon: null,
                    iconText:
                        'https://p4-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/home/<USER>',
                    linkType: 'JUMP_H5',
                    linkUrl:
                        'https://sf2025.m.kuaishou.cn/warmup/cards?layoutType=4&bizId=25cny-warmup&entry_src=ks_cny_082&softInputMode=50&pad_h5_promote_disable=true',
                },
                RESOURCE_RIGHT_TOP: {
                    id: 0,
                    labelDesc: '活动D',
                    title: '活动D',
                    icon: null,
                    iconText:
                        'https://p4-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/home/<USER>',
                    linkType: 'JUMP_H5',
                    linkUrl:
                        'https://sf2025.m.kuaishou.cn/warmup/cards?layoutType=4&bizId=25cny-warmup&entry_src=ks_cny_082&softInputMode=50&pad_h5_promote_disable=true',
                },
                RESOURCE_LEFT_TOP: {
                    id: 0,
                    labelDesc: '活动A',
                    title: '活动A',
                    icon: null,
                    iconText:
                        'https://p4-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/home/<USER>',
                    linkType: 'JUMP_H5',
                    linkUrl:
                        'https://sf2025.m.kuaishou.cn/warmup/cards?layoutType=4&bizId=25cny-warmup&entry_src=ks_cny_082&softInputMode=50&pad_h5_promote_disable=true',
                },
            },
            popList: [],
            // 新·新手引导
            // popList: [{ popupType: 'BEGINNER_SELECT_PRODUCT_POPUP' }],
            // popList: [{ popupType: 'ENHANCE_REWARD_POPUP' }],
            needSig3Path: [
                '/rest/wd/summer25/wishTravel/hugeSignIn/selectProduct',
                '/rest/wd/summer25/wishTravel/hugeSignIn/finalLlrewd',
                '/rest/wd/summer25/wishTravel/hugeSignIn/resume',
                '/rest/wd/summer25/wishTravel/hugeSignIn/abandon',
                '/rest/wd/summer25/wishTravel/hugeSignIn/pay/cancel',
                '/rest/wd/summer25/wishTravel/llwdwc/sign',
                '/rest/n/summer2025/wish/travel/llwdwc/sign',
                '/rest/nebula/summer2025/wish/travel/llwdwc/sign',
            ],
            degradeConfigView: {
                cdnLevel: 3,
                animationLevel: 0,
                videoLevel: 0,
                liveLevel: 0,
                audioLevel: 0,
                transparentVideoLevel: 2,
            },
            teamEntryView: {
                entryStatus: 1,
                newEntryStatus: 1,
                exitTeamGuide: false,
                freeCardImg:
                    'https://p4-plat.wskwai.com/kcdn/cdn-kcdn112543/wishTravel/team/freeCardImg.png?x-kcdn-pid=112543',
                teamUser: [
                    {
                        userId: 2198854870,
                        nickName: '快手用户1747819956470',
                        userAvatar: 'https://p4-plat.wskwai.com/kos/nlav12689/head.jpg',
                        todaySign: false,
                        currentUser: true,
                    },
                ],
                newShowPop: true,
                popType: 1,
                number: 7,
                freeCardListTitle: '直通卡',
                freeCardListSubTitle: '使用直通卡可以直接打卡哦',
                freeCardList: [
                    {
                        cardName: '直通卡',
                        cardImg:
                            'https://p4-plat.wskwai.com/kcdn/cdn-kcdn112543/wishTravel/team/freeCardImg.png?x-kcdn-pid=112543',
                        validTimeStr: '05.30 00:00:00-05.30 23:59:59',
                        buttonType: 1,
                    },
                    {
                        cardName: '直通卡',
                        cardImg:
                            'https://p4-plat.wskwai.com/kcdn/cdn-kcdn112543/wishTravel/team/freeCardImg.png?x-kcdn-pid=112543',
                        validTimeStr: '05.30 00:00:00-05.30 23:59:59',
                        buttonType: 2,
                    },
                    {
                        cardName: '直通卡',
                        cardImg:
                            'https://p4-plat.wskwai.com/kcdn/cdn-kcdn112543/wishTravel/team/freeCardImg.png?x-kcdn-pid=112543',
                        validTimeStr: '05.30 00:00:00-05.30 23:59:59',
                        buttonType: 3,
                    },
                    {
                        cardName: '直通卡',
                        cardImg:
                            'https://p4-plat.wskwai.com/kcdn/cdn-kcdn112543/wishTravel/team/freeCardImg.png?x-kcdn-pid=112543',
                        validTimeStr: '05.30 00:00:00-05.30 23:59:59',
                        buttonType: 4,
                    },
                    {
                        cardName: '直通卡',
                        cardImg:
                            'https://p4-plat.wskwai.com/kcdn/cdn-kcdn112543/wishTravel/team/freeCardImg.png?x-kcdn-pid=112543',
                        validTimeStr: '05.30 00:00:00-05.30 23:59:59',
                        buttonType: 5,
                    },
                ],
            },
            refreshTime: 15000,
            chessStepSwitch: true,
        },
        timestamp: 1747826519256,
        hostname: 'public-xm-c34-kce-node-staging37.idchb1az1.hb1.kwaidc.com',
    };
};

// 注释掉下面这行代码，则会禁用当前接口的 mock
// export const disable = true;
export default () =>
    mockjs.mock({
        result: 1,
        data: {
            signInHomeView: {
                progressArea: {
                    stationInfos: [
                        {
                            uniqueKey: 'tianjin',
                            stationName: '天津',
                            stationIcon:
                                'https://p4-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/sign-in/citylight/tianjin_light.png?x-kcdn-pid=112543',
                            stationBlackIcon:
                                'https://p4-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/sign-in/citydark/tianjin_dark.png?x-kcdn-pid=112543',
                            chessStationIcon:
                                'https://p4-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/sign-in/chess/tianjin_chess.png?x-kcdn-pid=112543',
                            llrewdIcon:
                                'https://p4-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/sign-in/reward/cash.png?x-kcdn-pid=112543',
                            stationThemeKey: 'jinghua',
                            signed: false,
                            stationDayIndex: 2,
                            stationTotalStep: 7,
                            stationBubbleIcon:
                                'https://p4-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/sign-in/reward/cash.png?x-kcdn-pid=112543',
                            normalBubbleText: '必得{0.1现金}',
                            stationBubbleText: '今日打卡/n必得{0.1现金}',
                            tomorrowBubbleText: '明日打卡/n必得{0.1现金}',
                            bubbleShowSeconds: 5000,
                            gridSkinUrl: null,
                            gridSkinSponsor: null,
                            gridSkinLocation: [2],
                            stationIconRewardText: '0.1',
                            stationIconRewardTextColor: '#00D164',
                            newLlrewdIcon:
                                'https://p4-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/sign-in/reward/cashWhite.png?x-kcdn-pid=112543',
                            cityTourismIdList: [2197871363, 2198176427, 2198176428],
                            roamingCityId: 120100,
                        },
                        {
                            uniqueKey: 'shijiazhuang',
                            stationName: '石家庄',
                            stationIcon:
                                'https://p4-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/sign-in/citylight/shijiazhuang_light.png?x-kcdn-pid=112543',
                            stationBlackIcon:
                                'https://p4-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/sign-in/citydark/shijiazhuang_dark.png?x-kcdn-pid=112543',
                            chessStationIcon:
                                'https://p4-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/sign-in/chess/shijiazhuang_chess.png?x-kcdn-pid=112543',
                            llrewdIcon: null,
                            stationThemeKey: 'yangfan',
                            signed: false,
                            stationDayIndex: 3,
                            stationTotalStep: 7,
                            stationBubbleIcon: null,
                            normalBubbleText: '',
                            stationBubbleText: '',
                            tomorrowBubbleText: '',
                            bubbleShowSeconds: 5000,
                            gridSkinUrl: null,
                            gridSkinSponsor: null,
                            gridSkinLocation: [2],
                            stationIconRewardText: '',
                            stationIconRewardTextColor: '',
                            newLlrewdIcon: null,
                            cityTourismIdList: [2197871363, 2198176427, 2198176428],
                            roamingCityId: 130100,
                        },
                        {
                            uniqueKey: 'baoding',
                            stationName: '保定',
                            stationIcon:
                                'https://p4-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/sign-in/citylight/baoding_light.png?x-kcdn-pid=112543',
                            stationBlackIcon:
                                'https://p4-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/sign-in/citydark/baoding_dark_new.png?x-kcdn-pid=112543',
                            chessStationIcon:
                                'https://p4-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/sign-in/chess/baoding_chess.png?x-kcdn-pid=112543',
                            llrewdIcon:
                                'https://p4-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/sign-in/reward/blackbox.png?x-kcdn-pid=112543',
                            stationThemeKey: 'jinghua',
                            signed: false,
                            stationDayIndex: 4,
                            stationTotalStep: 7,
                            stationBubbleIcon:
                                'https://p4-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/sign-in/reward/blackbox.png?x-kcdn-pid=112543',
                            normalBubbleText: '最高{8888金币}',
                            stationBubbleText: '今日打卡/n最高{8888金币}',
                            tomorrowBubbleText: '明日打卡/n最高{8888金币}',
                            bubbleShowSeconds: 5000,
                            gridSkinUrl: null,
                            gridSkinSponsor: null,
                            gridSkinLocation: [3],
                            stationIconRewardText: '8888',
                            stationIconRewardTextColor: '#B97B00',
                            newLlrewdIcon:
                                'https://p4-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/sign-in/reward/coinBlack.png?x-kcdn-pid=112543',
                            cityTourismIdList: [2197871363, 2198176427, 2198176428],
                            roamingCityId: 0,
                        },
                        {
                            uniqueKey: 'cangzhou',
                            stationName: '沧州',
                            stationIcon:
                                'https://p4-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/sign-in/citylight/cangzhou_light.png?x-kcdn-pid=112543',
                            stationBlackIcon:
                                'https://p4-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/sign-in/citydark/cangzhou_dark.png?x-kcdn-pid=112543',
                            chessStationIcon:
                                'https://p4-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/sign-in/chess/cangzhou_chess.png?x-kcdn-pid=112543',
                            llrewdIcon:
                                'https://p4-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/sign-in/reward/blackbox.png?x-kcdn-pid=112543',
                            stationThemeKey: 'jinghua',
                            signed: false,
                            stationDayIndex: 7,
                            stationTotalStep: 0,
                            stationBubbleIcon:
                                'https://p4-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/sign-in/reward/blackbox.png?x-kcdn-pid=112543',
                            normalBubbleText: '抽{888元红包}',
                            stationBubbleText: '今日打卡/n抽{888元红包}',
                            tomorrowBubbleText: '明日打卡/n抽{888元红包}',
                            bubbleShowSeconds: 5000,
                            gridSkinUrl: null,
                            gridSkinSponsor: null,
                            gridSkinLocation: [],
                            stationIconRewardText: '888',
                            stationIconRewardTextColor: '#00D164',
                            newLlrewdIcon:
                                'https://p4-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/sign-in/reward/cashBlack.png?x-kcdn-pid=112543',
                            cityTourismIdList: [2197871363, 2198176427, 2198176428],
                            roamingCityId: 0,
                        },
                    ],
                    broadCastInfo: {
                        todaySignIn: false,
                        nextLLrewdSignInDays: 1,
                        llrewdName: '必得{0.1现金}',
                    },
                    todayIndex: 0,
                    todayStationInfo: {
                        uniqueKey: 'tianjin',
                        stationName: '天津',
                        stationIcon:
                            'https://p4-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/sign-in/citylight/tianjin_light.png?x-kcdn-pid=112543',
                        stationBlackIcon:
                            'https://p4-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/sign-in/citydark/tianjin_dark.png?x-kcdn-pid=112543',
                        chessStationIcon:
                            'https://p4-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/sign-in/chess/tianjin_chess.png?x-kcdn-pid=112543',
                        llrewdIcon:
                            'https://p4-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/sign-in/reward/cash.png?x-kcdn-pid=112543',
                        stationThemeKey: 'jinghua',
                        signed: false,
                        stationDayIndex: 2,
                        stationTotalStep: 7,
                        stationBubbleIcon:
                            'https://p4-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/sign-in/reward/cash.png?x-kcdn-pid=112543',
                        normalBubbleText: '必得{0.1现金}',
                        stationBubbleText: '今日打卡/n必得{0.1现金}',
                        tomorrowBubbleText: '明日打卡/n必得{0.1现金}',
                        bubbleShowSeconds: 5000,
                        gridSkinUrl: null,
                        gridSkinSponsor: null,
                        gridSkinLocation: [2],
                        stationIconRewardText: '0.1',
                        stationIconRewardTextColor: '#00D164',
                        newLlrewdIcon:
                            'https://p4-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/sign-in/reward/cashWhite.png?x-kcdn-pid=112543',
                        cityTourismIdList: [2197871363, 2198176427, 2198176428],
                        roamingCityId: 120100,
                    },
                    needSignInDays: 10,
                },
                product: {
                    productId: 10004,
                    productName: '现金测试商品1',
                    productIcon:
                        'https://p4-plat.wsukwai.com/kos/nlav10395/activity/hugesignin/sign-in-cash.png?x-kcdn-pid=112543',
                    signInDays: 10,
                },
                eventId: null,
                calendarEventViewList: [
                    {
                        title: '心愿大巴签到提醒',
                        note: '打开快手进入活动页面http://ksurl.cn/J2-T1gw1',
                        url: null,
                        startDay: 1754015400000,
                        endDay: 1754706600000,
                        type: 1,
                    },
                    {
                        title: '快去确认今日心愿打卡已完成，错过将与大奖失之交臂',
                        note: '打开快手进入活动页面http://ksurl.cn/J2-T1gw1',
                        url: null,
                        startDay: 1754042400000,
                        endDay: 1754733600000,
                        type: 1,
                    },
                ],
                signInDays: 0,
            },
            todaySigned: false,
            signInStatus: 'PROCESSING',
            chessboard: {
                progress: {
                    currentStep: 0,
                    expectTotalStep: 7,
                    signed: false,
                    currentGridTaskDetail: null,
                    gridTaskDegrade: false,
                    currentTime: 1754030532178,
                    lastStation: false,
                },
                buildingInfos: [],
                stationHotInfoViews: {
                    stationDesc: null,
                    hotInfos: null,
                    liveTime: null,
                },
                userInfo: {
                    userName: 'S.E.N.S',
                    headUrl:
                        'https://kcdn.staging.kuaishou.com/uhead/AB/2024/04/26/16/BMjAyNDA0MjYxNjA4NTFfMjE5ODA5NjU5M18xX2hkMTQ4XzkwOA==_s.jpg',
                },
                stationList: [
                    {
                        stationInfo: {
                            uniqueKey: 'beijing',
                            stationName: '北京',
                            stationIcon:
                                'https://p4-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/sign-in/citylight/beijing_light.png?x-kcdn-pid=112543',
                            stationBlackIcon:
                                'https://p4-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/sign-in/citydark/beijing_dark.png?x-kcdn-pid=112543',
                            chessStationIcon:
                                'https://p4-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/sign-in/chess/beijing_chess.png?x-kcdn-pid=112543',
                            llrewdIcon:
                                'https://p4-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/sign-in/reward/cash.png?x-kcdn-pid=112543',
                            stationThemeKey: 'yangfan',
                            signed: false,
                            stationDayIndex: 1,
                            stationTotalStep: 1,
                            stationBubbleIcon:
                                'https://p4-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/sign-in/reward/cash.png?x-kcdn-pid=112543',
                            normalBubbleText: '最高得{0.88现金}',
                            stationBubbleText: '今日打卡/n最高得{0.88现金}',
                            tomorrowBubbleText: '明日打卡/n最高得{0.88现金}',
                            bubbleShowSeconds: 5000,
                            gridSkinUrl: null,
                            gridSkinSponsor: null,
                            gridSkinLocation: [0],
                            stationIconRewardText: '0.1',
                            stationIconRewardTextColor: '#00D164',
                            newLlrewdIcon:
                                'https://p4-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/sign-in/reward/cashWhite.png?x-kcdn-pid=112543',
                            cityTourismIdList: [2189466867, 2185591028],
                            roamingCityId: 110100,
                        },
                        llrewdGridLayout: {
                            LLCN_GRID: {
                                gridIcon:
                                    'https://p4-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/chessboard/llwd/LLCN.png?x-kcdn-pid=112543',
                                gridLocation: [],
                                gridTaskStatus: null,
                                gridLlrewdAmount: null,
                            },
                            TEAM_GRID: {
                                gridIcon:
                                    'https://p4-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/chess/challengeIcon.png?x-kcdn-pid=112543',
                                gridLocation: [],
                                gridTaskStatus: null,
                                gridLlrewdAmount: null,
                            },
                            LLCH_GRID: {
                                gridIcon:
                                    'https://p4-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/chessboard/llwd/LLCH.png?x-kcdn-pid=112543',
                                gridLocation: [],
                                gridTaskStatus: null,
                                gridLlrewdAmount: null,
                            },
                        },
                    },
                    {
                        stationInfo: {
                            uniqueKey: 'tianjin',
                            stationName: '天津',
                            stationIcon:
                                'https://p4-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/sign-in/citylight/tianjin_light.png?x-kcdn-pid=112543',
                            stationBlackIcon:
                                'https://p4-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/sign-in/citydark/tianjin_dark.png?x-kcdn-pid=112543',
                            chessStationIcon:
                                'https://p4-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/sign-in/chess/tianjin_chess.png?x-kcdn-pid=112543',
                            llrewdIcon:
                                'https://p4-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/sign-in/reward/cash.png?x-kcdn-pid=112543',
                            stationThemeKey: 'jinghua',
                            signed: false,
                            stationDayIndex: 2,
                            stationTotalStep: 7,
                            stationBubbleIcon:
                                'https://p4-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/sign-in/reward/cash.png?x-kcdn-pid=112543',
                            normalBubbleText: '必得{0.1现金}',
                            stationBubbleText: '今日打卡/n必得{0.1现金}',
                            tomorrowBubbleText: '明日打卡/n必得{0.1现金}',
                            bubbleShowSeconds: 5000,
                            gridSkinUrl: null,
                            gridSkinSponsor: null,
                            gridSkinLocation: [2],
                            stationIconRewardText: '0.1',
                            stationIconRewardTextColor: '#00D164',
                            newLlrewdIcon:
                                'https://p4-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/sign-in/reward/cashWhite.png?x-kcdn-pid=112543',
                            cityTourismIdList: [2197871363, 2198176427, 2198176428],
                            roamingCityId: 120100,
                        },
                        llrewdGridLayout: {
                            LLCN_GRID: {
                                gridIcon:
                                    'https://p4-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/chessboard/llwd/LLCN.png?x-kcdn-pid=112543',
                                gridLocation: [],
                                gridTaskStatus: null,
                                gridLlrewdAmount: null,
                            },
                            TEAM_GRID: {
                                gridIcon:
                                    'https://p4-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/chess/challengeIcon.png?x-kcdn-pid=112543',
                                gridLocation: [2, 3, 4, 5, 6],
                                gridTaskStatus: null,
                                gridLlrewdAmount: null,
                            },
                            LLCH_GRID: {
                                gridIcon:
                                    'https://p4-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/chessboard/llwd/LLCH.png?x-kcdn-pid=112543',
                                gridLocation: [],
                                gridTaskStatus: null,
                                gridLlrewdAmount: null,
                            },
                        },
                    },
                    {
                        stationInfo: {
                            uniqueKey: 'shijiazhuang',
                            stationName: '石家庄',
                            stationIcon:
                                'https://p4-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/sign-in/citylight/shijiazhuang_light.png?x-kcdn-pid=112543',
                            stationBlackIcon:
                                'https://p4-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/sign-in/citydark/shijiazhuang_dark.png?x-kcdn-pid=112543',
                            chessStationIcon:
                                'https://p4-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/sign-in/chess/shijiazhuang_chess.png?x-kcdn-pid=112543',
                            llrewdIcon: null,
                            stationThemeKey: 'yangfan',
                            signed: false,
                            stationDayIndex: 3,
                            stationTotalStep: 7,
                            stationBubbleIcon: null,
                            normalBubbleText: '',
                            stationBubbleText: '',
                            tomorrowBubbleText: '',
                            bubbleShowSeconds: 5000,
                            gridSkinUrl: null,
                            gridSkinSponsor: null,
                            gridSkinLocation: [2],
                            stationIconRewardText: '',
                            stationIconRewardTextColor: '',
                            newLlrewdIcon: null,
                            cityTourismIdList: [2197871363, 2198176427, 2198176428],
                            roamingCityId: 130100,
                        },
                        llrewdGridLayout: {
                            LLCN_GRID: {
                                gridIcon:
                                    'https://p4-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/chessboard/llwd/LLCN.png?x-kcdn-pid=112543',
                                gridLocation: [],
                                gridTaskStatus: null,
                                gridLlrewdAmount: null,
                            },
                            TEAM_GRID: {
                                gridIcon:
                                    'https://p4-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/chess/challengeIcon.png?x-kcdn-pid=112543',
                                gridLocation: [1, 2, 3, 4, 5, 6],
                                gridTaskStatus: null,
                                gridLlrewdAmount: null,
                            },
                            LLCH_GRID: {
                                gridIcon:
                                    'https://p4-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/chessboard/llwd/LLCH.png?x-kcdn-pid=112543',
                                gridLocation: [],
                                gridTaskStatus: null,
                                gridLlrewdAmount: null,
                            },
                        },
                    },
                    {
                        stationInfo: {
                            uniqueKey: 'baoding',
                            stationName: '保定',
                            stationIcon:
                                'https://p4-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/sign-in/citylight/baoding_light.png?x-kcdn-pid=112543',
                            stationBlackIcon:
                                'https://p4-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/sign-in/citydark/baoding_dark_new.png?x-kcdn-pid=112543',
                            chessStationIcon:
                                'https://p4-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/sign-in/chess/baoding_chess.png?x-kcdn-pid=112543',
                            llrewdIcon:
                                'https://p4-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/sign-in/reward/blackbox.png?x-kcdn-pid=112543',
                            stationThemeKey: 'jinghua',
                            signed: false,
                            stationDayIndex: 4,
                            stationTotalStep: 7,
                            stationBubbleIcon:
                                'https://p4-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/sign-in/reward/blackbox.png?x-kcdn-pid=112543',
                            normalBubbleText: '最高{8888金币}',
                            stationBubbleText: '今日打卡/n最高{8888金币}',
                            tomorrowBubbleText: '明日打卡/n最高{8888金币}',
                            bubbleShowSeconds: 5000,
                            gridSkinUrl: null,
                            gridSkinSponsor: null,
                            gridSkinLocation: [3],
                            stationIconRewardText: '8888',
                            stationIconRewardTextColor: '#B97B00',
                            newLlrewdIcon:
                                'https://p4-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/sign-in/reward/coinBlack.png?x-kcdn-pid=112543',
                            cityTourismIdList: [2197871363, 2198176427, 2198176428],
                            roamingCityId: 0,
                        },
                        llrewdGridLayout: {
                            LLCN_GRID: {
                                gridIcon:
                                    'https://p4-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/chessboard/llwd/LLCN.png?x-kcdn-pid=112543',
                                gridLocation: [2, 4, 6],
                                gridTaskStatus: null,
                                gridLlrewdAmount: null,
                            },
                            TEAM_GRID: {
                                gridIcon:
                                    'https://p4-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/chess/challengeIcon.png?x-kcdn-pid=112543',
                                gridLocation: [],
                                gridTaskStatus: null,
                                gridLlrewdAmount: null,
                            },
                            LLCH_GRID: {
                                gridIcon:
                                    'https://p4-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/chessboard/llwd/LLCH.png?x-kcdn-pid=112543',
                                gridLocation: [],
                                gridTaskStatus: null,
                                gridLlrewdAmount: null,
                            },
                            TASK_GRID: {
                                gridIcon:
                                    'https://p4-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/chess/challengeIcon.png?x-kcdn-pid=112543',
                                gridLocation: [],
                                gridTaskStatus: [],
                                gridLlrewdAmount: null,
                            },
                        },
                    },
                    {
                        stationInfo: {
                            uniqueKey: 'handan',
                            stationName: '邯郸',
                            stationIcon:
                                'https://p4-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/sign-in/citylight/handan_light.png?x-kcdn-pid=112543',
                            stationBlackIcon:
                                'https://p4-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/sign-in/citydark/handan_dark.png?x-kcdn-pid=112543',
                            chessStationIcon:
                                'https://p4-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/sign-in/chess/handan_chess.png?x-kcdn-pid=112543',
                            llrewdIcon: null,
                            stationThemeKey: 'jinghua',
                            signed: false,
                            stationDayIndex: 5,
                            stationTotalStep: 7,
                            stationBubbleIcon: null,
                            normalBubbleText: '',
                            stationBubbleText: '',
                            tomorrowBubbleText: '',
                            bubbleShowSeconds: 5000,
                            gridSkinUrl: null,
                            gridSkinSponsor: null,
                            gridSkinLocation: [2],
                            stationIconRewardText: '',
                            stationIconRewardTextColor: '',
                            newLlrewdIcon: null,
                            cityTourismIdList: [2197871363, 2198176427, 2198176428],
                            roamingCityId: 0,
                        },
                        llrewdGridLayout: {
                            LLCN_GRID: {
                                gridIcon:
                                    'https://p4-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/chessboard/llwd/LLCN.png?x-kcdn-pid=112543',
                                gridLocation: [2, 4, 6],
                                gridTaskStatus: null,
                                gridLlrewdAmount: null,
                            },
                            TEAM_GRID: {
                                gridIcon:
                                    'https://p4-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/chess/challengeIcon.png?x-kcdn-pid=112543',
                                gridLocation: [],
                                gridTaskStatus: null,
                                gridLlrewdAmount: null,
                            },
                            LLCH_GRID: {
                                gridIcon:
                                    'https://p4-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/chessboard/llwd/LLCH.png?x-kcdn-pid=112543',
                                gridLocation: [],
                                gridTaskStatus: null,
                                gridLlrewdAmount: null,
                            },
                            TASK_GRID: {
                                gridIcon:
                                    'https://p4-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/chess/challengeIcon.png?x-kcdn-pid=112543',
                                gridLocation: [],
                                gridTaskStatus: [],
                                gridLlrewdAmount: null,
                            },
                        },
                    },
                ],
                currentStationIndex: 1,
                initStationCount: 5,
            },
            mainButton: {
                rushCount: 0,
                reSignInCountdownTime: 0,
                stationDayIndex: 2,
                hasValidSignFreeCard: false,
                systemGivingRushChance: 0,
            },
            titleInfo: {
                logo: 'https://p4-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/home/<USER>',
                title: '百日心愿之旅',
                subTitle: '打卡10天必得终点豪礼',
            },
            accountModel: {
                total: '127.43',
                unit: '元',
            },
            homeUEConstantsConfig: {
                shortcutBgImg:
                    'https://p4-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/retain-popup/ios-shortcut-proxy-back.png?x-kcdn-pid=112543',
                activityRuleUrl:
                    'https://ppg.viviv.com/doodle/KfSYWOTr.html?uni_src=rule_page&hyId=jimu&bizId=jimu_KfSYWOTr&layoutType=4&noBackNavi=true',
                shortcutContentIcon:
                    'https://p4-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/retain-popup/contentIcon.png?x-kcdn-pid=112543',
                shortcutUrl: 'https://summer25.staging.kuaishou.com/home?layoutType=4',
                shareNumber9:
                    'https://p4-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/share/num9.png?x-kcdn-pid=112543',
                walletUrl:
                    'kwai://kds/react?bundleId=Cny2025RetentionKrn&componentName=wallet&biz=13627&themeStyle=1&entry_src=ks_cny_089',
                shareNumber8:
                    'https://p4-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/share/num8.png?x-kcdn-pid=112543',
                sponsorLogo:
                    'https://p4-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/sponsor/kuaishouLogo.png?x-kcdn-pid=112543',
                bigDayBannerTextImage:
                    'https://p4-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/bigday/chongqing.png?x-kcdn-pid=112543',
                activityUrl: 'https://summer25.staging.kuaishou.com/home?layoutType=4',
                shareNumber5:
                    'https://p4-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/share/num5.png?x-kcdn-pid=112543',
                shareNumber4:
                    'https://p4-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/share/num4.png?x-kcdn-pid=112543',
                shareNumber7:
                    'https://p4-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/share/num7.png?x-kcdn-pid=112543',
                sharePoster_default:
                    'https://p4-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/share/sharePoster_default.png?x-kcdn-pid=112543',
                shortcutIcon:
                    'https://p4-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/retain-popup/icon.png?x-kcdn-pid=112543',
                shareNumber6:
                    'https://p4-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/share/num6.png?x-kcdn-pid=112543',
                shareNumber1:
                    'https://p4-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/share/num1.png?x-kcdn-pid=112543',
                customerServiceUrl:
                    'https://csc-center.staging.kuaishou.com/help/index.html?enableWK=1&layoutType=4#/?entranceId=5852&environment=staging',
                shareNumber0:
                    'https://p4-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/share/num0.png?x-kcdn-pid=112543',
                shareNumber3:
                    'https://p4-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/share/num3.png?x-kcdn-pid=112543',
                shareNumber2:
                    'https://p4-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/share/num2.png?x-kcdn-pid=112543',
            },
            homeFEConstantsConfig: {
                frameConfig: {
                    themeMap: {
                        default: {
                            themeColor: '#f85c3e',
                            headerBackMaskBackground:
                                'linear-gradient(180deg, #f85c3e 0%, #ff7840 20%, #fba169 62%, #fff0 100%)',
                            footerBackMaskBackground: 'linear-gradient(180deg, #fff0 0%, #ffdcbfe6 42%, #ffefd1 100%)',
                            backgroundColor: '#FFDCBFE5',
                        },
                        jinghua: {
                            themeColor: '#f85c3e',
                            headerBackMaskBackground:
                                'linear-gradient(180deg, #f85c3e 0%, #ff7840 20%, #fba169 62%, #fff0 100%)',
                            footerBackMaskBackground: 'linear-gradient(180deg, #fff0 0%, #ffdcbfe6 42%, #ffefd1 100%)',
                            backgroundColor: '#FFDCBFE5',
                        },
                        xile: {
                            themeColor: '#188BFF',
                            headerBackMaskBackground:
                                'linear-gradient(180deg, #188BFF 0%, #57A7F7 20%, #98CBFF 62%, #fff0 100%)',
                            footerBackMaskBackground: 'linear-gradient(180deg, #fff0 0%, #d4eaffe6 42%, #C7E4FF 100%)',
                            backgroundColor: '#D4EAFFE5',
                        },
                        furao: {
                            themeColor: '#BCC619',
                            headerBackMaskBackground:
                                'linear-gradient(179.33deg, #BCC619 0%, #CDD823 35.56%, #E2EA5E 67.82%, rgba(245, 255, 177, 0) 99.42%)',
                            footerBackMaskBackground:
                                'linear-gradient(180deg, rgba(221, 216, 144, 0) 0.78%, rgba(253, 249, 200, 0.9) 42.63%, #EAE497 100%)',
                            backgroundColor: '#EAE497',
                        },
                        fengshou: {
                            themeColor: '#FFAA12',
                            headerBackMaskBackground:
                                'linear-gradient(179.33deg, #FFAA12 0%, #FFBF4E 35.56%, #FFC257 67.82%, rgba(255, 207, 122, 0) 99.42%)',
                            footerBackMaskBackground:
                                'linear-gradient(180deg, rgba(255, 223, 166, 0) 0.78%, rgba(255, 223, 166, 0.9) 42.63%, #FFDFA6 100%)',
                            backgroundColor: '#FFDFA6',
                        },
                        jiangnan: {
                            backgroundColor: '#BBFFF4',
                            footerBackMaskBackground:
                                'linear-gradient(180deg, rgba(187, 255, 244, 0) 0.78%, rgba(187, 255, 244, 0.9) 42.63%, #BBFFF4 100%)',
                            headerBackMaskBackground:
                                'linear-gradient(179.33deg, #0AD3C4 -7.01%, #08D5C4 19.83%, #08D5C4 67.82%, rgba(8, 213, 196, 0) 99.42%)',
                            themeColor: '#0AD3C4',
                        },
                        yangfan: {
                            backgroundColor: '#8EF1FF',
                            footerBackMaskBackground:
                                'linear-gradient(180deg, rgba(142, 241, 255, 0) 0.78%, rgba(142, 241, 255, 0.9) 42.63%, #8EF1FF 100%)',
                            headerBackMaskBackground:
                                'linear-gradient(179.33deg, #00CBFC -7.01%, #00CBFC 19.83%, #00CBFC 67.82%, rgba(0, 203, 252, 0) 99.42%)',
                            themeColor: '#00CBFC',
                        },
                        duocai: {
                            backgroundColor: '#CCF1A9',
                            footerBackMaskBackground:
                                'linear-gradient(180deg, rgba(204, 241, 169, 0) 0.78%, rgba(204, 241, 169, 0.9) 42.63%, #CCF1A9 100%)',
                            headerBackMaskBackground:
                                'linear-gradient(179.33deg, #55DA56 0%, #42D443 19.83%, #85E686 67.82%, rgba(130, 220, 131, 0) 99.42%)',
                            themeColor: '#55DA56',
                        },
                        sichou: {
                            themeColor: '#FFA765',
                            headerBackMaskBackground:
                                'linear-gradient(179.33deg, #FFA765 0%, #FFA765 19.83%, #FFA868 67.82%, rgba(247, 179, 129, 0) 99.42%)',
                            footerBackMaskBackground:
                                'linear-gradient(180deg, rgba(255, 211, 176, 0) 0.78%, rgba(255, 211, 176, 0.9) 42.63%, #FFD3B0 100%)',
                            backgroundColor: '#FF8C36',
                        },
                        jingchu: {
                            themeColor: '#FF849D',
                            headerBackMaskBackground:
                                'linear-gradient(179.33deg, #FF849D 0%, #FF849D 19.83%, #FF9AAE 67.82%, rgba(255, 154, 174, 0) 99.42%)',
                            footerBackMaskBackground:
                                'linear-gradient(180deg, rgba(255, 215, 220, 0) 0.78%, rgba(255, 215, 220, 0.9) 42.63%, #FFD7DC 100%)',
                            backgroundColor: '#FF5C7C',
                        },
                        rela: {
                            themeColor: '#FF7F34',
                            headerBackMaskBackground:
                                'linear-gradient(179.33deg, #FF7F34 0%, #FF995E 19.83%, #FFBF9A 67.82%, rgba(255, 191, 154, 0) 99.42%)',
                            footerBackMaskBackground:
                                'linear-gradient(180deg, rgba(255, 211, 186, 0) 0.78%, rgba(255, 211, 186, 0.9) 42.63%, #FFD3BA 100%)',
                            backgroundColor: '#FF7F34',
                        },
                    },
                },
                guideTitle: '连续打卡100天 赢好礼',
                flowerTip: {
                    signed: '打卡成功',
                    day2: '送你${0}步 冲!',
                    day100: '最后一步 冲!',
                },
                mainButtonConfig: {
                    leftSideButtonText: '赚现金',
                    rightSideButtonText: '赚步数',
                    noStepToast: '没有步数了，明天再来吧',
                    SIGN_INTERCEPTED: {
                        buttonText: '立即补打卡',
                        buttonSubText: '后不可补打卡',
                        bubbleText: '打卡中断',
                        bubbleSubText: '错过将痛失豪礼!',
                    },
                    FIRST_DAY_UN_SIGNED_EXPERIMENT: {
                        buttonText: '去选好礼',
                        bubbleText: '再冲{0}完成今日打卡!',
                    },
                    FIRST_DAY_SIGNED_EXPERIMENT: {
                        firstDayRemindText: '开启提醒',
                        firstDayRemindedText: '明天领',
                        firstDayToast: '今天领过啦，明天还有大额金币',
                        firstDayPacketToast: '今天领过啦，明天来领翻倍奖励',
                    },
                    SIGNED: {
                        buttonText: '今日已打卡',
                        newButtonText: '向前冲',
                        buttonSubText: '剩余步数: ',
                        duration: 3000,
                        bubbleText: '明天再来哦',
                        bubbleSubText: '打卡100天必得豪礼',
                        cityButtonText: '逛城市 赚现金',
                    },
                    SIGN_FREE: {
                        buttonText: '使用直通卡',
                        buttonSubText: '剩余步数: ',
                        bubbleText: '一键使用!',
                        bubbleSubText: '直接完成今日打卡',
                    },
                    UN_SIGN: {
                        buttonText: '向前冲',
                        buttonSubText: '剩余步数: ',
                        bubbleText: '再冲{0}完成今日打卡!',
                    },
                    OTHER: {
                        buttonText: '重新挑战',
                    },
                    FIRST_DAY: {
                        buttonText: '向前冲',
                        newButtonText: '向前冲',
                        buttonSubText: '剩余步数: ',
                        bubbleText: '打卡100天赢iPhone',
                        bubbleSubText: '立即冲',
                        cityButtonText: '逛城市 赚现金',
                    },
                },
                buildingInterval: 24,
                stationTagView: {
                    noSigned: {
                        first: '今日待打卡',
                        second: '明日待打卡',
                        another: '第{0}日待打卡',
                    },
                    signed: {
                        first: '今日已打卡',
                        second: '明日待打卡',
                        another: '第{0}日待打卡',
                    },
                },
                shortcutConfig: {
                    name: '百日心愿',
                    id: 'summer25',
                    bgColor: '#F2FCFF',
                    btText: '打开活动页',
                },
                retainFrequency: {
                    signRetainPopup: {
                        dayTime: 1,
                        allTime: 999,
                    },
                    shortcutRetainPopup: {
                        dayTime: 1,
                        allTime: 5,
                    },
                    searchRetainPopup: {
                        dayTime: 1,
                        allTime: 5,
                    },
                },
                beginnerConfig: {
                    beginnerSelectProductPopup: {
                        title: '打卡100天 白拿188元!',
                        subTitle: '打卡100天必得现金',
                        autoCloseTime: 8000,
                    },
                    enhanceRewardPopup: {
                        title: ['已坚持打卡${stationDay}天', '打卡100天白拿大奖'],
                        autoCloseTime: 2000,
                    },
                },
                inpushConfig: {
                    interval: 60000,
                },
                weakGuideConfig: {
                    GUIDE_RIGHT_BUTTON: {
                        totalLimitCount: 1,
                    },
                    GUIDE_TEAM: {
                        everydayLimitCount: 2,
                        duration: 5000,
                    },
                    GUIDE_CARD: {
                        everydayLimitCount: 5,
                        duration: 10000,
                    },
                    GUIDE_LEFT_TASK: {
                        totalLimitCount: 1,
                    },
                    GUIDE_RIGHT_TASK: {
                        totalLimitCount: 1,
                    },
                    GUIDE_MAIN_BUTTON: {
                        duration: 10000,
                    },
                },
                replaceProductConfirmText: {
                    FIRST: '连续打卡100天必得',
                    NORMAL: '更换后打卡进度、步数、直通卡将会清零',
                    TEAM: '你还在队伍中，先退队吧！',
                },
                broadcastTag: {
                    INTERCEPT: '打卡中断！',
                    UN_SIGN: '今日待打卡',
                    SIGNED: '今日已打卡',
                },
                broadcastText: {
                    INTERCEPT: '最后通牒，即将痛失好礼！',
                    DEFAULT: '查看我的打卡成就',
                },
                progressPointText: {
                    INTERCEPT: '中断',
                    DEFAULT: '第{0}天',
                    TODAY: '今天',
                    TARGET: '{0}天必得',
                },
                rewardTransPopupText: {
                    title: '挑战成功',
                    subTitle: '百日成就达成，速速发朋友圈收获膝盖！',
                },
                renderGroup: 2,
                refreshSiteTimeInterval: 600,
                activityStartTime: 1748601007607,
                mainButtonGuideHandShowDays: 1,
                bigDay: {
                    bigDayBannerText: '限时登场！\n 欢迎来到 热辣重庆',
                },
                bubbleOptimize: {
                    bubbleText: '再冲{0}完成今日打卡!!',
                    buttonTextOptimize: '向前冲完成打卡',
                    bubbleTitle: '连续打卡100天必得',
                },
                socialGuide0714: {
                    maskHideTime: 3000,
                    maskTip: '再邀{count}人组队成功',
                },
            },
            homeResourceMap: {
                RESOURCE_LEFT_TOP: {
                    id: 0,
                    labelDesc: '快聘活动',
                    title: '快聘活动',
                    icon: 'https://p4-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/home/<USER>',
                    iconText: null,
                    linkType: 'KWAI_LINK',
                    linkUrl:
                        'kwai://krn?bundleId=GrowthRecruitSummerVenue&componentName=main&themeStyle=1&source=ks_cny_050&portraitSolitary=1',
                },
                RESOURCE_LEFT_BOTTOM: {
                    id: 0,
                    labelDesc: '分享领钱',
                    title: '分享领钱',
                    icon: 'https://p4-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/home/<USER>',
                    iconText: null,
                    linkType: 'KWAI_LINK',
                    linkUrl:
                        'kwai://krn?componentName=main&bundleId=FissionShareOfficer&themeStyle=1&portraitSolitary=1&entry_src=main_icon&page_source=2025summer_main_icon&notFromOtherOpen=1&minBundleVersion=93',
                },
                RESOURCE_RIGHT_TOP: {
                    id: 0,
                    labelDesc: '特价团购',
                    title: '特价团购',
                    icon: 'https://p4-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/home/<USER>',
                    iconText: null,
                    linkType: 'JUMP_H5',
                    linkUrl:
                        'https://fangzhou.kwaixiaodian.com/locallife/695537109667909?hyId=OP_ACTIVITY_FZ_695537109667909&entry_src=fzdb1c89&bizId=OP_ACTIVITY_FZ_695537109667909&noBackNavi=true&layoutType=4&__launch_options__=%7B%22progressBarColor%22%3A%22%2300000000%22%7D&channelSource=CNY',
                },
                RESOURCE_RIGHT_BOTTOM: {
                    id: 0,
                    labelDesc: '直播夏季盛典',
                    title: '直播夏季盛典',
                    icon: 'https://p4-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/home/<USER>',
                    iconText: null,
                    linkType: 'JS_BRIDGE',
                    linkUrl: '{"name":"live.liveWatchTask","params":{"type":"watch","source":"shuqijingangwei"}}',
                },
            },
            popList: [],
            needSig3Path: [
                '/rest/wd/summer25/wishTravel/hugeSignIn/selectProduct',
                '/rest/wd/summer25/wishTravel/hugeSignIn/finalLlrewd',
                '/rest/wd/summer25/wishTravel/hugeSignIn/resume',
                '/rest/wd/summer25/wishTravel/hugeSignIn/abandon',
                '/rest/wd/summer25/wishTravel/hugeSignIn/pay/cancel',
                '/rest/wd/summer25/wishTravel/chess/move',
                '/rest/wd/summer25/wishTravel/llwdwc/sign',
                '/rest/n/summer2025/wish/travel/llwdwc/sign',
                '/rest/nebula/summer2025/wish/travel/llwdwc/sign',
                '/rest/wd/summer25/wishTravel/secondPage/move',
            ],

            degradeConfigView: {
                cdnLevel: 3,
                animationLevel: 0,
                videoLevel: 0,
                liveLevel: 0,
                audioLevel: 0,
                transparentVideoLevel: 2,
            },
            teamEntryView: {
                entryStatus: 1,
                teamUser: [
                    {
                        userId: 2198096593,
                        nickName: 'S.E.N.S',
                        userAvatar:
                            'https://kcdn.staging.kuaishou.com/uhead/AB/2024/04/26/16/BMjAyNDA0MjYxNjA4NTFfMjE5ODA5NjU5M18xX2hkMTQ4XzkwOA==_s.jpg',
                        todaySign: false,
                        currentUser: true,
                    },
                ],
                showPop: false,
                popType: 0,
                number: 0,
                teamId: 0,
                teamStatus: 1,
                newEntryStatus: 1,
                newShowPop: false,
                exitTeamGuide: false,
            },
            refreshTime: 33496587,
            abTestConfigView: {
                beginnerGuideStrategy: 4,
                enableNewMainTitle: false,
                beginnerModeOptimize: 0,
                doubledDetailView: null,
            },
            chessStepSwitch: true,
            cityButtonView: {
                show: false,
                cityCompleted: false,
                bubbleText: '快去探索天津',
                bubbleSubText: '现金红包先到先得～',
            },
        },
        timestamp: 1754030532258,
        hostname: 'public-xm-c34-kce-node102.idchb1az1.hb1.kwaidc.com',
    });
