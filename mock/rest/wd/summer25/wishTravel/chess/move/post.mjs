import mockjs from 'mockjs';

const move = (mockData) => {
    if (mockData.expectTotalStep > mockData.currentStep) {
        // 将 currentStep 加 1 后存回 mockData.json
        mockData.currentStep += 1;
    }
    return mockData.currentStep;
};

const mockSelectProductPopup = {
    popupType: 'HUGE_SIGN_IN_PRODUCT',
    title: '选择你的心动豪礼',
    roundId: 1,
    selectProductListView: {
        title: '打卡100天${必得豪礼}',
        subTitle: '已为你默认选择豪礼，也可以更换',
        firstSelectTag: '官方承诺',
        productList: [
            {
                templateId: 1,
                productId: 1001,
                productName: '电商测试商品2电商测试',
                productIcon:
                    'https://p2-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/product/iphone16.png?x-kcdn-pid=112543',
                originLLpe: '4999',
                labels: ['测试商品'],
                localLabels: [''],
                allSignDays: 40,
                llrewdType: 2,
                productChannel: 0,
                selectedNumText: '379人已选',
                selectNumText: '剩4件',
                limitProductRemainCount: 4,
                limitNum: 5,
                productRemainStatus: 0,
            },
            {
                templateId: 1,
                productId: 1002,
                productName: 'iphone16 8+128g 颜色随机',
                productIcon:
                    'https://p2-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/product/local.png?x-kcdn-pid=112543',
                originLLpe: '0.2',
                labels: ['测试商品'],
                localLabels: [''],
                allSignDays: 40,
                llrewdType: 2,
                productChannel: 1,
                selectedNumText: '196人已选',
                selectNumText: '剩5件',
                limitProductRemainCount: 5,
                limitNum: 5,
                productRemainStatus: 0,
            },
            {
                templateId: 1,
                productId: 1003,
                productName: '现金测试商品2',
                productIcon:
                    'https://p2-plat.wsukwai.com/kos/nlav10395/activity/hugesignin/sign-in-cash.png?x-kcdn-pid=112543',
                originLLpe: '99.99',
                labels: ['测试商品'],
                localLabels: [''],
                allSignDays: 40,
                llrewdType: 4,
                productChannel: 0,
                selectedNumText: '575人已选',
                selectNumText: null,
                limitProductRemainCount: -1,
                limitNum: 5,
                productRemainStatus: 0,
            },
            {
                templateId: 1,
                productId: 1004,
                productName: '电商测试商品40天',
                productIcon:
                    'https://p2-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/product/iphone16.png?x-kcdn-pid=112543',
                originLLpe: '499.99',
                labels: ['测试商品'],
                localLabels: [''],
                allSignDays: 100,
                llrewdType: 2,
                productChannel: 0,
                selectedNumText: '71人已选',
                selectNumText: null,
                limitProductRemainCount: -1,
                limitNum: 5,
                productRemainStatus: 0,
            },
            {
                templateId: 1,
                productId: 1005,
                productName: '本地测试商品40',
                productIcon:
                    'https://p2-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/product/local.png?x-kcdn-pid=112543',
                originLLpe: '0.2',
                labels: ['测试商品'],
                localLabels: [''],
                allSignDays: 40,
                llrewdType: 2,
                productChannel: 1,
                selectedNumText: '8人已选',
                selectNumText: null,
                limitProductRemainCount: -1,
                limitNum: 5,
                productRemainStatus: 0,
            },
            {
                templateId: 1,
                productId: 1006,
                productName: '现金测试商品100天',
                productIcon:
                    'https://p2-plat.wsukwai.com/kos/nlav10395/activity/hugesignin/sign-in-cash.png?x-kcdn-pid=112543',
                originLLpe: '99.99',
                labels: ['测试商品'],
                localLabels: [''],
                allSignDays: 100,
                llrewdType: 4,
                productChannel: 0,
                selectedNumText: '11人已选',
                selectNumText: null,
                limitProductRemainCount: -1,
                limitNum: 1000,
                productRemainStatus: 0,
            },
            {
                templateId: 1,
                productId: 1007,
                productName: '电商测试商品2包邮不配送地区',
                productIcon:
                    'https://p2-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/product/iphone16.png?x-kcdn-pid=112543',
                originLLpe: '8999',
                labels: ['测试商品'],
                localLabels: [''],
                allSignDays: 10,
                llrewdType: 2,
                productChannel: 0,
                selectedNumText: '4人已选',
                selectNumText: '剩5件',
                limitProductRemainCount: 5,
                limitNum: 5,
                productRemainStatus: 0,
            },
            {
                templateId: 1,
                productId: 1008,
                productName: '电商测试商品-杰鹏',
                productIcon:
                    'https://p2-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/product/iphone16.png?x-kcdn-pid=112543',
                originLLpe: '49.99',
                labels: ['测试商品'],
                localLabels: [''],
                allSignDays: 10,
                llrewdType: 2,
                productChannel: 0,
                selectedNumText: '2人已选',
                selectNumText: null,
                limitProductRemainCount: -1,
                limitNum: 1000,
                productRemainStatus: 0,
            },
            {
                templateId: 1,
                productId: 10002,
                productName: '本地测试商品1',
                productIcon:
                    'https://p2-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/product/local.png?x-kcdn-pid=112543',
                originLLpe: '0.2',
                labels: ['测试商品'],
                localLabels: [''],
                allSignDays: 40,
                llrewdType: 2,
                productChannel: 1,
                selectedNumText: '4人已选',
                selectNumText: '剩5件',
                limitProductRemainCount: 5,
                limitNum: 5,
                productRemainStatus: 0,
            },
            {
                templateId: 1,
                productId: 10004,
                productName: '现金测试商品1',
                productIcon:
                    'https://p2-plat.wsukwai.com/kos/nlav10395/activity/hugesignin/sign-in-cash.png?x-kcdn-pid=112543',
                originLLpe: '1',
                labels: ['512GB', '6.82英寸', '玄武架构'],
                localLabels: [''],
                allSignDays: 10,
                llrewdType: 4,
                productChannel: 0,
                selectedNumText: '3人已选',
                selectNumText: null,
                limitProductRemainCount: -1,
                limitNum: 5,
                productRemainStatus: 0,
            },
            {
                templateId: 1,
                productId: 10001,
                productName: 'iphone16 8+128g 颜色随机',
                productIcon:
                    'https://p2-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/product/iphone16.png?x-kcdn-pid=112543',
                originLLpe: '0.3',
                labels: ['测试商品'],
                localLabels: [''],
                allSignDays: 10,
                llrewdType: 2,
                productChannel: 0,
                selectedNumText: '',
                selectNumText: '今日已抢光',
                limitProductRemainCount: 0,
                limitNum: 0,
                productRemainStatus: 1,
            },
        ],
        selectedProductId: 1001,
        freeChange: true,
    },
    date: null,
};

const mockSignInPopup = {
    popupType: 'HUGE_SIGN_IN_LLAWD',
    sponsorLogo:
        'https://p2-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/sponsor/kuaishouLogo.png?x-kcdn-pid=112543',
    sponsorText: '快手',
    title: '今日打卡成功',
    subTitle: '已连续打卡',
    continueSignInDays: 1,
    signInStationList: [
        {
            stationName: '北京',
            stationIcon:
                'https://p2-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/sign-in/citylight/beijing_light.png?x-kcdn-pid=112543',
            stationBlackIcon:
                'https://p2-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/sign-in/citydark/beijing_dark.png?x-kcdn-pid=112543',
        },
        {
            stationName: '天津',
            stationIcon:
                'https://p2-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/sign-in/citylight/tianjin_light.png?x-kcdn-pid=112543',
            stationBlackIcon:
                'https://p2-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/sign-in/citydark/tianjin_dark.png?x-kcdn-pid=112543',
        },
        {
            stationName: '石家庄',
            stationIcon:
                'https://p2-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/sign-in/citylight/shijiazhuang_light.png?x-kcdn-pid=112543',
            stationBlackIcon:
                'https://p2-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/sign-in/citydark/shijiazhuang_dark.png?x-kcdn-pid=112543',
        },
        {
            stationName: '保定',
            stationIcon:
                'https://p2-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/sign-in/citylight/baoding_light.png?x-kcdn-pid=112543',
            stationBlackIcon:
                'https://p2-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/sign-in/citydark/baoding_dark_new.png?x-kcdn-pid=112543',
        },
        {
            stationName: '邯郸',
            stationIcon:
                'https://p2-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/sign-in/citylight/handan_light.png?x-kcdn-pid=112543',
            stationBlackIcon:
                'https://p2-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/sign-in/citydark/handan_dark.png?x-kcdn-pid=112543',
        },
        {
            stationName: '唐山',
            stationIcon:
                'https://p2-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/sign-in/citylight/tangshan_light.png?x-kcdn-pid=112543',
            stationBlackIcon:
                'https://p2-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/sign-in/citydark/tangshan_dark.png?x-kcdn-pid=112543',
        },
        {
            stationName: '沧州',
            stationIcon:
                'https://p2-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/sign-in/citylight/cangzhou_light.png?x-kcdn-pid=112543',
            stationBlackIcon:
                'https://p2-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/sign-in/citydark/cangzhou_dark.png?x-kcdn-pid=112543',
        },
    ],
    todayIndex: 0,
    llawdInfo: {
        amount: 0,
        llawdText: '距离豪礼还要39天！',
        llawdSubText: '坚持打卡加油哦！',
        llawdDesc: null,
        llawdIcon: 'https://p2-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/product/iphone16.png?x-kcdn-pid=112543',
        llawdName: '电商测试商品2电商测试',
        productId: 1001,
        couponId: null,
    },
    canGetLLawdDay: 39,
    hasStationLLawd: false,
    mainButton: {
        linkType: 'CLOSE',
        linkText: '我知道了',
        linkSubText: null,
        linkUrl: null,
        icon: null,
    },
    subButton: {
        linkType: 'SHARE',
        linkText: '炫耀一下',
        linkSubText: null,
        linkUrl: null,
        icon: null,
    },
};

const mockWatchVideoTask = {
    popupType: 'LS_WATCH_VIDEO_LLCN_TASK',
    sponsorLogo:
        'https://p2-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/sponsor/kuaishouLogo.png?x-kcdn-pid=112543',
    sponsorText: '快手',
    title: '观看内容30秒领福利',
    titleContext: {
        desc: '',
        userList: null,
        userCount: 0,
    },
    subTitle: '完成任务，金币+126',
    subTitleDesc: '',
    mainButton: {
        linkType: 'COMMON_TASK',
        linkText: '去完成',
        linkSubText: null,
        linkUrl: null,
        icon: null,
    },
    subButton: null,
    bottomButton: null,
    blessing: '',
    icon: null,
    showInTaskList: true,
    llpeDetail: [
        {
            llpeType: 'COMMON_ZT_TASK_LLCN',
            amount: 126,
            displayAmount: '126',
            displayUnit: '金币',
            bottomDesc: null,
            taskExtra: {
                taskId: 32425,
                title: '观看内容30秒领福利',
                description: '完成任务，金币+126',
                iconUrls: ['https://p2-pro.a.yximgs.com/kos/nlav11066/encourage-1749046559265-0juXeO.png'],
                completeConditionAmount: 30,
                completedAmount: 0,
                completeMaxTimes: 1,
                completedTimes: 0,
                taskStatus: 'COMPLETING_TASK',
                displayText: '去完成',
                jumpLink:
                    'kwai://commonfeedslide?utm_source=encourageCommonFeed&path=/rest/n/share/encourage/feed/view/list&pageSize=10&pcursor=&serverExtraInfo=eyJiaXoiOiJLVUFJU0hPVV9BRF9GRUVEIiwic291cmNlTmFtZSI6IlNVTU1FUl8yMDI1In0&hidePendant=true&taskToken=z9G0Z89nOqBBnAvIyg1adNz7Y0EnzGrbzBxbasyHLy8&taskId=32425&subBizId=8048&widgetParams=eyJjdXJyZW50UHJvZ3Jlc3MiOjAsImFjdGl2aXR5VGFzayI6dHJ1ZSwiY3VycmVudFBlcmlvZEluZGV4IjoyMDI1MDYwNSwiZXZlbnRDb3VudFR5cGUiOjEsIndpZGdldEVuYWJsZVNob3dOZXciOnRydWUsImVuYWJsZVJlcG9ydE5ld0FwaSI6ZmFsc2UsImFjdGl2aXR5SWQiOiIiLCJ0YXJnZXRQcm9ncmVzcyI6MzAsImV4cGlyZVRpbWUiOjE3NTgyOTc1OTkwMDAsImN1c3RvbUNvbmZpZyI6e30sImNvbXBsZXRlSWNvblVybHMiOlt7InVybCI6IiIsImNkbiI6IiJ9XSwiYml6SWQiOjIwMDAwMDAwMCwiaWNvblVybHMiOlt7InVybCI6IiIsImNkbiI6IiJ9XSwidHlwZUlkIjoid2F0Y2hfcGhvdG9fZHVyYXRpb24iLCJwZW5kYW50Q29uZmlnIjp7InNob3dQYWdlTGlzdCI6WzcsMywxLDMwMTY4LDMyMDY2LDQ2LDMwMTY5XSwic2hvd1BhZ2UyTGlzdCI6WyJBR0dSRUdBVEVfU0xJREVfRkVFRF9ERVRBSUwiLCJUSEFOT1NfTE9BRElORyIsIkRFVEFJTCIsIlBPUFVMQVJfUEFHRSIsIkZFQVRVUkVEX0RFVEFJTCIsIkZJTkQiLCJIT1QiLCJUSEFOT1NfRklORCIsIkZFQVRVUkVEX1BBR0UiLCJUSEFOT1NfSE9UIl0sImluUHJvZ3Jlc3NMaW5rVXJsIjoiIiwiY29tcGxldGVMaW5rVXJsIjoiIiwidGtCdW5kbGVJZCI6IiIsInRrRXh0cmFQYXJhbXMiOiIiLCJvbmx5VGsiOmZhbHNlLCJjbGlja1BvbGljeSI6MSwidWlDb25maWciOnsic3R5bGUiOjY2NiwiaW5pdFNpZGUiOjAsImluaXRZIjoyNTUsIndpZHRoIjo4MCwiaGVpZ2h0Ijo4MCwidGV4dEJnSGVpZ2h0IjoyNywidGV4dEJnQm90dG9tIjowLCJ0ZXh0U2l6ZSI6MTAsInRleHRDb2xvciI6IiMwMDAwMDAiLCJpblByb2dyZXNzVGV4dCI6IuWAkuiuoeaXtiAiLCJhbmltUmVzVXJsIjoiIiwiYW5pbUZyYW1lRHVyYXRpb24iOjUwLCJhbmltUmVwZWF0Q291bnQiOjAsImFuaW1JbnRlcnZhbER1cmF0aW9uIjo1LCJ4UG9saWN5IjowLCJpY29uVXJscyI6W3sidXJsIjoiaHR0cHM6Ly9wMi1wcm8uYS55eGltZ3MuY29tL2tvcy9ubGF2MTEwNjYvZW5jb3VyYWdlLTE3NDkwNDY0OTgxMjEtQVFldWVCLnBuZyIsImNkbiI6InAyLXByby5hLnl4aW1ncy5jb20ifV0sImNvbXBsZXRlSWNvblVybHMiOlt7InVybCI6Imh0dHBzOi8vcDUtcHJvLmEueXhpbWdzLmNvbS9rb3MvbmxhdjExMDY2L2VuY291cmFnZS0xNzQ5MDQ2NTAzMDY4LTFBSzQxdC5wbmciLCJjZG4iOiJwNS1wcm8uYS55eGltZ3MuY29tIn1dfSwiZGlzYWJsZUFudGlDaGVhdGluZyI6ImZhbHNlIiwiaW5Qcm9ncmVzc0NsaWNrUG9saWN5IjowLCJkaXNhcHBlYXJTZWNvbmRzSWZDb21wbGV0ZSI6MH0sImNoZWNrVmFsdWUiOjMwLCJkaXNhYmxlU3RvcmVUYXNrIjp0cnVlLCJ0eXBlSWRLZXkiOiJ3YXRjaF9ob21lcGFnZV92ZWRpb19uZXciLCJ0YXNrVG9rZW4iOiJGN2ZLWFd3VGFwbE1qNEFKeGFiZUZBbWJKdFhxSHVHSyJ9',
                jumpType: 'native',
                prizeName: '25暑期-任务列表-金币奖品1金币',
                prizeCount: 126,
                prizeId: 11226,
                extParams: {
                    taskId: 'TASK_10',
                    llrewd: 'LLCN',
                    activityName: 'MAYDAY_LONG_SIGN_FINAL',
                },
                completeToastText: '',
                timeLimitedStart: 0,
                timeLimitedEnd: 0,
                timeLimitExpireTime: 0,
                timeLimitedType: 0,
                takeTime: 1749094787730,
                delayQueryStatus: false,
                widgetParam: '',
                taskShowStyle: null,
                apkName: '',
                apkAddr: '',
                iosStoreAddr: '',
                iosSchema: '',
                shareSubBiz: '',
                unsupportedToast: '现App或系统版本低，请升级重试',
                taskPriority: 100,
                taskContentId: '完成任务，金币+${prizeCount}',
                taskToken: 'z9G0Z89nOqBBnAvIyg1adNz7Y0EnzGrbzBxbasyHLy8',
                subBizId: 8048,
                takeType: 'MANUAL_TAKE',
                takeStatus: 'TAKED',
                assistUserInfo: null,
                relationChainInfo: [],
                relationChainTypes: null,
                prizeIconUrls: [],
                reservationCalendarConfig: {
                    remindPeriod: 0,
                    scheduleStartTimeStamp: 0,
                    scheduleEndTimeStamp: 0,
                    calendarTitle: '',
                    calendarDescription: '',
                    kwaiLink: '',
                    nebulaLink: '',
                    dailyTimeStampList: [],
                },
                dynamicExtParam: {},
                titleCornerText: '',
                descriptionLabel: '',
                taskPropertyKey: '',
                backgroundColor: '',
                backgroundUrl: '',
                titleCornerUrl: '',
                bannerUrl: '',
                cornerBubbleText: '',
                pendantIconUrl: '',
            },
        },
    ],
};

const mockInpushTask = {
    popupType: 'HUGE_SIGN_IN_SUBSCRIBE_POPUP',
    sponsorLogo:
        'https://p2-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/sponsor/kuaishouLogo.png?x-kcdn-pid=112543',
    sponsorText: '快手',
    title: '快手官方送钱啦',
    titleContext: null,
    subTitle: '明日打卡必得',
    subTitleDesc: null,
    mainButton: {
        linkType: 'COMMON_TASK',
        linkText: '设置提醒得金币',
        linkSubText: '必得126金币',
        linkUrl: null,
        icon: null,
    },
    subButton: null,
    bottomButton: null,
    blessing: null,
    icon: null,
    showInTaskList: false,
    goldenStyle: false,
    stationDayIndex: 2,
    llpeDetail: [
        {
            llpeType: 'LLCN',
            amount: 1688,
            displayAmount: '1688',
            displayUnit: '金币',
            bottomDesc: null,
            taskExtra: {
                taskId: 17482,
                title: '订阅打卡提醒',
                description: '完成订阅，金币+126',
                iconUrls: [
                    'http://blobstore-nginx.staging.kuaishou.com/bs2/zt-encourage-prize/encourage-1751429424951-kFO2O8.png',
                ],
                completeConditionAmount: 1,
                completedAmount: 0,
                completeMaxTimes: 1,
                completedTimes: 0,
                taskStatus: 'COMPLETING_TASK',
                displayText: '去订阅',
                jumpLink: '',
                jumpType: 'report',
                prizeName: '25暑期 向前冲-金币',
                prizeCount: 126,
                prizeId: 44707,
                extParams: {
                    taskType: 'INPUSH_SUBSCRIBE',
                },
                completeToastText: '',
                timeLimitedStart: 0,
                timeLimitedEnd: 0,
                timeLimitExpireTime: 0,
                timeLimitedType: 0,
                takeTime: 1751535553036,
                delayQueryStatus: false,
                widgetParam: '',
                taskShowStyle: null,
                apkName: '',
                apkAddr: '',
                iosStoreAddr: '',
                iosSchema: '',
                shareSubBiz: '',
                unsupportedToast: '',
                taskPriority: 0,
                taskContentId: '完成订阅，金币+${prizeCount}',
                taskToken: '6cZ78EFqZvg8Q1UzdRINsm_lWJwdUL38hqHHs2oKWpo',
                subBizId: 13625,
                takeType: 'DISPLAY_TAKE',
                takeStatus: 'TAKED',
                assistUserInfo: null,
                relationChainInfo: [],
                relationChainTypes: null,
                prizeIconUrls: [],
                reservationCalendarConfig: {
                    remindPeriod: 0,
                    scheduleStartTimeStamp: 0,
                    scheduleEndTimeStamp: 0,
                    calendarTitle: '',
                    calendarDescription: '',
                    kwaiLink: '',
                    nebulaLink: '',
                    dailyTimeStampList: [],
                },
                dynamicExtParam: {},
                titleCornerText: '',
                descriptionLabel: '',
                taskPropertyKey: '',
                backgroundColor: '',
                backgroundUrl: '',
                titleCornerUrl: '',
                bannerUrl: '',
                cornerBubbleText: '',
                pendantIconUrl: '',
            },
        },
    ],
};

const currentGridTaskDetail = {
    commonTaskDetail: {
        taskId: 0,
        title: 'string',
        description: 'string',
        iconUrls: ['string'],
        completeConditionAmount: 0,
        completedAmount: 0,
        completeMaxTimes: 0,
        completedTimes: 0,
        taskStatus: 'UNKNOWN',
        displayText: 'string',
        jumpLink: 'string',
        jumpType: 'string',
        prizeName: 'string',
        prizeCount: 0,
        prizeId: 0,
        extParams: {
            additionalProp1: 'string',
            additionalProp2: 'string',
            additionalProp3: 'string',
        },
        completeToastText: 'string',
        timeLimitedStart: 0,
        timeLimitedEnd: 0,
        timeLimitExpireTime: 0,
        timeLimitedType: 0,
        takeTime: 0,
        delayQueryStatus: true,
        widgetParam: 'string',
        taskShowStyle: 'string',
        apkName: 'string',
        apkAddr: 'string',
        iosStoreAddr: 'string',
        iosSchema: 'string',
        shareSubBiz: 'string',
        unsupportedToast: 'string',
        taskPriority: 0,
        taskContentId: 'string',
        taskToken: 'string',
        subBizId: 0,
        takeType: 'string',
        takeStatus: 'string',
        assistUserInfo: [
            {
                userId: 0,
                userName: 'string',
                headImg: 'string',
            },
        ],
        relationChainInfo: [
            {
                userId: 0,
                userName: 'string',
                headImg: 'string',
            },
        ],
        relationChainTypes: ['string'],
        prizeIconUrls: ['string'],
        reservationCalendarConfig: {
            remindPeriod: 0,
            scheduleStartTimeStamp: 0,
            scheduleEndTimeStamp: 0,
            calendarTitle: 'string',
            calendarDescription: 'string',
            kwaiLink: 'string',
            nebulaLink: 'string',
            dailyTimeStampList: [0],
        },
        dynamicExtParam: {
            additionalProp1: 'string',
            additionalProp2: 'string',
            additionalProp3: 'string',
        },
        titleCornerText: 'string',
        descriptionLabel: 'string',
        taskPropertyKey: 'string',
        backgroundColor: 'string',
        backgroundUrl: 'string',
        titleCornerUrl: 'string',
        bannerUrl: 'string',
        cornerBubbleText: 'string',
        pendantIconUrl: 'string',
    },
};

const mockNewerCouponPopup = {
    popupType: 'HUGE_SIGN_IN_STATION_NEW_COUPON_POPUP',
    sponsorLogo:
        'https://p2-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/sponsor/kuaishouLogo.png?x-kcdn-pid=112543',
    sponsorText: '快手',
    title: '快手官方送钱啦',
    titleContext: null,
    subTitle: '第7天打卡必得优惠券',
    subTitleDesc: null,
    mainButton: {
        linkType: 'CLOSE',
        linkText: '我知道了',
        linkSubText: null,
        linkUrl: null,
        icon: null,
    },
    subButton: null,
    bottomButton: null,
    blessing: null,
    icon: 'https://p5-plat.wsbkwai.com/kos/nlav111422/25-summer-share-officer/city_popup.png',
    showInTaskList: false,
    llpeDetail: [
        {
            llpeType: 'LLCH',
            amount: 180,
            displayAmount: '1.8',
            displayUnit: '元',
            openSubTitle: null,
        },
    ],
};

const mockNewerLLCHPopup = {
    popupType: 'HUGE_SIGN_IN_STATION_NEW_POPUP',
    sponsorLogo:
        'https://p2-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/sponsor/kuaishouLogo.png?x-kcdn-pid=112543',
    sponsorText: '快手',
    title: '快手官方送钱啦',
    titleContext: null,
    subTitle: '第7天打卡必得优惠券',
    subTitleDesc: null,
    mainButton: {
        linkType: 'CLOSE',
        linkText: '我知道了',
        linkSubText: null,
        linkUrl: null,
        icon: null,
    },
    subButton: null,
    bottomButton: null,
    blessing: null,
    icon: 'https://p5-plat.wsbkwai.com/kos/nlav111422/25-summer-share-officer/city_popup.png',
    showInTaskList: false,
    llpeDetail: [
        {
            llpeType: 'LLCH',
            amount: 180,
            displayAmount: '1.8',
            displayUnit: '元',
            openSubTitle: null,
        },
    ],
};

const getRandom = (arr) => arr[Math.floor(Math.random() * arr.length)];

const getPopupList = (mockData, signed) => {
    if (!signed) {
        // 未打卡成功: 下发任务/无
        return getRandom([true, false]) ? [mockWatchVideoTask] : [];
    }

    // 打卡成功
    if (mockData.stationDayIndex === 1) {
        const showSelectProduct = getRandom([true, false]);
        let popList = [];
        // 第一天: 选品、签到、任务
        if (showSelectProduct) {
            // 换品后第一天不会下发选品弹窗
            popList.push(mockSelectProductPopup);
        }
        // 始终展示打卡成就弹窗
        popList.push(mockSignInPopup);
        return popList;
    } else {
        // 非第一天: 签到
        return [mockSignInPopup];
    }
};

const subscribeInpushPopupView = (mockData, signed) => {
    // 首日打卡成功，下发 inpush 任务弹窗
    const showInpushTask = getRandom([true, false]);
    if (signed && mockData.stationDayIndex === 1 && showInpushTask) {
        // inpush 任务弹窗全周期仅会展示一次
        return [mockInpushTask];
    }
    return null;
};

const doubleData1 = {
    sponsorLogo: 'https://kcdn-w1.staging.kuaishou.com/kos/nlav10309/feihe.png?x-ks-ptid=0',
    sponsorText: '飞鹤奶粉',
    popupType: 'LS_TIME_LIMITED_ASSIST_DOUBLE_LLCH',
    show: true,
    title: '恭喜获得打卡奖励',
    subTitle: '快手送你现金',
    openSubTitle: '获取金币',
    // subTitleDesc: '折合现金为1元',
    // titleContext: {
    //     desc: '挑战成功',
    //     userList: [
    //         {
    //             avatar: 'https://s3.kskwai.com/udata/pkg/nebula/fission/static-picture-manual-upload/male-avatar.cb4e7b5edc82c7a6.png',
    //             name: '测试1111111',
    //         },
    //         {
    //             avatar: 'https://s3.kskwai.com/udata/pkg/nebula/fission/static-picture-manual-upload/male-avatar.cb4e7b5edc82c7a6.png',
    //             name: '测试1112222',
    //         },
    //     ],
    // },
    mainButton: {
        linkType: 'UNKNOWN',
        linkText: '开心收下',
        linkUrl: 'string',
    },
    // bottomInfo: {
    //     bottomDesc: '领取至「钱包」',
    //     bottomButton: {
    //         linkType: 'UNKNOWN',
    //         linkText: '去查看',
    //         linkUrl: 'string',
    //     },
    // },
    prizeDetail: [
        {
            prizeType: 'amount',
            amount: 800,
            unit: '金币',
            desc: '折合为现金0.8元',
        },
        // {
        //     prizeType: 'amount' as const,
        //     title: '5分钟完成4个任务',
        //     amount: 6.66,
        //     unit: '元',
        //     tag: '最高',
        //     // bottomDesc: '红包可提现至微信零钱',
        //     // bottomDescIcon: '',
        // },
        // {
        //     prizeType: 'coupon' as const,
        //     coupon: {
        //         displayType: 'discount',
        //         branding: false,
        //         name: '花西子官方店铺2',
        //         icon: 'https://kcdn-w1.staging.kuaishou.com/kos/nlav100106/encourage-1699613865329-6xsxDu.jpeg',
        //         desc: '全店通用2',
        //         forwardUrl: 'https://www.baidu.com',
        //         forwardText: '去使用',
        //         status: 'unused',
        //         value: 7.0,
        //         valueText: '7.0',
        //         valueDesc: '无门槛',
        //         expiredTime: 1709204185830,
        //         validTime: 0,
        //     },
        // },
        // {
        //     prizeType: 'video' as const,
        //     poster: 'https://ali.a.yximgs.com/kos/nlav12119/GpdcTwpG_2024-11-12-14-18-48.png',
        //     source: 'https://kcdn.staging.kuaishou.com/upic/2023/09/13/11/BMjAyMzA5MTMxMTQ2MjdfMTMwMDA1XzEyODExMDVfMF8z_v5HighLowV8_A020a4e0e7f5bcc73f55b5d7c477d90ea.mp4?tag=1-1701668289-unknown-0-kvffm3egh0-c45cb13e0e941e66&bp=10001&tt=v5HighLowV8&ss=vp',
        // },
    ],
    secondPopup: {
        popupType: 'LS_TIME_LIMITED_ASSIST_DOUBLE_LLCH',
        sponsorLogo: 'https://kcdn-w1.staging.kuaishou.com/kos/nlav10309/feihe.png?x-ks-ptid=0',
        sponsorText: '飞鹤奶粉',
        title: '邀5位好友可膨胀',
        titleContext: {
            desc: '邀请好友即可获得奖励',
            userList: null,
            userCount: 0,
        },
        subTitle: '邀请成功，得现金 10元',
        subTitleDesc: '',
        mainButton: {
            linkType: 'COMMON_TASK',
            linkText: '去助力',
            linkSubText: null,
            linkUrl: null,
            icon: null,
        },
        subButton: null,
        bottomButton: null,
        blessing: '',
        icon: null,
        llpeDetail: [
            {
                llpeType: 'TIME_LIMITED_ASSIST_DOUBLE_LLCH_TASK',
                amount: 0,
                displayAmount: '0',
                displayUnit: '',
                expireTime: 1737504733628,
                originalLlrewdAmount: 100,
                displayOriginalLlrewdAmount: '1',
                displayOriginalLlrewdUnit: '元',
                originalLlrewdDesc: '当前现金',
                originalLlrewdIcon:
                    'https://kcdn.staging.kuaishou.com/kos/nlav111737/25CNYWarmupRichTree/assistDoubleTaskOriginalIcon.png?x-ks-ptid=0',
                doubleLlrewdAmount: 1000,
                displayDoubleLlrewdAmount: '10',
                displayDoubleLlrewdUnit: '元',
                doubleLlrewdDesc: '翻倍后得',
                doubleLlrewdIcon:
                    'https://kcdn.staging.kuaishou.com/kos/nlav111737/25CNYWarmupRichTree/assistDoubleTaskDoubleIcon.png?x-ks-ptid=0',
                needAssistCount: 5,
                taskExtra: {
                    taskId: 11368,
                    title: '邀5位好友可膨胀',
                    description: '邀请成功，得现金 10元',
                    iconUrls: [''],
                    completeConditionAmount: 5,
                    completedAmount: 0,
                    completeMaxTimes: 5,
                    completedTimes: 0,
                    taskStatus: 'COMPLETING_TASK',
                    displayText: '去助力',
                    jumpLink: '',
                    jumpType: 'share',
                    prizeName: '现金',
                    prizeCount: 1000,
                    extParams: {
                        groupName: 'limitedAssist',
                        baseRewardAmount: '100',
                        multiple: '10',
                        taskType: 'LIMITED_ASSIST_DOUBLE',
                        countdownDescription: '${countdown}内好友摇一摇即可成功',
                        llrewd: 'LLCH',
                        redPackageBtnName: '立即膨胀',
                        titleDesc: '邀请好友即可获得奖励',
                    },
                    completeToastText: '',
                    timeLimitedStart: 1734347533628,
                    timeLimitedEnd: 1734351133628,
                    timeLimitExpireTime: 3600000,
                    timeLimitedType: 2,
                    takeTime: 1734347533628,
                    delayQueryStatus: false,
                    widgetParam: '',
                    taskShowStyle: null,
                    apkName: '',
                    apkAddr: '',
                    iosStoreAddr: '',
                    iosSchema: '',
                    shareSubBiz: 'SF_SNAKE_CHUXI_LHFB',
                    unsupportedToast: '现App或系统版本低，请升级重试',
                    taskPriority: 0,
                    taskContentId: '邀请成功，得${prizeName} ${prizeCountFen2Yuan}元',
                    taskToken: '-usjgNeI8nA3OnV8XMS9KEpByGXdS4WwxewoSTucHcs',
                    subBizId: 12759,
                    takeType: 'MANUAL_TAKE',
                    takeStatus: 'TAKED',
                    assistUserInfo: null,
                    relationChainInfo: [],
                    relationChainTypes: null,
                    prizeIconUrls: [],
                    reservationCalendarConfig: {
                        remindPeriod: 0,
                        scheduleStartTimeStamp: 0,
                        scheduleEndTimeStamp: 0,
                        calendarTitle: '',
                        calendarDescription: '',
                        kwaiLink: '',
                        nebulaLink: '',
                        dailyTimeStampList: [],
                    },
                    dynamicExtParam: null,
                    titleCornerText: null,
                    descriptionLabel: null,
                    taskPropertyKey: null,
                    backgroundColor: null,
                    backgroundUrl: null,
                    titleCornerUrl: null,
                    bannerUrl: null,
                    cornerBubbleText: null,
                    pendantIconUrl: null,
                },
            },
        ],
        stepLeee: null,
    },
};
const doubleData2 = {
    popupType: 'LS_TIME_LIMITED_ASSIST_DOUBLE_LLCH',
    sponsorLogo: 'https://kcdn-w1.staging.kuaishou.com/kos/nlav10309/feihe.png?x-ks-ptid=0',
    sponsorText: '飞鹤奶粉',
    title: '邀5位好友可膨胀',
    titleContext: {
        desc: '邀请好友即可获得奖励',
        userList: null,
        userCount: 0,
    },
    subTitle: '明天打卡金币翻倍',
    subTitleDesc: '',
    mainButton: {
        linkType: 'COMMON_TASK',
        linkText: '去助力',
        linkSubText: null,
        linkUrl: null,
        icon: null,
    },
    subButton: null,
    bottomButton: null,
    blessing: '',
    icon: null,
    llpeDetail: [
        {
            llpeType: 'TIME_LIMITED_ASSIST_DOUBLE_LLCH_TASK',
            amount: 0,
            displayAmount: '0',
            displayUnit: '',
            expireTime: 1737504733628,
            originalLlrewdAmount: 100,
            displayOriginalLlrewdAmount: '1',
            displayOriginalLlrewdUnit: '元',
            originalLlrewdDesc: '当前现金',
            originalLlrewdIcon:
                'https://kcdn.staging.kuaishou.com/kos/nlav111737/25CNYWarmupRichTree/assistDoubleTaskOriginalIcon.png?x-ks-ptid=0',
            doubleLlrewdAmount: 1000,
            displayDoubleLlrewdAmount: '10',
            displayDoubleLlrewdUnit: '元',
            doubleLlrewdDesc: '翻倍后得',
            doubleLlrewdIcon:
                'https://kcdn.staging.kuaishou.com/kos/nlav111737/25CNYWarmupRichTree/assistDoubleTaskDoubleIcon.png?x-ks-ptid=0',
            needAssistCount: 5,
        },
    ],
    stepLeee: null,
};

const gen = () => {
    // 从 data.json 文件中读取公共信息
    const mockData = require('./data.json');
    const currentStep = move(mockData);
    const signed = currentStep === mockData.expectTotalStep;

    return {
        result: 1,
        data: {
            progress: {
                currentStep: 5,
                expectTotalStep: 7,
                signed: false,
                currentGridTaskDetail: null,
                gridTaskDegrade: false,
                teamGridDegrade: false,
                currentTime: 1754480877264,
                lastStation: false,
            },
            luckRushSudokuView: [
                {
                    popupType: 'TEAM_GRID_RECO_FRIENDS_CARD',
                    sponsorLogo:
                        'https://p2-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/home/<USER>',
                    mainButton: {
                        linkType: 'UNKNOWN',
                        linkText: '喊Ta组队',
                        linkSubText: null,
                        linkUrl: null,
                        icon: null,
                    },
                    friendCardViews: [
                        {
                            title: '快邀请ta组队打卡吧',
                            desc: '组队送直通卡,使用后当天一键打卡',
                            userId: 2198629232,
                            avatar: 'https://kcdn.staging.kuaishou.com/uhead/AB/2025/07/11/16/BMjAyNTA3MTExNjE5MDBfMjE5ODYyOTIzMl8yX2hkMzQ5XzIyNw==_s.jpg',
                            nickName: '李珠',
                            commonFriendCount: 0,
                            properties: {
                                GENDER: 'U',
                            },
                        },
                        {
                            title: '快邀请ta组队打卡吧',
                            desc: '组队送直通卡,使用后当天一键打卡',
                            userId: 2198662635,
                            avatar: 'https://kcdn-w1.staging.kuaishou.com/uhead/AB/2025/06/13/15/BMjAyNTA2MTMxNTA4MDFfMjE5ODY2MjYzNV8yX2hkNzM3XzI2Ng==_s.jpg',
                            nickName: '殷眨',
                            commonFriendCount: 0,
                            properties: {
                                GENDER: 'U',
                            },
                        },
                        {
                            title: '快邀请ta组队打卡吧',
                            desc: '组队送直通卡,使用后当天一键打卡',
                            userId: 2198685816,
                            avatar: 'https://kcdn.staging.kuaishou.com/uhead/AB/2025/08/06/11/BMjAyNTA4MDYxMTQ4MDhfMjE5ODY4NTgxNl8xX2hkOTA5XzA=.jpg',
                            nickName: '我是封禁用户',
                            commonFriendCount: 0,
                            properties: {
                                GENDER: 'U',
                                XZ: '天蝎座',
                            },
                        },
                        {
                            title: '快邀请ta组队打卡吧',
                            desc: '组队送直通卡,使用后当天一键打卡',
                            userId: 2198682022,
                            avatar: 'https://kcdn-w1.staging.kuaishou.com/uhead/AB/2025/03/21/11/BMjAyNTAzMjExMTA0MTRfMjE5ODY4MjAyMl8yX2hkOTAzXzM2Mw==_s.jpg',
                            nickName: '唐烷碗',
                            commonFriendCount: 0,
                            properties: {
                                GENDER: 'U',
                            },
                        },
                        {
                            title: '快邀请ta组队打卡吧',
                            desc: '组队送直通卡,使用后当天一键打卡',
                            userId: 2198683589,
                            avatar: 'https://kcdn-w1.staging.kuaishou.com/uhead/AB/2025/01/21/14/BMjAyNTAxMjExNDUyNDZfMjE5ODY4MzU4OV8yX2hkNTc3XzY2Nw==_s.jpg',
                            nickName: '倪螟',
                            commonFriendCount: 0,
                            properties: {
                                GENDER: 'U',
                            },
                        },
                        {
                            title: '快邀请ta组队打卡吧',
                            desc: '组队送直通卡,使用后当天一键打卡',
                            userId: 2198138184,
                            avatar: 'https://alimov2.a.yximgs.com/kos/nlav12689/head.jpg',
                            nickName: '日常沉淀',
                            commonFriendCount: 0,
                            properties: {
                                GENDER: 'F',
                            },
                        },
                        {
                            title: '快邀请ta组队打卡吧',
                            desc: '组队送直通卡,使用后当天一键打卡',
                            userId: 2198493807,
                            avatar: 'https://kcdn-w1.staging.kuaishou.com/uhead/AB/2024/11/12/15/BMjAyNDExMTIxNTAyMjdfMjE5ODQ5MzgwN18yX2hkMjIzXzY3Ng==_s.jpg',
                            nickName: '子车忠脂',
                            commonFriendCount: 0,
                            properties: {
                                GENDER: 'M',
                            },
                        },
                        {
                            title: '快邀请ta组队打卡吧',
                            desc: '组队送直通卡,使用后当天一键打卡',
                            userId: 2198648940,
                            avatar: 'https://kcdn.staging.kuaishou.com/uhead/AB/2025/07/22/17/BMjAyNTA3MjIxNzU2MTNfMjE5ODY0ODk0MF8yX2hkOTEzXzIyNQ==_s.jpg',
                            nickName: '酆奎',
                            commonFriendCount: 0,
                            properties: {
                                GENDER: 'U',
                            },
                        },
                        {
                            title: '快邀请ta组队打卡吧',
                            desc: '组队送直通卡,使用后当天一键打卡',
                            userId: 2198620971,
                            avatar: 'https://kcdn-w1.staging.kuaishou.com/uhead/AB/2025/02/20/19/BMjAyNTAyMjAxOTU0NTlfMjE5ODYyMDk3MV8yX2hkMjI5XzM1NQ==_s.jpg',
                            nickName: '乐类嗜',
                            commonFriendCount: 0,
                            properties: {
                                GENDER: 'U',
                            },
                        },
                        {
                            title: '快邀请ta组队打卡吧',
                            desc: '组队送直通卡,使用后当天一键打卡',
                            userId: 2198901355,
                            avatar: 'https://alimov2.a.yximgs.com/kos/nlav12689/head.jpg',
                            nickName: '快手用户1754385809360',
                            commonFriendCount: 0,
                            properties: {
                                IP: '广东',
                                GENDER: 'M',
                                XZ: '水瓶座',
                            },
                        },
                    ],
                },
            ],
            subscribeInpushPopupView: null,
        },
        timestamp: 1754480877314,
        hostname: 'public-xm-c11-29.idchb1az1.hb1.kwaidc.com',
    };
};

export default () => {
    return mockjs.mock(gen());
};

// 注释掉下面这行代码，则会禁用当前接口的 mock
// export const disable = true;
