import mockjs from 'mockjs';

const mockUsers = [
    {
        userId: 1006,
        nickName: 'yyy',
        userAvatar: 'https://figma-alpha-api.s3.us-west-2.amazonaws.com/images/e3d91595-2319-4be1-addc-aeddd076e717',
        todaySign: true,
        currentUser: false,
    },
    {
        userId: 1007,
        nickName: '你真是够了',
        userAvatar: 'https://figma-alpha-api.s3.us-west-2.amazonaws.com/images/d51ee1f1-48fe-4310-b91e-a07956e5303f',
        todaySign: false,
        currentUser: false,
    },
    {
        userId: 1001,
        nickName: '灰灰',
        userAvatar: 'https://figma-alpha-api.s3.us-west-2.amazonaws.com/images/f815ad9d-e74a-4f33-b2d3-5d7a2c3296f2', // 实际开发时替换为真实头像路径
        todaySign: true,
        currentUser: false,
    },
    {
        userId: 1002,
        nickName: '吉吉国王',
        userAvatar: 'https://figma-alpha-api.s3.us-west-2.amazonaws.com/images/c8cdf254-2f64-4cdb-8c29-aaf4776183af', // 实际开发时替换为真实头像路径
        todaySign: false,
        currentUser: false,
    },
    {
        userId: 1003,
        nickName: '复航',
        userAvatar: 'https://figma-alpha-api.s3.us-west-2.amazonaws.com/images/51fddd5d-18e3-4a7c-b8a9-bfff86a1f36d', // 实际开发时替换为真实头像路径
        todaySign: true,
        currentUser: false,
    },
    {
        userId: 1004,
        nickName: '小猪佩奇',
        userAvatar: 'https://figma-alpha-api.s3.us-west-2.amazonaws.com/images/11f3c2f3-d9fb-48e2-a904-d7fe05d7ec36', // 实际开发时替换为真实头像路径
        todaySign: false,
        currentUser: false,
    },
    {
        userId: 1005,
        nickName: '白雪公主',
        userAvatar: 'https://figma-alpha-api.s3.us-west-2.amazonaws.com/images/3689879b-05e1-40e8-8fc7-422030c63ed1', // 实际开发时替换为真实头像路径
        todaySign: true,
        currentUser: false,
    },
];
export default mockjs.mock({
    result: 1,
    message: '@string',
    data: [...mockUsers, ...mockUsers],
    timestamp: '@integer(1, 100)',
    hostname: '@string',
    error_msg: '@string',
});

// 注释掉下面这行代码，则会禁用当前接口的 mock
// export const disable = true;
