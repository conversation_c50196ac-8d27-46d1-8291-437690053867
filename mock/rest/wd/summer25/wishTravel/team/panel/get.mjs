import mockjs from 'mockjs';
const teamCreated = {
    title: '3人组队打卡免做7天任务',
    desc: ['需邀请活动新用户或断签用户，直通卡人人有份', '团队打卡进度不会影响个人打卡进度哦'],
    teamUser: [
        {
            userId: 1006,
            nickName: 'yyy',
            userAvatar:
                'https://figma-alpha-api.s3.us-west-2.amazonaws.com/images/e3d91595-2319-4be1-addc-aeddd076e717',
            todaySign: true,
            currentUser: true,
        },
        {
            userId: 1001,
            nickName: '灰灰',
            userAvatar:
                'https://figma-alpha-api.s3.us-west-2.amazonaws.com/images/f815ad9d-e74a-4f33-b2d3-5d7a2c3296f2', // 实际开发时替换为真实头像路径
            todaySign: true,
            currentUser: false,
        },
        {
            userId: 1002,
            nickName: '吉吉国王',
            userAvatar:
                'https://figma-alpha-api.s3.us-west-2.amazonaws.com/images/c8cdf254-2f64-4cdb-8c29-aaf4776183af', // 实际开发时替换为真实头像路径
            todaySign: false,
            currentUser: false,
        },
    ],
    chatId: 9527,
    taskFreeCardImg: 'https://ali.a.yximgs.com/kos/nlav12119/jTyADRou_2025-05-07-15-43-46.png',
    taskFreeCardNum: 7,
    taskFreeCardDesc: '@string',
    exitTeamPopup: {
        popupTitle: '确定离开小队吗？',
        popupDesc: '马上就能获得7张直通卡了哦',
        mainButton: '再想想',
        subButton: '退出',
    },
    showExitTeam: true,
    teamBuildExpireTime: '@integer(1683792000000, 1684396800000)',
    remainingTeamBuildTime: 25200,
    teamBuildExpireDesc: '你今日任务已完成，快去提醒队友吧',
    togetherSignDays: 0,
    togetherSignDesc: '已打卡1/7天',
    teamSignTotalDays: 1,
    teamSuccessMode: true,
    teamStatus: 1,
    // teamEntryView: {
    //     title: '组队打卡',
    //     desc: '邀请好友组队，享受免做任务特权',
    //     buttonText: '立即组队',
    // },
    maxTeamUser: 3,
    timestamp: '@integer(1, 100)',
    hostname: '@string',
    error_msg: '@string',
};
const teamNotCreated = {
    title: '3人组队打卡送直通卡',
    desc: ['需邀请活动新用户或断签用户，直通卡人人有份', '团队打卡进度不会影响个人打卡进度哦'],
    teamUser: [
        {
            userId: 1006,
            nickName: 'yyy',
            userAvatar:
                'https://p2-ad.adkwai.com/udata/pkg/ks-ad-fe/chrome-plugin-upload/2025-06-04/1749042470132.073b103bfc5ef56a.JPG',
            todaySign: true,
            currentUser: true,
        },
        {
            userId: 2197379806,
            nickName: '玛卡巴卡',
            userAvatar:
                'https://p2-ad.adkwai.com/udata/pkg/ks-ad-fe/chrome-plugin-upload/2025-06-04/1749042470132.073b103bfc5ef56a.JPG',
            todaySign: false,
            currentUser: false,
        },
    ],
    chatId: 9527,
    taskFreeCardImg: 'https://ali.a.yximgs.com/kos/nlav12119/jTyADRou_2025-05-07-15-43-46.png',
    taskFreeCardNum: 7,
    taskFreeCardDesc: '使用直通卡可直达当日终点',
    exitTeamPopup: {
        popupTitle: '确定离开小队吗？',
        popupDesc: '马上就能获得7张直通卡了哦',
        mainButton: '再想想',
        subButton: '退出',
    },
    showExitTeam: true,
    teamBuildExpireTime: '@integer(1683792000000, 1684396800000)',
    remainingTeamBuildTime: 25200,
    teamBuildExpireDesc: '后未邀请到第3人，队伍解散',
    togetherSignDays: 0,
    togetherSignDesc: '已打卡1/7天',
    teamSignTotalDays: 1,
    teamSuccessMode: false,
    teamStatus: 1,
    // teamEntryView: {
    //     title: '组队打卡',
    //     desc: '邀请好友组队，享受免做任务特权',
    //     buttonText: '立即组队',
    // },
    maxTeamUser: 3,
    timestamp: '@integer(1, 100)',
    hostname: '@string',
    error_msg: '@string',
};
export default mockjs.mock({
    result: 1,
    message: '@string',
    data: teamNotCreated,
});

// 注释掉下面这行代码，则会禁用当前接口的 mock
// export const disable = true;
