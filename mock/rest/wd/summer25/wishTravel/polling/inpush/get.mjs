import mockjs from 'mockjs';

const mockInpushList = [
    // 助力inpush
    {
        inpushType: 'ASSIST',
        title: '2位好友为你助力',
        content: '邀好友，多邀多赚钱',
        assistedUserInfo: [
            {
                userName: 'x x',
                headUrl: null,
            },
            {
                headUrl: 'https://w2-pro.kskwai.com/kcdn/cdn-kcdn112308/warmup/test/avatar.87c5d2403c59f1cc.png',
                userName: '自然醒',
            },
        ],
        iconUrl: null,
        buttonView: {
            linkType: null,
            linkText: '',
            linkSubText: null,
            linkUrl: null,
            icon: null,
            iconText: null,
            anchorId: 0,
            sudokuStartTime: 0,
        },
    },
    // 次数类任务
    {
        inpushType: 'SHAKE_NUMBER_TASK',
        title: '任务完成，向前冲次数+1',
        content: '已完成xxx任务',
        iconUrl: 'https://kcdn.staging.kuaishou.com/kc/files/a/summer2025-server/wishTravel/task/kuaiShouInpush.png',
        buttonTex: '',
    },
    // 现金类任务
    {
        inpushType: 'CASH_TASK',
        title: '任务完成，现金${+%s}',
        content: '已完成%s任务',
        iconUrl: 'https://d2-pro.a.kwimgs.com/kcdn/cdn-kcdn112348/inpushcishu.png',
        buttonTex: '',
    },
    // 金币类任务
    {
        inpushType: 'GOLD_COIN_TASK',
        title: '任务完成，金币${+%s}',
        content: '已完成%s任务',
        iconUrl: 'https://d2-pro.a.kwimgs.com/kcdn/cdn-kcdn112348/inpushcishu.png',
        buttonTex: '',
    },
    // 续签任务完成，后续逻辑：关闭续签弹窗
    // {
    //     inpushType: 'RESUME_SUCCESS',
    //     title: '续签任务完成',
    //     content: '已完成',
    //     iconUrl: 'https://kcdn.staging.kuaishou.com/kc/files/a/summer2025-server/wishTravel/task/kuaiShouInpush.png',
    //     buttonTex: '',
    // },
];

const getData = () => mockInpushList[Math.floor(Math.random() * mockInpushList.length)];

export default mockjs.mock({
    result: 1,
    data: {
        inpushList: [
            {
                inpushType: 'ASSIST',
                title: '1位好友为你助力',
                content: '邀好友，多邀多赚钱',
                assistedUserInfo: [
                    { userName: '快手用户1754401263942', headUrl: 'https://static.yximgs.com/s1/i/def/head_u.png' },
                ],
                iconUrl: null,
                buttonView: {
                    linkType: null,
                    linkText: '',
                    linkSubText: null,
                    linkUrl: null,
                    icon: null,
                    iconText: null,
                    anchorId: 0,
                    sudokuStartTime: 0,
                },
            },
        ],
        nextTimeMills: 3000,
        needRefreshPopup: true,
    },
    timestamp: 1754401276153,
    hostname: 'public-xm-c34-kce-node91.idchb1az1.hb1.kwaidc.com',
});

// 注释掉下面这行代码，则会禁用当前接口的 mock
// export const disable = true;
